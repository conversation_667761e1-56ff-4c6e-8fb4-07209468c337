@echo off
REM ============================================================================
REM PayslipSender Windows Service Installation Script
REM ============================================================================
REM This script installs PayslipSender as a Windows service using NSSM
REM Run as Administrator for proper service installation
REM ============================================================================

echo.
echo ========================================
echo PayslipSender Service Installation
echo ========================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script must be run as Administrator!
    echo Right-click and select "Run as administrator"
    echo.
    pause
    exit /b 1
)

REM Set variables
set SERVICE_NAME=DSCPayslipSender
set SERVICE_DISPLAY_NAME=DSC PayslipSender Service
set SERVICE_DESCRIPTION=Automated payslip email sender for DSC Human Resources
set CURRENT_DIR=%~dp0
set EXECUTABLE_PATH=%CURRENT_DIR%dist\PayslipSender.exe
set NSSM_PATH=%CURRENT_DIR%nssm.exe

echo Current Directory: %CURRENT_DIR%
echo Executable Path: %EXECUTABLE_PATH%
echo Service Name: %SERVICE_NAME%
echo.

REM Check if executable exists
if not exist "%EXECUTABLE_PATH%" (
    echo ERROR: PayslipSender.exe not found at: %EXECUTABLE_PATH%
    echo Please run 'python create_exe.py' first to create the executable
    echo.
    pause
    exit /b 1
)

REM Download NSSM if not present
if not exist "%NSSM_PATH%" (
    echo NSSM not found. Downloading NSSM...
    echo.
    
    REM Try to download NSSM using PowerShell
    powershell -Command "try { Invoke-WebRequest -Uri 'https://nssm.cc/release/nssm-2.24.zip' -OutFile 'nssm.zip'; Expand-Archive -Path 'nssm.zip' -DestinationPath '.'; Copy-Item '.\nssm-2.24\win64\nssm.exe' '.\nssm.exe'; Remove-Item 'nssm.zip'; Remove-Item 'nssm-2.24' -Recurse; Write-Host 'NSSM downloaded successfully' } catch { Write-Host 'Failed to download NSSM automatically' }"
    
    if not exist "%NSSM_PATH%" (
        echo.
        echo ERROR: Could not download NSSM automatically.
        echo Please download NSSM manually:
        echo 1. Go to https://nssm.cc/download
        echo 2. Download nssm-2.24.zip
        echo 3. Extract nssm.exe to this directory: %CURRENT_DIR%
        echo 4. Run this script again
        echo.
        pause
        exit /b 1
    )
)

echo NSSM found: %NSSM_PATH%
echo.

REM Stop and remove existing service if it exists
echo Checking for existing service...
sc query "%SERVICE_NAME%" >nul 2>&1
if %errorLevel% equ 0 (
    echo Existing service found. Stopping and removing...
    "%NSSM_PATH%" stop "%SERVICE_NAME%"
    timeout /t 3 /nobreak >nul
    "%NSSM_PATH%" remove "%SERVICE_NAME%" confirm
    timeout /t 2 /nobreak >nul
    echo Existing service removed.
    echo.
)

REM Install the service
echo Installing PayslipSender as Windows service...
"%NSSM_PATH%" install "%SERVICE_NAME%" "%EXECUTABLE_PATH%"

if %errorLevel% neq 0 (
    echo ERROR: Failed to install service
    pause
    exit /b 1
)

REM Configure service settings
echo Configuring service settings...

REM Set display name and description
"%NSSM_PATH%" set "%SERVICE_NAME%" DisplayName "%SERVICE_DISPLAY_NAME%"
"%NSSM_PATH%" set "%SERVICE_NAME%" Description "%SERVICE_DESCRIPTION%"

REM Set working directory
"%NSSM_PATH%" set "%SERVICE_NAME%" AppDirectory "%CURRENT_DIR%"

REM Set startup type to automatic
"%NSSM_PATH%" set "%SERVICE_NAME%" Start SERVICE_AUTO_START

REM Configure logging
"%NSSM_PATH%" set "%SERVICE_NAME%" AppStdout "%CURRENT_DIR%logs\service_output.log"
"%NSSM_PATH%" set "%SERVICE_NAME%" AppStderr "%CURRENT_DIR%logs\service_error.log"

REM Set service to restart on failure
"%NSSM_PATH%" set "%SERVICE_NAME%" AppExit Default Restart
"%NSSM_PATH%" set "%SERVICE_NAME%" AppRestartDelay 30000

REM Set service dependencies (optional)
"%NSSM_PATH%" set "%SERVICE_NAME%" DependOnService Tcpip

REM Create logs directory if it doesn't exist
if not exist "%CURRENT_DIR%logs" (
    mkdir "%CURRENT_DIR%logs"
    echo Created logs directory
)

echo.
echo ========================================
echo Service Installation Complete!
echo ========================================
echo.
echo Service Name: %SERVICE_NAME%
echo Display Name: %SERVICE_DISPLAY_NAME%
echo Executable: %EXECUTABLE_PATH%
echo Working Directory: %CURRENT_DIR%
echo.
echo Service Logs:
echo - Output: %CURRENT_DIR%logs\service_output.log
echo - Errors: %CURRENT_DIR%logs\service_error.log
echo - Application: %CURRENT_DIR%logs\payslip_sender.log
echo.

REM Ask if user wants to start the service now
set /p START_NOW="Do you want to start the service now? (Y/N): "
if /i "%START_NOW%"=="Y" (
    echo.
    echo Starting PayslipSender service...
    "%NSSM_PATH%" start "%SERVICE_NAME%"
    
    if %errorLevel% equ 0 (
        echo.
        echo ✅ Service started successfully!
        echo.
        echo You can manage the service using:
        echo - Services.msc (Windows Services Manager)
        echo - sc start "%SERVICE_NAME%" (Command line start)
        echo - sc stop "%SERVICE_NAME%" (Command line stop)
        echo - "%NSSM_PATH%" edit "%SERVICE_NAME%" (Edit service settings)
    ) else (
        echo.
        echo ❌ Failed to start service. Check the error logs:
        echo - %CURRENT_DIR%logs\service_error.log
        echo - %CURRENT_DIR%logs\payslip_sender.log
    )
) else (
    echo.
    echo Service installed but not started.
    echo To start the service later, run: sc start "%SERVICE_NAME%"
)

echo.
echo ========================================
echo Service Management Commands:
echo ========================================
echo Start service:    sc start "%SERVICE_NAME%"
echo Stop service:     sc stop "%SERVICE_NAME%"
echo Service status:   sc query "%SERVICE_NAME%"
echo Edit service:     "%NSSM_PATH%" edit "%SERVICE_NAME%"
echo Remove service:   "%NSSM_PATH%" remove "%SERVICE_NAME%" confirm
echo.
echo Service will automatically start on system boot.
echo Check logs in the 'logs' folder for troubleshooting.
echo.

pause
