@echo off
REM Create logs directory if it doesn't exist
if not exist "C:\Users\<USER>\Desktop\PayslipSender\logs" mkdir "C:\Users\<USER>\Desktop\PayslipSender\logs"

echo Starting PayslipSender Service... > "C:\Users\<USER>\Desktop\PayslipSender\logs\batch_log.txt"
echo %date% %time% >> "C:\Users\<USER>\Desktop\PayslipSender\logs\batch_log.txt"

REM Change to project directory
cd /d "C:\Users\<USER>\Desktop\PayslipSender"
echo Changed directory to: %cd% >> "C:\Users\<USER>\Desktop\PayslipSender\logs\batch_log.txt"

REM Check if Python exists
if not exist "C:\Python312\python.exe" (
    echo ERROR: Python not found at C:\Python312\python.exe >> "C:\Users\<USER>\Desktop\PayslipSender\logs\batch_log.txt"
    exit /b 1
)

REM Check if script exists
if not exist "payslip_sender.py" (
    echo ERROR: payslip_sender.py not found in current directory >> "C:\Users\<USER>\Desktop\PayslipSender\logs\batch_log.txt"
    exit /b 1
)

echo Running Python script... >> "C:\Users\<USER>\Desktop\PayslipSender\logs\batch_log.txt"
"C:\Python312\python.exe" "payslip_sender.py" >> "C:\Users\<USER>\Desktop\PayslipSender\logs\batch_log.txt" 2>&1

echo Service ended with exit code: %ERRORLEVEL% >> "C:\Users\<USER>\Desktop\PayslipSender\logs\batch_log.txt"
