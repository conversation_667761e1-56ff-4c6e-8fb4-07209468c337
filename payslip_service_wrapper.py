#!/usr/bin/env python3
"""
PayslipSender Service Wrapper
This lightweight wrapper starts quickly to avoid Error 1053,
then launches the main PayslipSender application.
"""

import os
import sys
import time
import subprocess
import threading
from pathlib import Path

class PayslipServiceWrapper:
    """Lightweight service wrapper that starts quickly."""
    
    def __init__(self):
        self.process = None
        self.running = False
        
    def start(self):
        """Start the PayslipSender service quickly."""
        try:
            print("🚀 PayslipSender Service Wrapper starting...")
            
            # Get the path to the main executable
            if getattr(sys, 'frozen', False):
                # Running as executable
                wrapper_path = Path(sys.executable).parent
                main_executable = wrapper_path / "PayslipSender.exe"
            else:
                # Running as script
                wrapper_path = Path(__file__).parent
                main_executable = wrapper_path / "payslip_sender.py"
            
            if not main_executable.exists():
                print(f"❌ Main executable not found: {main_executable}")
                return False
            
            # Start the main application in a separate process
            print(f"Starting main application: {main_executable}")
            
            if main_executable.suffix == '.py':
                # Running Python script
                self.process = subprocess.Popen([sys.executable, str(main_executable)])
            else:
                # Running executable
                self.process = subprocess.Popen([str(main_executable)])
            
            self.running = True
            
            # Monitor the process in a separate thread
            monitor_thread = threading.Thread(target=self._monitor_process)
            monitor_thread.daemon = True
            monitor_thread.start()
            
            print("✅ PayslipSender Service Wrapper started successfully")
            return True
            
        except Exception as e:
            print(f"❌ Failed to start service wrapper: {e}")
            return False
    
    def _monitor_process(self):
        """Monitor the main process and restart if it crashes."""
        while self.running:
            if self.process:
                # Check if process is still running
                if self.process.poll() is not None:
                    if self.running:  # Only restart if we're supposed to be running
                        print("⚠️ Main process stopped unexpectedly, restarting...")
                        time.sleep(5)  # Wait before restarting
                        self.start()  # Restart the process
                    break
            
            time.sleep(10)  # Check every 10 seconds
    
    def stop(self):
        """Stop the PayslipSender service."""
        try:
            print("🛑 PayslipSender Service Wrapper stopping...")
            
            self.running = False
            
            if self.process:
                # Try to terminate gracefully
                self.process.terminate()
                
                # Wait up to 10 seconds for graceful shutdown
                try:
                    self.process.wait(timeout=10)
                except subprocess.TimeoutExpired:
                    # Force kill if it doesn't stop gracefully
                    print("⚠️ Force killing main process...")
                    self.process.kill()
                    self.process.wait()
            
            print("✅ PayslipSender Service Wrapper stopped")
            return True
            
        except Exception as e:
            print(f"❌ Error stopping service wrapper: {e}")
            return False
    
    def run(self):
        """Run the service wrapper."""
        if self.start():
            try:
                # Keep the wrapper running
                while self.running:
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\n🛑 Service wrapper interrupted")
            finally:
                self.stop()

def main():
    """Main entry point for the service wrapper."""
    print("PayslipSender Service Wrapper")
    print("=" * 40)
    
    wrapper = PayslipServiceWrapper()
    
    # Handle command line arguments
    if len(sys.argv) > 1:
        if sys.argv[1] == "--start":
            wrapper.start()
            # Keep running
            try:
                while wrapper.running:
                    time.sleep(1)
            except KeyboardInterrupt:
                wrapper.stop()
        elif sys.argv[1] == "--stop":
            wrapper.stop()
        elif sys.argv[1] == "--help":
            print("Usage:")
            print("  python payslip_service_wrapper.py         - Run interactively")
            print("  python payslip_service_wrapper.py --start - Start and keep running")
            print("  python payslip_service_wrapper.py --stop  - Stop the service")
        else:
            print("Unknown argument. Use --help for usage information.")
    else:
        # Run interactively
        wrapper.run()

if __name__ == "__main__":
    main()
