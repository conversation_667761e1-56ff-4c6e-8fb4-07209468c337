#!/usr/bin/env python3
"""
Demonstration: Invalid files are moved to archived directory
"""

import os
import sys
import tempfile
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def demonstrate_invalid_file_handling():
    """Demonstrate that invalid files are moved to archived directory."""
    print("🎯 DEMONSTRATION: Invalid Files Moved to Archived Directory")
    print("=" * 60)
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        watch_dir = temp_path / "payslips"
        archived_dir = temp_path / "archived_payslips"
        
        watch_dir.mkdir()
        archived_dir.mkdir()
        
        print(f"📁 Watch Directory: {watch_dir}")
        print(f"📁 Archived Directory: {archived_dir}")
        
        # Create various types of invalid files
        invalid_files = {
            "invalid_name.pdf": "Invalid filename format",
            "empty_file.pdf": "Empty file (0 bytes)",
            "wrong_format_123.pdf": "Wrong filename pattern",
            "not_a_pdf.pdf": "Not a real PDF file",
            "Payslip_EMP001_baddate.pdf": "Invalid date format"
        }
        
        print(f"\n📄 Creating {len(invalid_files)} invalid files...")
        
        for filename, description in invalid_files.items():
            file_path = watch_dir / filename
            
            if filename == "empty_file.pdf":
                file_path.touch()  # Create empty file
            elif filename == "not_a_pdf.pdf":
                with open(file_path, 'w') as f:
                    f.write("This is not a PDF file")
            else:
                with open(file_path, 'wb') as f:
                    f.write(b'%PDF-1.4\nTest content')
            
            print(f"   ✅ {filename} - {description}")
        
        print(f"\n📊 BEFORE Processing:")
        watch_files = list(watch_dir.glob("*.pdf"))
        archived_files = list(archived_dir.glob("*"))
        print(f"   Files in watch directory: {len(watch_files)}")
        print(f"   Files in archived directory: {len(archived_files)}")
        
        # Set environment variables
        os.environ['WATCH_DIRECTORY'] = str(watch_dir)
        os.environ['ARCHIVED_DIRECTORY'] = str(archived_dir)
        os.environ['LOG_LEVEL'] = 'ERROR'  # Reduce log noise
        os.environ['LOG_TO_CONSOLE'] = 'false'
        
        try:
            # Import and create PayslipSender
            from payslip_sender import PayslipSender
            sender = PayslipSender()
            
            print(f"\n🔄 PROCESSING Files...")
            print("   Running scan_directory() to process invalid files...")
            
            # Process the files
            sender.scan_directory()
            
            print(f"\n📊 AFTER Processing:")
            watch_files_after = list(watch_dir.glob("*.pdf"))
            archived_files_after = list(archived_dir.glob("*"))
            failed_pdfs = [f for f in archived_files_after if f.name.startswith("FAILED_") and f.suffix == ".pdf"]
            info_files = [f for f in archived_files_after if f.name.startswith("FAILED_") and f.suffix == ".txt"]
            
            print(f"   Files in watch directory: {len(watch_files_after)}")
            print(f"   Files in archived directory: {len(archived_files_after)}")
            print(f"   Failed PDF files: {len(failed_pdfs)}")
            print(f"   Info files: {len(info_files)}")
            
            print(f"\n📄 ARCHIVED FILES:")
            for failed_pdf in failed_pdfs:
                print(f"   📄 {failed_pdf.name}")
            
            print(f"\n📄 INFO FILES:")
            for info_file in info_files:
                print(f"   📄 {info_file.name}")
                
                # Show content of one info file as example
                if "invalid_name" in info_file.name:
                    print(f"      📋 Content preview:")
                    try:
                        with open(info_file, 'r', encoding='utf-8') as f:
                            content = f.read()
                            for line in content.split('\n')[:6]:  # Show first 6 lines
                                if line.strip():
                                    print(f"         {line}")
                    except Exception as e:
                        print(f"         Error reading file: {e}")
            
            # Verify success
            if len(watch_files_after) == 0 and len(failed_pdfs) == len(invalid_files):
                print(f"\n🎉 SUCCESS!")
                print(f"   ✅ All {len(invalid_files)} invalid files moved to archived directory")
                print(f"   ✅ All files have FAILED_ prefix with timestamp")
                print(f"   ✅ Info files created with failure details")
                print(f"   ✅ Watch directory is now clean")
                
                print(f"\n💡 KEY POINTS:")
                print(f"   • Invalid files are NOT left in watch directory")
                print(f"   • They are moved to archived_payslips/ with FAILED_ prefix")
                print(f"   • Each failed file gets an accompanying _info.txt file")
                print(f"   • This prevents files from being stuck or lost")
                
                return True
            else:
                print(f"\n❌ UNEXPECTED RESULT!")
                print(f"   Expected: 0 files in watch, {len(invalid_files)} in archived")
                print(f"   Actual: {len(watch_files_after)} files in watch, {len(failed_pdfs)} in archived")
                return False
                
        except Exception as e:
            print(f"\n❌ ERROR: {e}")
            import traceback
            traceback.print_exc()
            return False
        finally:
            try:
                sender.singleton.release()
            except:
                pass

if __name__ == "__main__":
    result = demonstrate_invalid_file_handling()
    
    print("\n" + "=" * 60)
    if result:
        print("✅ DEMONSTRATION COMPLETE!")
        print("\nThe PayslipSender system correctly handles invalid files by:")
        print("1. Detecting validation errors (filename, format, size, etc.)")
        print("2. Moving failed files to archived_payslips/ directory")
        print("3. Adding FAILED_ prefix with timestamp for identification")
        print("4. Creating detailed info files with failure reasons")
        print("5. Ensuring no files are abandoned in the watch directory")
        print("\n🎯 CONCLUSION: Invalid files ARE moved to archived directory!")
    else:
        print("❌ DEMONSTRATION FAILED!")
        print("There may be an issue with the implementation.")