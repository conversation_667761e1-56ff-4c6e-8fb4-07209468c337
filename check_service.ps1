# ============================================================================
# Check PayslipSender Service Status (PowerShell)
# ============================================================================

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "PayslipSender Service Status Check" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

$serviceName = "DSCPayslipSender"

# Check if service exists
$service = Get-Service -Name $serviceName -ErrorAction SilentlyContinue

if ($service) {
    Write-Host "✅ Service FOUND: $serviceName" -ForegroundColor Green
    Write-Host ""
    
    Write-Host "========================================" -ForegroundColor Yellow
    Write-Host "Service Details:" -ForegroundColor Yellow
    Write-Host "========================================" -ForegroundColor Yellow
    
    Write-Host "Name: " -NoNewline
    Write-Host $service.Name -ForegroundColor White
    
    Write-Host "Display Name: " -NoNewline
    Write-Host $service.DisplayName -ForegroundColor White
    
    Write-Host "Status: " -NoNewline
    if ($service.Status -eq "Running") {
        Write-Host $service.Status -ForegroundColor Green
    } elseif ($service.Status -eq "Stopped") {
        Write-Host $service.Status -ForegroundColor Red
    } else {
        Write-Host $service.Status -ForegroundColor Yellow
    }
    
    Write-Host "Start Type: " -NoNewline
    Write-Host $service.StartType -ForegroundColor White
    
    Write-Host ""
    
    # Get additional service information
    $serviceConfig = Get-WmiObject -Class Win32_Service -Filter "Name='$serviceName'"
    if ($serviceConfig) {
        Write-Host "========================================" -ForegroundColor Yellow
        Write-Host "Additional Information:" -ForegroundColor Yellow
        Write-Host "========================================" -ForegroundColor Yellow
        
        Write-Host "Executable Path: " -NoNewline
        Write-Host $serviceConfig.PathName -ForegroundColor White
        
        Write-Host "Service Account: " -NoNewline
        Write-Host $serviceConfig.StartName -ForegroundColor White
        
        Write-Host "Description: " -NoNewline
        Write-Host $serviceConfig.Description -ForegroundColor White
        
        Write-Host "Process ID: " -NoNewline
        if ($serviceConfig.ProcessId -gt 0) {
            Write-Host $serviceConfig.ProcessId -ForegroundColor Green
        } else {
            Write-Host "Not Running" -ForegroundColor Red
        }
    }
    
} else {
    Write-Host "❌ Service NOT FOUND: $serviceName" -ForegroundColor Red
    Write-Host ""
    Write-Host "The PayslipSender service is not registered in Windows Services." -ForegroundColor Yellow
    Write-Host ""
    Write-Host "To register the service, run: register_service.bat (as Administrator)" -ForegroundColor Cyan
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "All Services containing 'Payslip':" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

$payslipServices = Get-Service | Where-Object { $_.Name -like "*payslip*" -or $_.DisplayName -like "*payslip*" }
if ($payslipServices) {
    $payslipServices | Format-Table Name, DisplayName, Status -AutoSize
} else {
    Write-Host "No services found containing 'Payslip'" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "All Services containing 'DSC':" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

$dscServices = Get-Service | Where-Object { $_.Name -like "*dsc*" -or $_.DisplayName -like "*dsc*" }
if ($dscServices) {
    $dscServices | Format-Table Name, DisplayName, Status -AutoSize
} else {
    Write-Host "No services found containing 'DSC'" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Press any key to continue..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
