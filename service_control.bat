@echo off
REM ============================================================================
REM PayslipSender Service Control (Simple Management)
REM ============================================================================
REM Quick service management without NSSM
REM Run as Administrator
REM ============================================================================

echo.
echo ========================================
echo PayslipSender Service Control
echo ========================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script must be run as Administrator!
    echo Right-click and select "Run as administrator"
    echo.
    pause
    exit /b 1
)

set SERVICE_NAME=DSCPayslipSender

:MENU
cls
echo.
echo ========================================
echo PayslipSender Service Control
echo ========================================
echo.
echo Service Name: %SERVICE_NAME%
echo.

REM Check service status
sc query "%SERVICE_NAME%" >nul 2>&1
if %errorLevel% equ 0 (
    for /f "tokens=3" %%i in ('sc query "%SERVICE_NAME%" ^| find "STATE"') do set SERVICE_STATE=%%i
    echo Current Status: !SERVICE_STATE!
) else (
    echo Current Status: NOT REGISTERED
    set SERVICE_STATE=NOT_REGISTERED
)

echo.
echo Available Commands:
echo.
echo 1. Start Service
echo 2. Stop Service  
echo 3. Restart Service
echo 4. View Service Status
echo 5. View Application Logs
echo 6. Register Service (if not registered)
echo 7. Unregister Service
echo 0. Exit
echo.

set /p CHOICE="Select option (0-7): "

if "%CHOICE%"=="1" goto START
if "%CHOICE%"=="2" goto STOP
if "%CHOICE%"=="3" goto RESTART
if "%CHOICE%"=="4" goto STATUS
if "%CHOICE%"=="5" goto LOGS
if "%CHOICE%"=="6" goto REGISTER
if "%CHOICE%"=="7" goto UNREGISTER
if "%CHOICE%"=="0" goto EXIT

echo Invalid choice. Try again.
timeout /t 2 /nobreak >nul
goto MENU

:START
echo.
echo Starting PayslipSender service...
sc start "%SERVICE_NAME%"
echo.
pause
goto MENU

:STOP
echo.
echo Stopping PayslipSender service...
sc stop "%SERVICE_NAME%"
echo.
pause
goto MENU

:RESTART
echo.
echo Restarting PayslipSender service...
echo Stopping...
sc stop "%SERVICE_NAME%"
timeout /t 3 /nobreak >nul
echo Starting...
sc start "%SERVICE_NAME%"
echo.
pause
goto MENU

:STATUS
echo.
echo ========================================
echo Service Status Details
echo ========================================
sc query "%SERVICE_NAME%"
echo.
echo ========================================
echo Service Configuration
echo ========================================
sc qc "%SERVICE_NAME%"
echo.
pause
goto MENU

:LOGS
echo.
echo ========================================
echo PayslipSender Application Logs
echo ========================================
echo.

if exist "logs\payslip_sender.log" (
    echo Last 20 lines of application log:
    echo ----------------------------------------
    powershell "Get-Content 'logs\payslip_sender.log' -Tail 20"
    echo.
    echo Full log location: logs\payslip_sender.log
) else (
    echo Application log not found: logs\payslip_sender.log
    echo The service may not have started yet or logs directory doesn't exist.
)

echo.
echo Press any key to return to menu...
pause >nul
goto MENU

:REGISTER
echo.
echo Launching service registration...
call register_service.bat
echo.
echo Press any key to return to menu...
pause >nul
goto MENU

:UNREGISTER
echo.
echo Launching service unregistration...
call unregister_service.bat
echo.
echo Press any key to return to menu...
pause >nul
goto MENU

:EXIT
echo.
echo Goodbye!
exit /b 0
