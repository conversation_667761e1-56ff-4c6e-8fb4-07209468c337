#!/usr/bin/env python3
"""
PayslipSender - Production HR Department Payslip Distribution System

Automatically monitors a directory for payslip PDF files and emails them to employees
using data from the existing HR database. Designed for deployment as a Windows service.

Configuration is managed entirely through the .env file.
"""

import os
import time
import logging
import pyodbc
import signal
import sys
import threading
import queue
from pathlib import Path
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor
from dotenv import load_dotenv
from email_service import EmailService

def setup_working_directory():
    """Ensure the working directory is set correctly for both script and executable."""
    if getattr(sys, 'frozen', False):
        # Running as executable
        application_path = os.path.dirname(sys.executable)
    else:
        # Running as script
        application_path = os.path.dirname(os.path.abspath(__file__))
    
    os.chdir(application_path)
    return application_path

class PayslipSender:
    """Production payslip sender for HR department deployment."""

    def __init__(self):
        """Initialize the payslip sender."""
        # Ensure correct working directory
        self.application_path = setup_working_directory()
        
        # Load environment variables
        load_dotenv()

        # Setup logging from environment
        self._setup_logging()

        # Database configuration
        self.db_config = {
            'server': os.getenv('DATABASE_SERVER', 'localhost'),
            'port': os.getenv('DATABASE_PORT', '1433'),
            'database': os.getenv('DATABASE_NAME', ''),
            'username': os.getenv('DATABASE_USERNAME', ''),
            'password': os.getenv('DATABASE_PASSWORD', ''),
            'trusted_connection': os.getenv('DATABASE_TRUSTED_CONNECTION', 'no').lower() == 'yes',
            'employee_table': os.getenv('EMPLOYEE_TABLE_NAME', 'Employees'),
            'employee_id_col': os.getenv('EMPLOYEE_ID_COLUMN', 'EmployeeId'),
            'employee_name_col': os.getenv('EMPLOYEE_NAME_COLUMN', 'Name'),
            'employee_email_col': os.getenv('EMPLOYEE_EMAIL_COLUMN', 'Email')
        }
        
        # Email configuration
        self.email_service = EmailService(
            smtp_server=os.getenv('SMTP_SERVER', 'smtp.gmail.com'),
            smtp_port=int(os.getenv('SMTP_PORT', '587')),
            username=os.getenv('SMTP_USERNAME', ''),
            password=os.getenv('SMTP_PASSWORD', ''),
            use_tls=os.getenv('SMTP_USE_TLS', 'True').lower() == 'true'
        )
        
        # File paths
        self.watch_directory = Path(os.getenv('WATCH_DIRECTORY', './payslips'))
        self.archived_directory = Path(os.getenv('ARCHIVED_DIRECTORY', './archived_payslips'))
        
        # Create directories
        self.watch_directory.mkdir(parents=True, exist_ok=True)
        self.archived_directory.mkdir(parents=True, exist_ok=True)
        Path('logs').mkdir(exist_ok=True)
        
        # Track processed files and service state
        self.processed_files = set()
        self.running = False

        # Setup file processing queue and worker threads
        self.file_queue = queue.Queue()
        self.max_workers = int(os.getenv('MAX_WORKER_THREADS', '3'))
        self.workers = []
        self.processing_lock = threading.Lock()

        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

        self.logger.info("PayslipSender initialized for production deployment")
        self.logger.info(f"Working directory: {self.application_path}")
        self.logger.info(f"Watch directory: {self.watch_directory}")
        self.logger.info(f"Archived directory: {self.archived_directory}")

    def _setup_logging(self):
        """Setup logging configuration from environment variables."""
        log_level = os.getenv('LOG_LEVEL', 'INFO').upper()
        log_file = os.getenv('LOG_FILE', 'logs/payslip_sender.log')

        # Ensure logs directory exists
        Path(log_file).parent.mkdir(parents=True, exist_ok=True)

        # Configure logging
        logging.basicConfig(
            level=getattr(logging, log_level),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler() if os.getenv('LOG_TO_CONSOLE', 'true').lower() == 'true' else logging.NullHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def _signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully."""
        self.logger.info(f"Received signal {signum}, initiating graceful shutdown...")
        self.running = False

        # Stop all worker threads
        for _ in range(self.max_workers):
            self.file_queue.put(None)  # Poison pill to stop workers

    def _start_worker_threads(self):
        """Start worker threads for processing files."""
        self.logger.info(f"Starting {self.max_workers} worker threads for file processing")

        for i in range(self.max_workers):
            worker = threading.Thread(target=self._worker_thread, name=f"PayslipWorker-{i+1}")
            worker.daemon = True
            worker.start()
            self.workers.append(worker)

    def _worker_thread(self):
        """Worker thread that processes files from the queue."""
        thread_name = threading.current_thread().name
        self.logger.info(f"{thread_name} started")

        while self.running:
            try:
                # Get file from queue (timeout to check if we should stop)
                file_info = self.file_queue.get(timeout=1)

                # Check for poison pill (shutdown signal)
                if file_info is None:
                    break

                # Process the file
                file_path, employee_id, date_str = file_info
                self.logger.info(f"{thread_name} processing: {file_path.name}")

                try:
                    self._process_single_file(file_path, employee_id, date_str)
                    self.logger.info(f"{thread_name} completed: {file_path.name}")
                except Exception as e:
                    self.logger.error(f"{thread_name} failed to process {file_path.name}: {e}")
                finally:
                    self.file_queue.task_done()

            except queue.Empty:
                # Timeout occurred, continue loop to check if we should stop
                continue
            except Exception as e:
                self.logger.error(f"{thread_name} error: {e}")

        self.logger.info(f"{thread_name} stopped")

    def get_db_connection(self):
        """Get database connection with automatic ODBC driver detection."""
        # Get ODBC driver from environment or auto-detect
        odbc_driver = os.getenv('ODBC_DRIVER', '')
        if not odbc_driver:
            odbc_driver = self._detect_odbc_driver()

        if self.db_config['trusted_connection']:
            conn_str = f"DRIVER={{{odbc_driver}}};SERVER={self.db_config['server']},{self.db_config['port']};DATABASE={self.db_config['database']};Trusted_Connection=yes;TrustServerCertificate=yes;"
        else:
            conn_str = f"DRIVER={{{odbc_driver}}};SERVER={self.db_config['server']},{self.db_config['port']};DATABASE={self.db_config['database']};UID={self.db_config['username']};PWD={self.db_config['password']};TrustServerCertificate=yes;"

        return pyodbc.connect(conn_str)

    def _detect_odbc_driver(self):
        """Automatically detect the best available ODBC driver for SQL Server."""
        # List of SQL Server ODBC drivers in order of preference (newest first)
        preferred_drivers = [
            "ODBC Driver 18 for SQL Server",
            "ODBC Driver 17 for SQL Server",
            "ODBC Driver 13 for SQL Server",
            "ODBC Driver 11 for SQL Server",
            "SQL Server Native Client 11.0",
            "SQL Server Native Client 10.0",
            "SQL Server"
        ]

        available_drivers = pyodbc.drivers()

        for driver in preferred_drivers:
            if driver in available_drivers:
                self.logger.info(f"Using ODBC driver: {driver}")
                return driver

        # Fallback - use the first available SQL Server driver
        for driver in available_drivers:
            if "SQL Server" in driver:
                self.logger.warning(f"Using fallback ODBC driver: {driver}")
                return driver

        # No SQL Server driver found
        raise Exception("No SQL Server ODBC driver found. Please install SQL Server ODBC driver.")
    
    def get_employee_by_id(self, employee_id: str):
        """Get employee information from database."""
        try:
            with self.get_db_connection() as conn:
                cursor = conn.cursor()
                query = f"""
                    SELECT {self.db_config['employee_id_col']}, 
                           {self.db_config['employee_name_col']}, 
                           {self.db_config['employee_email_col']} 
                    FROM {self.db_config['employee_table']} 
                    WHERE {self.db_config['employee_id_col']} = ?
                """
                cursor.execute(query, (employee_id,))
                row = cursor.fetchone()
                
                if row:
                    return {
                        'EmployeeId': row[0],
                        'Name': row[1],
                        'Email': row[2]
                    }
                return None
        except Exception as e:
            self.logger.error(f"Database error getting employee {employee_id}: {e}")
            return None

    def log_failed_send(self, employee_id: str, reason: str):
        """Log failed send attempt."""
        try:
            with self.get_db_connection() as conn:
                cursor = conn.cursor()

                # Create failed logs table if it doesn't exist
                cursor.execute("""
                    IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='PayslipFailedLogs' AND xtype='U')
                    CREATE TABLE PayslipFailedLogs (
                        id INT IDENTITY(1,1) PRIMARY KEY,
                        EmployeeId NVARCHAR(50) NOT NULL,
                        PayslipDate NVARCHAR(50) NOT NULL,
                        Reason NVARCHAR(MAX) NOT NULL,
                        created_at DATETIME DEFAULT GETDATE()
                    )
                """)

                # Insert failed log
                cursor.execute("""
                    INSERT INTO PayslipFailedLogs (EmployeeId, PayslipDate, Reason)
                    VALUES (?, ?, ?)
                """, (employee_id, datetime.now().strftime("%Y-%m-%d"), reason))

                conn.commit()
                self.logger.warning(f"Logged failed send for employee {employee_id}: {reason}")
        except Exception as e:
            self.logger.error(f"Error logging failed send: {e}")
    
    def parse_filename(self, filename: str):
        """Parse payslip filename to extract employee ID and date."""
        import re
        
        # Expected format: Payslip_EmployeeID_YYYYMMDD.pdf
        match = re.match(r'^Payslip_(\w+)_(\d{8})\.pdf$', filename, re.IGNORECASE)
        if match:
            return match.group(1), match.group(2)
        return None, None
    
    def move_to_archived(self, file_path: Path):
        """Move processed file to archived directory."""
        try:
            destination = self.archived_directory / file_path.name
            
            # Add timestamp if file already exists
            if destination.exists():
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                stem = destination.stem
                suffix = destination.suffix
                destination = self.archived_directory / f"{stem}_{timestamp}{suffix}"
            
            file_path.rename(destination)
            self.logger.info(f"Moved processed file to: {destination}")
        except Exception as e:
            self.logger.error(f"Error moving file to archived directory: {e}")

    def process_payslip_file(self, file_path: Path, employee_id: str, date_str: str):
        """Process a single payslip file."""
        self.logger.info(f"Processing payslip for employee {employee_id}, date {date_str}")

        try:
            # Get employee information
            employee = self.get_employee_by_id(employee_id)

            if not employee:
                error_msg = f"Employee ID {employee_id} not found in database"
                self.logger.error(error_msg)
                self.log_failed_send(employee_id, error_msg)
                return

            # Send email
            success = self.email_service.send_email(
                to_email=employee['Email'],
                employee_name=employee['Name'],
                payslip_date_str=date_str,
                pdf_path=str(file_path)
            )

            if success:
                self.logger.info(f"Successfully sent payslip to {employee['Name']} ({employee['Email']})")
                self.move_to_archived(file_path)
            else:
                error_msg = "Failed to send email"
                self.logger.error(f"Failed to send payslip to {employee['Name']}: {error_msg}")
                self.log_failed_send(employee_id, error_msg)

        except Exception as e:
            error_msg = f"Error processing payslip: {str(e)}"
            self.logger.error(error_msg)
            self.log_failed_send(employee_id, error_msg)
    
    def scan_directory(self):
        """Scan watch directory for new payslip files and queue them for processing."""
        try:
            # Get all PDF files in the directory
            pdf_files = list(self.watch_directory.glob('*.pdf'))

            # Filter out already processed files
            with self.processing_lock:
                new_files = [f for f in pdf_files if str(f) not in self.processed_files]

            if new_files:
                self.logger.info(f"Found {len(new_files)} new payslip files to process")

            # Queue each new file for processing by worker threads
            for pdf_file in new_files:
                file_path = str(pdf_file)

                try:
                    # Parse filename
                    employee_id, date_str = self.parse_filename(pdf_file.name)

                    if employee_id and date_str:
                        self.logger.info(f"Queuing file for processing: {pdf_file.name}")
                        # Add to queue for worker threads
                        self.file_queue.put((pdf_file, employee_id, date_str))
                        # Mark as processed to avoid re-queuing
                        with self.processing_lock:
                            self.processed_files.add(file_path)
                    else:
                        self.logger.warning(f"Invalid filename format: {pdf_file.name}")
                        with self.processing_lock:
                            self.processed_files.add(file_path)  # Mark as processed to avoid repeated warnings

                except Exception as e:
                    self.logger.error(f"Error queuing file {pdf_file.name}: {e}")
                    with self.processing_lock:
                        self.processed_files.add(file_path)  # Mark as processed to avoid repeated errors

        except Exception as e:
            self.logger.error(f"Error scanning directory: {e}")
    
    def _process_single_file(self, file_path, employee_id, date_str):
        """Process a single payslip file in a worker thread."""
        try:
            # Process the file
            self.process_payslip_file(file_path, employee_id, date_str)
        except Exception as e:
            self.logger.error(f"Error processing file {file_path.name}: {e}")
            raise

    def run(self):
        """Run the payslip sender service with parallel processing."""
        self.logger.info("Starting PayslipSender service with parallel processing...")

        # Validate configuration and connections
        if not self._validate_startup():
            return False

        self.logger.info(f"Monitoring directory: {self.watch_directory}")
        self.logger.info(f"Using {self.max_workers} worker threads for parallel processing")
        self.logger.info("PayslipSender service is running")

        self.running = True
        scan_interval = int(os.getenv('SCAN_INTERVAL_SECONDS', '1'))  # Faster scanning

        # Start worker threads
        self._start_worker_threads()

        try:
            # Initial scan to process any existing files
            self.scan_directory()

            # Main monitoring loop
            while self.running:
                self.scan_directory()
                time.sleep(scan_interval)
        except Exception as e:
            self.logger.error(f"Unexpected error in main loop: {e}")
            return False
        finally:
            # Clean shutdown
            self.running = False

            # Wait for queue to empty
            self.logger.info("Waiting for processing queue to complete...")
            self.file_queue.join()

            # Stop worker threads
            for _ in range(self.max_workers):
                self.file_queue.put(None)  # Poison pill

            # Wait for workers to finish
            for worker in self.workers:
                worker.join(timeout=2)

            self.logger.info("PayslipSender service stopped")

        return True

    def _validate_startup(self):
        """Validate email and database connections on startup."""
        # Test email connection
        if not self.email_service.test_connection():
            self.logger.error("Email service connection failed - check SMTP configuration")
            return False

        # Test database connection with a simple query
        try:
            with self.get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT 1")
                cursor.fetchone()
            self.logger.info("Database connection validated")
        except Exception as e:
            self.logger.error(f"Database connection failed: {e}")
            return False

        return True

def main():
    """Main entry point for the PayslipSender service."""
    try:
        sender = PayslipSender()
        success = sender.run()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"Fatal error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
