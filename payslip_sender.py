#!/usr/bin/env python3
"""
PayslipSender - Production HR Department Payslip Distribution System

Automatically monitors a directory for payslip PDF files and emails them to employees
using data from the existing HR database. Designed for deployment as a Windows service.

Configuration is managed entirely through the .env file.
"""

import os
import time
import logging
import pyodbc
import signal
import sys
import threading
import queue
import uuid
import hashlib
import atexit
import socket
import tempfile
import subprocess
from pathlib import Path
from datetime import datetime
from dotenv import load_dotenv
from email_service import EmailService

# Windows service support
try:
    import win32serviceutil
    import win32service
    import win32event
    import servicemanager
    WINDOWS_SERVICE_AVAILABLE = True
except ImportError:
    WINDOWS_SERVICE_AVAILABLE = False

def setup_working_directory():
    """Ensure the working directory is set correctly for both script and executable."""
    if getattr(sys, 'frozen', False):
        # Running as executable
        application_path = os.path.dirname(sys.executable)
    else:
        # Running as script
        application_path = os.path.dirname(os.path.abspath(__file__))

    os.chdir(application_path)
    return application_path

class BulletproofSingleton:
    """Bulletproof singleton implementation using multiple protection layers."""

    def __init__(self, app_name="PayslipSender"):
        self.app_name = app_name
        self.lock_file = None
        self.socket_lock = None
        self.is_locked = False
        self.lock_file_path = Path(f"{app_name.lower()}_singleton.lock")
        self.socket_port = 19847  # Unique port for this application

    def acquire(self):
        """Acquire singleton lock using multiple protection layers."""
        print(f"🔒 Attempting to acquire {self.app_name} singleton lock...")

        # Layer 1: Socket-based lock (most reliable)
        if not self._acquire_socket_lock():
            return False

        # Layer 2: File-based lock with PID validation
        if not self._acquire_file_lock():
            self._release_socket_lock()
            return False

        # Layer 3: Process validation
        if not self._validate_no_other_instances():
            self._release_file_lock()
            self._release_socket_lock()
            return False

        self.is_locked = True
        atexit.register(self.release)

        print(f"✅ {self.app_name} singleton lock acquired successfully!")
        print(f"   - Socket lock on port {self.socket_port}")
        print(f"   - File lock: {self.lock_file_path}")
        print(f"   - Process ID: {os.getpid()}")

        return True

    def _acquire_socket_lock(self):
        """Acquire socket-based lock (most reliable method)."""
        try:
            self.socket_lock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket_lock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.socket_lock.bind(('127.0.0.1', self.socket_port))
            self.socket_lock.listen(1)
            print(f"🔒 Socket lock acquired on port {self.socket_port}")
            return True
        except OSError as e:
            print(f"❌ Socket lock failed - another {self.app_name} instance is running!")
            print(f"   Error: {e}")
            if self.socket_lock:
                try:
                    self.socket_lock.close()
                except:
                    pass
                self.socket_lock = None
            return False

    def _acquire_file_lock(self):
        """Acquire file-based lock with PID validation."""
        try:
            # Check existing lock file
            if self.lock_file_path.exists():
                try:
                    with open(self.lock_file_path, 'r') as f:
                        content = f.read().strip()
                        if content:
                            lines = content.split('\n')
                            for line in lines:
                                if line.startswith('PID:'):
                                    old_pid = int(line.split(':')[1].strip())
                                    if self._is_process_running(old_pid):
                                        print(f"❌ File lock failed - process {old_pid} is still running!")
                                        return False
                                    else:
                                        print(f"🧹 Removing stale lock file (PID {old_pid} not running)")
                                        self.lock_file_path.unlink()
                                        break
                except Exception as e:
                    print(f"⚠️ Error reading lock file: {e}")
                    try:
                        self.lock_file_path.unlink()
                    except:
                        pass

            # Create new lock file
            current_pid = os.getpid()
            with open(self.lock_file_path, 'w') as f:
                f.write(f"Application: {self.app_name}\n")
                f.write(f"PID: {current_pid}\n")
                f.write(f"Started: {datetime.now().isoformat()}\n")
                f.write(f"Socket Port: {self.socket_port}\n")
                f.write(f"Lock File: {self.lock_file_path}\n")

            print(f"🔒 File lock created: {self.lock_file_path}")
            return True

        except Exception as e:
            print(f"❌ File lock failed: {e}")
            return False

    def _validate_no_other_instances(self):
        """Additional validation to ensure no other instances are running."""
        try:
            # Check for processes with similar names
            import subprocess
            result = subprocess.run(['tasklist', '/FI', f'IMAGENAME eq PayslipSender*'],
                                  capture_output=True, text=True, shell=True)

            if result.returncode == 0 and 'PayslipSender' in result.stdout:
                lines = [line.strip() for line in result.stdout.split('\n') if 'PayslipSender' in line]
                current_pid = os.getpid()

                other_instances = []
                for line in lines:
                    parts = line.split()
                    if len(parts) >= 2:
                        try:
                            pid = int(parts[1])
                            if pid != current_pid:
                                other_instances.append(pid)
                        except ValueError:
                            continue

                if other_instances:
                    print(f"❌ Found other {self.app_name} instances running: {other_instances}")
                    return False

            print(f"✅ No other {self.app_name} instances detected")
            return True

        except Exception as e:
            print(f"⚠️ Process validation warning: {e}")
            return True  # Don't fail on validation errors

    def _is_process_running(self, pid):
        """Check if process is running."""
        try:
            os.kill(pid, 0)
            return True
        except OSError:
            return False

    def _release_socket_lock(self):
        """Release socket lock."""
        if self.socket_lock:
            try:
                self.socket_lock.close()
                print(f"🔓 Socket lock released (port {self.socket_port})")
            except:
                pass
            self.socket_lock = None

    def _release_file_lock(self):
        """Release file lock."""
        try:
            if self.lock_file_path.exists():
                self.lock_file_path.unlink()
                print(f"🔓 File lock released: {self.lock_file_path}")
        except Exception as e:
            print(f"⚠️ Error releasing file lock: {e}")

    def release(self):
        """Release all locks."""
        if self.is_locked:
            print(f"🔓 Releasing {self.app_name} singleton locks...")
            self._release_socket_lock()
            self._release_file_lock()
            self.is_locked = False
            print(f"✅ {self.app_name} singleton locks released")



class PayslipSender:
    """Production payslip sender for HR department deployment."""

    def __init__(self):
        """Initialize the payslip sender."""
        # CRITICAL: Bulletproof singleton protection
        self.singleton = BulletproofSingleton("PayslipSender")
        if not self.singleton.acquire():
            print("❌ CRITICAL ERROR: Another PayslipSender instance is already running!")
            print("❌ Multiple instances cause duplicate email sending!")
            print("❌ Please ensure only one instance runs at a time.")
            print("❌ If you're sure no other instance is running:")
            print("   1. Check Task Manager for PayslipSender processes")
            print("   2. Delete payslipsender_singleton.lock file")
            print("   3. Restart the application")
            sys.exit(1)

        print("✅ PayslipSender singleton protection active - no duplicates possible!")

        # Ensure correct working directory
        self.application_path = setup_working_directory()

        # Load environment variables
        load_dotenv()

        # Setup logging from environment
        self._setup_logging()

        # Database configuration
        self.db_config = {
            'server': os.getenv('DATABASE_SERVER', 'localhost'),
            'port': os.getenv('DATABASE_PORT', '1433'),
            'database': os.getenv('DATABASE_NAME', ''),
            'username': os.getenv('DATABASE_USERNAME', ''),
            'password': os.getenv('DATABASE_PASSWORD', ''),
            'trusted_connection': os.getenv('DATABASE_TRUSTED_CONNECTION', 'no').lower() == 'yes',
            'employee_table': os.getenv('EMPLOYEE_TABLE_NAME', 'Employees'),
            'employee_id_col': os.getenv('EMPLOYEE_ID_COLUMN', 'EmployeeId'),
            'employee_name_col': os.getenv('EMPLOYEE_NAME_COLUMN', 'Name'),
            'employee_email_col': os.getenv('EMPLOYEE_EMAIL_COLUMN', 'Email')
        }
        
        # Email configuration
        self.email_service = EmailService(
            smtp_server=os.getenv('SMTP_SERVER', 'smtp.gmail.com'),
            smtp_port=int(os.getenv('SMTP_PORT', '587')),
            username=os.getenv('SMTP_USERNAME', ''),
            password=os.getenv('SMTP_PASSWORD', ''),
            use_tls=os.getenv('SMTP_USE_TLS', 'True').lower() == 'true'
        )
        
        # File paths
        self.watch_directory = Path(os.getenv('WATCH_DIRECTORY', './payslips'))
        self.archived_directory = Path(os.getenv('ARCHIVED_DIRECTORY', './archived_payslips'))
        
        # Create directories
        self.watch_directory.mkdir(parents=True, exist_ok=True)
        self.archived_directory.mkdir(parents=True, exist_ok=True)
        Path('logs').mkdir(exist_ok=True)
        
        # Track processed files by full path and modification time
        self.processed_files = {}  # {file_path: (mtime, processed_time)}
        self.running = False

        # Setup file processing queue and worker threads
        self.file_queue = queue.Queue()
        self.max_workers = int(os.getenv('MAX_WORKER_THREADS', '3'))
        self.workers = []
        self.processing_lock = threading.Lock()

        # Enhanced error handling and duplicate prevention
        self.currently_processing = set()  # Track files currently being processed
        self.processing_ids = {}  # {file_path: processing_id} for tracking
        # Note: Failed files are now moved to archived_directory with FAILED_ prefix

        # File processing statistics
        self.stats = {
            'total_processed': 0,
            'successful_sends': 0,
            'failed_sends': 0,
            'errors': 0,
            'duplicates_prevented': 0
        }

        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

        self.logger.info("PayslipSender initialized for production deployment")
        self.logger.info(f"Working directory: {self.application_path}")
        self.logger.info(f"Watch directory: {self.watch_directory}")
        self.logger.info(f"Archived directory: {self.archived_directory}")

    def _setup_logging(self):
        """Setup logging configuration from environment variables."""
        log_level = os.getenv('LOG_LEVEL', 'INFO').upper()
        log_file = os.getenv('LOG_FILE', 'logs/payslip_sender.log')

        # Ensure logs directory exists
        Path(log_file).parent.mkdir(parents=True, exist_ok=True)

        # Configure logging
        logging.basicConfig(
            level=getattr(logging, log_level),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler() if os.getenv('LOG_TO_CONSOLE', 'true').lower() == 'true' else logging.NullHandler()
            ]
        )

        # Enable debug logging for troubleshooting
        if log_level == 'DEBUG':
            logging.getLogger().setLevel(logging.DEBUG)
        self.logger = logging.getLogger(__name__)



    def _signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully."""
        self.logger.info(f"Received signal {signum}, initiating graceful shutdown...")
        self.running = False

        # Stop all worker threads
        for _ in range(self.max_workers):
            self.file_queue.put(None)  # Poison pill to stop workers

    def _start_worker_threads(self):
        """Start worker threads for processing files."""
        self.logger.info(f"Starting {self.max_workers} worker threads for file processing")

        for i in range(self.max_workers):
            worker = threading.Thread(target=self._worker_thread, name=f"PayslipWorker-{i+1}")
            worker.daemon = True
            worker.start()
            self.workers.append(worker)

    def _worker_thread(self):
        """Worker thread that processes files from the queue."""
        thread_name = threading.current_thread().name
        self.logger.info(f"{thread_name} started and ready for processing")

        while self.running:
            try:
                # Get file from queue (timeout to check if we should stop)
                file_info = self.file_queue.get(timeout=2)

                # Check for poison pill (shutdown signal)
                if file_info is None:
                    self.logger.info(f"{thread_name} received shutdown signal")
                    break

                # Unpack file info (now includes processing ID)
                if len(file_info) == 4:
                    file_path, employee_id, date_str, processing_id = file_info
                else:
                    # Backward compatibility
                    file_path, employee_id, date_str = file_info
                    processing_id = "legacy"

                file_path_str = str(file_path)
                self.logger.info(f"{thread_name} processing: {file_path.name} (ID: {processing_id})")

                try:
                    # Double-check that we should still process this file
                    with self.processing_lock:
                        if file_path_str not in self.currently_processing:
                            self.logger.warning(f"{thread_name} - File {file_path.name} no longer marked for processing, skipping")
                            self.stats['duplicates_prevented'] += 1
                            continue

                        # Verify processing ID matches
                        if file_path_str in self.processing_ids and self.processing_ids[file_path_str] != processing_id:
                            self.logger.warning(f"{thread_name} - Processing ID mismatch for {file_path.name}, skipping")
                            self.stats['duplicates_prevented'] += 1
                            continue

                    # Process the file
                    success = self._process_single_file(file_path, employee_id, date_str, processing_id)

                    if success:
                        self.logger.info(f"{thread_name} completed successfully: {file_path.name} (ID: {processing_id})")
                        self.stats['successful_sends'] += 1
                    else:
                        self.logger.error(f"{thread_name} failed to process: {file_path.name} (ID: {processing_id})")
                        self.stats['failed_sends'] += 1

                    self.stats['total_processed'] += 1

                except Exception as e:
                    self.logger.error(f"{thread_name} failed to process {file_path.name}: {e}")
                    self.stats['failed_sends'] += 1
                    self.stats['total_processed'] += 1
                finally:
                    # CRITICAL: Always remove from currently processing when done
                    with self.processing_lock:
                        self.currently_processing.discard(file_path_str)
                        self.processing_ids.pop(file_path_str, None)

                        # Mark as processed with current timestamp
                        try:
                            current_mtime = file_path.stat().st_mtime if file_path.exists() else time.time()
                            self.processed_files[file_path_str] = (current_mtime, time.time())
                        except:
                            pass

                    self.file_queue.task_done()

            except queue.Empty:
                # Timeout occurred, continue loop to check if we should stop
                # This is normal - worker is waiting for new files
                continue
            except Exception as e:
                self.logger.error(f"{thread_name} error: {e}")

        self.logger.info(f"{thread_name} stopped")

    def clear_processed_files(self):
        """Clear the processed files list - useful for testing."""
        with self.processing_lock:
            count = len(self.processed_files)
            self.processed_files.clear()
            self.logger.info(f"Cleared {count} processed files from memory")

    def test_failed_logging(self, employee_id: str = "TEST_513"):
        """Test the failed send logging functionality."""
        self.logger.info("🧪 Testing failed send logging functionality...")

        try:
            # Test logging a failed send
            test_reason = f"Test failed send logging for employee {employee_id} - Employee not found in database"
            test_date = "2025-01-25"

            self.log_failed_send(employee_id, test_reason, test_date)

            # Verify the record was inserted
            with self.get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT TOP 1 id, EmployeeId, PayslipDate, Reason, FailedAt
                    FROM FailedToSendLogs
                    WHERE EmployeeId = ?
                    ORDER BY FailedAt DESC
                """, (employee_id,))

                result = cursor.fetchone()
                if result:
                    self.logger.info(f"✅ Test successful! Found record in database:")
                    self.logger.info(f"   ID: {result[0]}")
                    self.logger.info(f"   Employee ID: {result[1]}")
                    self.logger.info(f"   Payslip Date: {result[2]}")
                    self.logger.info(f"   Reason: {result[3]}")
                    self.logger.info(f"   Failed At: {result[4]}")
                    return True
                else:
                    self.logger.error("❌ Test failed! No record found in database")
                    return False

        except Exception as e:
            self.logger.error(f"❌ Test failed with exception: {e}")
            return False

    def test_comprehensive_failed_logging(self):
        """Comprehensive test for all FailedToSendLogs scenarios."""
        self.logger.info("🧪 Running comprehensive FailedToSendLogs tests...")

        try:
            # Test 1: Valid employee ID (non-existent employee)
            self.logger.info("Test 1: Valid employee ID format")
            self.log_failed_send("TEST_999", "Employee not found in database", "2025-01-25")

            # Test 2: Invalid filename
            self.logger.info("Test 2: Invalid filename format")
            self.log_failed_send_with_filename("invalid_file.pdf", "Invalid filename format")

            # Test 3: Another invalid filename with numbers
            self.logger.info("Test 3: Invalid filename with numbers")
            self.log_failed_send_with_filename("payslip_123_wrong_format.pdf", "Wrong filename pattern")

            # Verify all records
            with self.get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT COUNT(*) FROM FailedToSendLogs
                    WHERE (EmployeeId = 'TEST_999' AND Reason LIKE '%Employee not found%')
                       OR (EmployeeId LIKE 'INVALID_FILE_%' AND Reason LIKE '%Invalid filename%')
                       OR (EmployeeId LIKE 'INVALID_FILE_%' AND Reason LIKE '%Wrong filename%')
                """)

                count = cursor.fetchone()[0]

                if count >= 3:
                    self.logger.info(f"✅ Comprehensive test PASSED - Found {count} test records in FailedToSendLogs")

                    # Show recent records
                    cursor.execute("""
                        SELECT TOP 5 id, EmployeeId, PayslipDate, Reason, FailedAt
                        FROM FailedToSendLogs
                        ORDER BY FailedAt DESC
                    """)

                    records = cursor.fetchall()
                    self.logger.info("📋 Recent FailedToSendLogs entries:")
                    for record in records:
                        self.logger.info(f"   ID: {record[0]}, Employee: {record[1]}, Date: {record[2]}")
                        self.logger.info(f"      Reason: {record[3][:100]}...")

                    return True
                else:
                    self.logger.error(f"❌ Comprehensive test FAILED - Only found {count} records, expected at least 3")
                    return False

        except Exception as e:
            self.logger.error(f"❌ Comprehensive test failed with exception: {e}")
            return False

    def get_db_connection(self):
        """Get database connection with automatic ODBC driver detection."""
        # Get ODBC driver from environment or auto-detect
        odbc_driver = os.getenv('ODBC_DRIVER', '')
        if not odbc_driver:
            odbc_driver = self._detect_odbc_driver()

        if self.db_config['trusted_connection']:
            conn_str = f"DRIVER={{{odbc_driver}}};SERVER={self.db_config['server']},{self.db_config['port']};DATABASE={self.db_config['database']};Trusted_Connection=yes;TrustServerCertificate=yes;"
        else:
            conn_str = f"DRIVER={{{odbc_driver}}};SERVER={self.db_config['server']},{self.db_config['port']};DATABASE={self.db_config['database']};UID={self.db_config['username']};PWD={self.db_config['password']};TrustServerCertificate=yes;"

        return pyodbc.connect(conn_str)

    def _detect_odbc_driver(self):
        """Automatically detect the best available ODBC driver for SQL Server."""
        # List of SQL Server ODBC drivers in order of preference (newest first)
        preferred_drivers = [
            "ODBC Driver 18 for SQL Server",
            "ODBC Driver 17 for SQL Server",
            "ODBC Driver 13 for SQL Server",
            "ODBC Driver 11 for SQL Server",
            "SQL Server Native Client 11.0",
            "SQL Server Native Client 10.0",
            "SQL Server"
        ]

        available_drivers = pyodbc.drivers()

        for driver in preferred_drivers:
            if driver in available_drivers:
                self.logger.info(f"Using ODBC driver: {driver}")
                return driver

        # Fallback - use the first available SQL Server driver
        for driver in available_drivers:
            if "SQL Server" in driver:
                self.logger.warning(f"Using fallback ODBC driver: {driver}")
                return driver

        # No SQL Server driver found
        raise Exception("No SQL Server ODBC driver found. Please install SQL Server ODBC driver.")
    
    def get_employee_by_id(self, employee_id: str):
        """Get employee information from database."""
        try:
            with self.get_db_connection() as conn:
                cursor = conn.cursor()
                query = f"""
                    SELECT {self.db_config['employee_id_col']}, 
                           {self.db_config['employee_name_col']}, 
                           {self.db_config['employee_email_col']} 
                    FROM {self.db_config['employee_table']} 
                    WHERE {self.db_config['employee_id_col']} = ?
                """
                cursor.execute(query, (employee_id,))
                row = cursor.fetchone()
                
                if row:
                    return {
                        'EmployeeId': row[0],
                        'Name': row[1],
                        'Email': row[2]
                    }
                return None
        except Exception as e:
            self.logger.error(f"Database error getting employee {employee_id}: {e}")
            return None

    def log_failed_send(self, employee_id: str, reason: str, payslip_date: str = None):
        """Log failed send attempt to database."""
        try:
            self.logger.info(f"Attempting to log failed send for employee {employee_id}")

            with self.get_db_connection() as conn:
                cursor = conn.cursor()

                # Use the table name you mentioned: FailedToSendLogs
                # Create failed logs table if it doesn't exist
                create_table_sql = """
                    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'FailedToSendLogs')
                    CREATE TABLE FailedToSendLogs (
                        id INT IDENTITY(1,1) PRIMARY KEY,
                        EmployeeId NVARCHAR(50) NOT NULL,
                        PayslipDate NVARCHAR(50) NOT NULL,
                        Reason NVARCHAR(MAX) NOT NULL,
                        FailedAt DATETIME DEFAULT GETDATE(),
                        ProcessedBy NVARCHAR(100) DEFAULT SYSTEM_USER
                    )
                """

                self.logger.debug("Creating FailedToSendLogs table if not exists")
                cursor.execute(create_table_sql)

                # Use provided payslip_date or current date
                if not payslip_date:
                    payslip_date = datetime.now().strftime("%Y-%m-%d")

                # Insert failed log
                insert_sql = """
                    INSERT INTO FailedToSendLogs (EmployeeId, PayslipDate, Reason)
                    VALUES (?, ?, ?)
                """

                self.logger.debug(f"Inserting failed log: EmployeeId={employee_id}, PayslipDate={payslip_date}, Reason={reason}")
                cursor.execute(insert_sql, (employee_id, payslip_date, reason))

                # Get the inserted record ID for confirmation
                cursor.execute("SELECT @@IDENTITY")
                record_id = cursor.fetchone()[0]

                conn.commit()
                self.logger.info(f"✅ Successfully logged failed send to database (Record ID: {record_id}) - Employee: {employee_id}, Reason: {reason}")

        except Exception as e:
            self.logger.error(f"❌ CRITICAL: Failed to log failed send to database for employee {employee_id}: {e}")
            self.logger.error(f"Database connection details: Server={self.db_config.get('server')}, Database={self.db_config.get('database')}")
            # Try to log additional debug info
            try:
                self.logger.error(f"Exception type: {type(e).__name__}")
                self.logger.error(f"Exception details: {str(e)}")
            except:
                pass

    def log_failed_send_with_filename(self, filename: str, reason: str):
        """Log failed send attempt for invalid filenames that can't be parsed."""
        try:
            self.logger.info(f"Attempting to log failed send for invalid filename: {filename}")

            with self.get_db_connection() as conn:
                cursor = conn.cursor()

                # Create failed logs table if it doesn't exist
                create_table_sql = """
                    IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'FailedToSendLogs')
                    CREATE TABLE FailedToSendLogs (
                        id INT IDENTITY(1,1) PRIMARY KEY,
                        EmployeeId NVARCHAR(50) NOT NULL,
                        PayslipDate NVARCHAR(50) NOT NULL,
                        Reason NVARCHAR(MAX) NOT NULL,
                        FailedAt DATETIME DEFAULT GETDATE(),
                        ProcessedBy NVARCHAR(100) DEFAULT SYSTEM_USER
                    )
                """

                self.logger.debug("Creating FailedToSendLogs table if not exists")
                cursor.execute(create_table_sql)

                # For invalid filenames, use filename as employee_id and current date
                current_date = datetime.now().strftime("%Y-%m-%d")

                # Try to extract any numbers from filename as potential employee ID
                import re
                numbers = re.findall(r'\d+', filename)
                potential_employee_id = numbers[0] if numbers else "UNKNOWN"

                # Create a descriptive employee_id field
                employee_id_field = f"INVALID_FILE_{potential_employee_id}"

                # Enhanced reason with filename
                enhanced_reason = f"Invalid filename '{filename}': {reason}"

                # Insert failed log
                insert_sql = """
                    INSERT INTO FailedToSendLogs (EmployeeId, PayslipDate, Reason)
                    VALUES (?, ?, ?)
                """

                self.logger.debug(f"Inserting failed log for invalid filename: EmployeeId={employee_id_field}, PayslipDate={current_date}, Reason={enhanced_reason}")
                cursor.execute(insert_sql, (employee_id_field, current_date, enhanced_reason))

                # Get the inserted record ID for confirmation
                cursor.execute("SELECT @@IDENTITY")
                record_id = cursor.fetchone()[0]

                conn.commit()
                self.logger.info(f"✅ Successfully logged failed send for invalid filename to database (Record ID: {record_id}) - File: {filename}")

        except Exception as e:
            self.logger.error(f"❌ CRITICAL: Failed to log failed send to database for invalid filename {filename}: {e}")
            self.logger.error(f"Database connection details: Server={self.db_config.get('server')}, Database={self.db_config.get('database')}")
            # Try to log additional debug info
            try:
                self.logger.error(f"Exception type: {type(e).__name__}")
                self.logger.error(f"Exception details: {str(e)}")
            except:
                pass
    
    def parse_filename(self, filename: str):
        """Parse payslip filename to extract employee ID and date."""
        import re

        # Expected format: Payslip_EmployeeID_YYYYMMDD.pdf
        match = re.match(r'^Payslip_(\w+)_(\d{8})\.pdf$', filename, re.IGNORECASE)
        if match:
            return match.group(1), match.group(2)
        return None, None

    def _format_date_string(self, date_str: str):
        """Convert YYYYMMDD format to YYYY-MM-DD format."""
        if not date_str or len(date_str) != 8:
            return datetime.now().strftime("%Y-%m-%d")

        try:
            # Convert YYYYMMDD to YYYY-MM-DD
            year = date_str[:4]
            month = date_str[4:6]
            day = date_str[6:8]
            return f"{year}-{month}-{day}"
        except:
            return datetime.now().strftime("%Y-%m-%d")

    def _generate_processing_id(self, file_path: Path):
        """Generate unique processing ID for file tracking."""
        file_info = f"{file_path.name}_{file_path.stat().st_mtime}_{time.time()}"
        return hashlib.md5(file_info.encode()).hexdigest()[:12]

    def _validate_pdf_file(self, file_path: Path):
        """Comprehensive PDF file validation."""
        validation_errors = []

        try:
            # Check if file exists
            if not file_path.exists():
                validation_errors.append("File does not exist")
                return validation_errors

            # Check file size
            file_size = file_path.stat().st_size
            if file_size == 0:
                validation_errors.append("File is empty (0 bytes)")
            elif file_size < 100:  # Very small for a PDF
                validation_errors.append(f"File is suspiciously small ({file_size} bytes)")

            # Check file permissions
            if not os.access(file_path, os.R_OK):
                validation_errors.append("File is not readable (permission denied)")

            # Check PDF header (basic validation)
            try:
                with open(file_path, 'rb') as f:
                    header = f.read(8)
                    if not header.startswith(b'%PDF-'):
                        validation_errors.append("File does not appear to be a valid PDF (missing PDF header)")
            except PermissionError:
                validation_errors.append("Cannot read file due to permission restrictions")
            except Exception as e:
                validation_errors.append(f"Error reading file: {str(e)}")

            # Check filename format
            employee_id, date_str = self.parse_filename(file_path.name)
            if not employee_id or not date_str:
                validation_errors.append("Invalid filename format (expected: Payslip_EmployeeID_YYYYMMDD.pdf)")
            else:
                # Validate date format
                try:
                    datetime.strptime(date_str, '%Y%m%d')
                except ValueError:
                    validation_errors.append(f"Invalid date format in filename: {date_str} (expected: YYYYMMDD)")

        except Exception as e:
            validation_errors.append(f"Unexpected error during validation: {str(e)}")

        return validation_errors


    
    def move_to_archived(self, file_path: Path, is_failed: bool = False, failure_reason: str = ""):
        """Move processed file to archived directory."""
        self.logger.info(f"🔄 ATTEMPTING TO MOVE FILE: {file_path.name} (failed={is_failed})")
        try:
            # Check if file exists before attempting to move
            if not file_path.exists():
                self.logger.error(f"❌ Cannot move file - file does not exist: {file_path}")
                return False
                
            # For failed files, add FAILED prefix and timestamp
            if is_failed:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                stem = file_path.stem
                suffix = file_path.suffix
                destination = self.archived_directory / f"FAILED_{timestamp}_{stem}{suffix}"
                
                # Create failure info file
                info_filename = f"FAILED_{timestamp}_{stem}_info.txt"
                info_path = self.archived_directory / info_filename
                try:
                    with open(info_path, 'w', encoding='utf-8') as f:
                        f.write(f"Failed File Information\n")
                        f.write(f"=======================\n")
                        f.write(f"Original File: {file_path.name}\n")
                        f.write(f"Failed Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                        f.write(f"Failure Reason: {failure_reason}\n")
                        f.write(f"File Size: {file_path.stat().st_size if file_path.exists() else 'N/A'} bytes\n")
                    self.logger.info(f"✅ Created failure info file: {info_filename}")
                except Exception as info_error:
                    self.logger.warning(f"Could not create failure info file: {info_error}")
            else:
                # For successful files, use original logic
                destination = self.archived_directory / file_path.name
                
                # Add timestamp if file already exists
                if destination.exists():
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    stem = destination.stem
                    suffix = destination.suffix
                    destination = self.archived_directory / f"{stem}_{timestamp}{suffix}"
            
            self.logger.info(f"🔄 Moving {file_path} -> {destination}")
            file_path.rename(destination)
            status = "failed" if is_failed else "processed"
            self.logger.info(f"✅ Successfully moved {status} file to archived directory: {destination.name}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Error moving file to archived directory: {e}")
            return False

    def process_payslip_file(self, file_path: Path, employee_id: str, date_str: str, processing_id: str = "unknown"):
        """Process a single payslip file with comprehensive error handling."""
        self.logger.info(f"Processing payslip for employee {employee_id}, date {date_str} (ID: {processing_id})")
        formatted_date = self._format_date_string(date_str)

        try:
            # Database connection check
            try:
                # Get employee information with timeout
                employee = self.get_employee_by_id(employee_id)
            except pyodbc.Error as db_error:
                error_msg = f"Database connection failed: {str(db_error)}"
                self.logger.error(error_msg)
                self.log_failed_send(employee_id, error_msg, formatted_date)
                self.move_to_archived(file_path, is_failed=True, failure_reason=error_msg)
                return False
            except Exception as db_error:
                error_msg = f"Database query error: {str(db_error)}"
                self.logger.error(error_msg)
                self.log_failed_send(employee_id, error_msg, formatted_date)
                self.move_to_archived(file_path, is_failed=True, failure_reason=error_msg)
                return False

            if not employee:
                error_msg = f"Employee ID {employee_id} not found in database"
                self.logger.error(error_msg)
                self.log_failed_send(employee_id, error_msg, formatted_date)
                self.move_to_archived(file_path, is_failed=True, failure_reason=error_msg)
                return False

            # Validate employee email
            employee_email = employee.get('Email', '').strip()
            if not employee_email:
                error_msg = f"Employee {employee_id} has no email address in database"
                self.logger.error(error_msg)
                self.log_failed_send(employee_id, error_msg, formatted_date)
                self.move_to_archived(file_path, is_failed=True, failure_reason=error_msg)
                return False

            # Basic email validation
            if '@' not in employee_email or '.' not in employee_email:
                error_msg = f"Employee {employee_id} has invalid email address: {employee_email}"
                self.logger.error(error_msg)
                self.log_failed_send(employee_id, error_msg, formatted_date)
                self.move_to_archived(file_path, is_failed=True, failure_reason=error_msg)
                return False

            # Check file accessibility before sending
            try:
                file_size = file_path.stat().st_size
                if file_size == 0:
                    error_msg = "PDF file is empty"
                    self.logger.error(error_msg)
                    self.log_failed_send(employee_id, error_msg, formatted_date)
                    self.move_to_archived(file_path, is_failed=True, failure_reason=error_msg)
                    return False
            except Exception as file_error:
                error_msg = f"Cannot access PDF file: {str(file_error)}"
                self.logger.error(error_msg)
                self.log_failed_send(employee_id, error_msg, formatted_date)
                self.move_to_archived(file_path, is_failed=True, failure_reason=error_msg)
                return False

            # Send email with comprehensive error handling
            try:
                self.logger.debug(f"Attempting to send email to {employee_email} for employee {employee_id}")

                success = self.email_service.send_email(
                    to_email=employee_email,
                    employee_name=employee.get('Name', f'Employee {employee_id}'),
                    payslip_date_str=date_str,
                    pdf_path=str(file_path)
                )

                if success:
                    self.logger.info(f"✅ Successfully sent payslip to {employee.get('Name', employee_id)} ({employee_email})")

                    # Move to archived folder
                    try:
                        self.move_to_archived(file_path)
                        return True
                    except Exception as archive_error:
                        # Email was sent successfully, but archiving failed
                        error_msg = f"Email sent successfully but failed to archive file: {str(archive_error)}"
                        self.logger.warning(error_msg)
                        # Don't log as failed send since email was successful
                        return True

                else:
                    error_msg = f"Email service returned failure for {employee_email}"
                    self.logger.error(error_msg)
                    self.log_failed_send(employee_id, error_msg, formatted_date)
                    self.move_to_archived(file_path, is_failed=True, failure_reason=error_msg)
                    return False

            except Exception as email_error:
                error_msg = f"Email sending failed: {str(email_error)}"
                self.logger.error(error_msg)
                self.log_failed_send(employee_id, error_msg, formatted_date)
                self.move_to_archived(file_path, is_failed=True, failure_reason=error_msg)
                return False

        except Exception as e:
            error_msg = f"Unexpected error processing payslip: {str(e)}"
            self.logger.error(error_msg)
            self.log_failed_send(employee_id, error_msg, formatted_date)
            self.move_to_archived(file_path, is_failed=True, failure_reason=error_msg)
            return False
    
    def scan_directory(self):
        """Scan watch directory for new payslip files and queue them for processing."""
        try:
            # Get all PDF files in the directory
            pdf_files = list(self.watch_directory.glob('*.pdf'))
            self.logger.debug(f"Scan found {len(pdf_files)} total PDF files in directory")

            # Clean up processed files list - remove files that no longer exist
            with self.processing_lock:
                existing_files = {str(f) for f in pdf_files}
                files_to_remove = [f for f in self.processed_files.keys() if f not in existing_files]
                for f in files_to_remove:
                    del self.processed_files[f]
                    # Also remove from currently processing if it was there
                    self.currently_processing.discard(f)
                    if f in self.processing_ids:
                        del self.processing_ids[f]

                if files_to_remove:
                    self.logger.debug(f"Removed {len(files_to_remove)} non-existent files from processed list")

                # Filter out files that are new or have been modified since last processing
                new_files = []
                for pdf_file in pdf_files:
                    file_path = str(pdf_file)

                    # Skip if currently being processed (CRITICAL: Prevents duplicates)
                    if file_path in self.currently_processing:
                        self.logger.debug(f"Skipping {pdf_file.name} - currently being processed")
                        self.stats['duplicates_prevented'] += 1
                        continue

                    try:
                        current_mtime = pdf_file.stat().st_mtime

                        if file_path not in self.processed_files:
                            # File is completely new
                            new_files.append(pdf_file)
                            self.logger.debug(f"New file detected: {pdf_file.name}")
                        else:
                            # Check if file has been modified since last processing
                            last_mtime, _ = self.processed_files[file_path]
                            if current_mtime > last_mtime:
                                new_files.append(pdf_file)
                                self.logger.debug(f"Modified file detected: {pdf_file.name}")
                    except Exception as e:
                        self.logger.error(f"Error checking file {pdf_file.name}: {e}")
                        # Move problematic file to archived folder
                        error_msg = f"Error during file check: {e}"
                        self.move_to_archived(pdf_file, is_failed=True, failure_reason=error_msg)
                        self.stats['errors'] += 1
                        continue

                processed_count = len(self.processed_files)

            self.logger.debug(f"After filtering: {len(new_files)} new files, {processed_count} already processed")

            if new_files:
                self.logger.info(f"Found {len(new_files)} new payslip files to process")
                for file in new_files:
                    self.logger.debug(f"New file detected: {file.name}")
            else:
                self.logger.debug("No new files found in this scan")

            # Queue each new file for processing by worker threads
            for pdf_file in new_files:
                file_path = str(pdf_file)
                processing_id = self._generate_processing_id(pdf_file)

                try:
                    # CRITICAL: Mark as currently processing BEFORE any other operations
                    with self.processing_lock:
                        if file_path in self.currently_processing:
                            self.logger.warning(f"File {pdf_file.name} already being processed, skipping")
                            self.stats['duplicates_prevented'] += 1
                            continue

                        self.currently_processing.add(file_path)
                        self.processing_ids[file_path] = processing_id

                    # Comprehensive file validation
                    validation_errors = self._validate_pdf_file(pdf_file)
                    if validation_errors:
                        error_msg = f"File validation failed: {'; '.join(validation_errors)}"
                        self.logger.error(f"File {pdf_file.name}: {error_msg}")

                        # Move to archived folder and log to database
                        self.move_to_archived(pdf_file, is_failed=True, failure_reason=error_msg)

                        # Try to extract employee ID for logging even if validation failed
                        employee_id, date_str = self.parse_filename(pdf_file.name)
                        if employee_id:
                            formatted_date = self._format_date_string(date_str) if date_str else None
                            self.log_failed_send(employee_id, error_msg, formatted_date)
                        else:
                            # For invalid filenames, log with filename as identifier
                            self.log_failed_send_with_filename(pdf_file.name, error_msg)

                        self.stats['errors'] += 1

                        # Remove from currently processing
                        with self.processing_lock:
                            self.currently_processing.discard(file_path)
                            self.processing_ids.pop(file_path, None)
                            # Mark as processed to avoid repeated attempts
                            current_mtime = pdf_file.stat().st_mtime if pdf_file.exists() else time.time()
                            self.processed_files[file_path] = (current_mtime, time.time())
                        continue

                    # Parse filename (already validated above, but extract values)
                    employee_id, date_str = self.parse_filename(pdf_file.name)

                    if employee_id and date_str:
                        self.logger.info(f"Queuing file for processing: {pdf_file.name} (ID: {processing_id})")

                        # Add to queue for worker threads with processing ID
                        self.file_queue.put((pdf_file, employee_id, date_str, processing_id))
                        queue_size = self.file_queue.qsize()
                        self.logger.debug(f"File queued. Current queue size: {queue_size}")

                        # DO NOT mark as processed here - only mark after successful processing
                        # This prevents the file from being processed again while in queue

                    else:
                        # This should not happen due to validation above, but handle just in case
                        error_msg = f"Invalid filename format: {pdf_file.name}"
                        self.logger.error(error_msg)
                        self.move_to_archived(pdf_file, is_failed=True, failure_reason=error_msg)

                        # Log to database even for invalid filenames
                        self.log_failed_send_with_filename(pdf_file.name, error_msg)

                        self.stats['errors'] += 1

                        with self.processing_lock:
                            self.currently_processing.discard(file_path)
                            self.processing_ids.pop(file_path, None)
                            current_mtime = pdf_file.stat().st_mtime if pdf_file.exists() else time.time()
                            self.processed_files[file_path] = (current_mtime, time.time())

                except Exception as e:
                    error_msg = f"Error queuing file {pdf_file.name}: {e}"
                    self.logger.error(error_msg)

                    # Clean up and mark as processed to avoid repeated errors
                    with self.processing_lock:
                        self.currently_processing.discard(file_path)
                        self.processing_ids.pop(file_path, None)
                        try:
                            current_mtime = pdf_file.stat().st_mtime if pdf_file.exists() else time.time()
                            self.processed_files[file_path] = (current_mtime, time.time())
                        except:
                            pass

                    self.stats['errors'] += 1

        except Exception as e:
            self.logger.error(f"Error scanning directory: {e}")
    
    def _process_single_file(self, file_path, employee_id, date_str, processing_id="unknown"):
        """Process a single payslip file in a worker thread with comprehensive error handling."""
        try:
            self.logger.debug(f"Starting processing of {file_path.name} (ID: {processing_id})")

            # Final validation before processing
            if not file_path.exists():
                error_msg = "File no longer exists"
                self.logger.error(f"Processing {file_path.name}: {error_msg}")
                formatted_date = self._format_date_string(date_str)
                self.log_failed_send(employee_id, error_msg, formatted_date)
                # File doesn't exist, so we can't move it to archived
                return False

            # Process the file
            success = self.process_payslip_file(file_path, employee_id, date_str, processing_id)
            return success

        except Exception as e:
            error_msg = f"Unexpected error processing file: {str(e)}"
            self.logger.error(f"Processing {file_path.name}: {error_msg}")
            formatted_date = self._format_date_string(date_str) if date_str else None
            self.log_failed_send(employee_id, error_msg, formatted_date)
            # Move failed file to archived directory
            if file_path.exists():
                self.move_to_archived(file_path, is_failed=True, failure_reason=error_msg)
            return False

    def run(self):
        """Run the payslip sender service with parallel processing."""
        self.logger.info("Starting PayslipSender service with parallel processing...")

        # Validate configuration and connections
        if not self._validate_startup():
            return False

        self.logger.info(f"Monitoring directory: {self.watch_directory}")
        self.logger.info(f"Using {self.max_workers} worker threads for parallel processing")
        self.logger.info("PayslipSender service is running")

        self.running = True
        scan_interval = int(os.getenv('SCAN_INTERVAL_SECONDS', '1'))  # Faster scanning

        # Start worker threads
        self._start_worker_threads()

        try:
            # Initial scan to process any existing files
            self.scan_directory()

            # Main monitoring loop - keep scanning for new files
            last_status_time = time.time()
            last_cleanup_time = time.time()
            status_interval = 60  # Log status every 60 seconds
            cleanup_interval = 3600  # Clean up processed files list every hour

            while self.running:
                # Scan for new files
                self.scan_directory()

                current_time = time.time()

                # Periodically log queue status
                if current_time - last_status_time > status_interval:
                    queue_size = self.file_queue.qsize()
                    active_workers = sum(1 for w in self.workers if w.is_alive())
                    with self.processing_lock:
                        processed_count = len(self.processed_files)
                        currently_processing_count = len(self.currently_processing)

                    self.logger.info(f"Status: {queue_size} queued, {currently_processing_count} processing, {active_workers}/{self.max_workers} workers, {processed_count} completed")
                    self.logger.info(f"Stats: {self.stats['successful_sends']} sent, {self.stats['failed_sends']} failed, {self.stats['errors']} errors, {self.stats['duplicates_prevented']} duplicates prevented")

                    # Check if any workers died and restart them
                    for i, worker in enumerate(self.workers):
                        if not worker.is_alive():
                            self.logger.warning(f"Worker {worker.name} died, restarting...")
                            new_worker = threading.Thread(target=self._worker_thread, name=f"PayslipWorker-{i+1}")
                            new_worker.daemon = True
                            new_worker.start()
                            self.workers[i] = new_worker

                    last_status_time = current_time

                # Periodically clean up processed files list to prevent memory issues
                if current_time - last_cleanup_time > cleanup_interval:
                    with self.processing_lock:
                        count = len(self.processed_files)
                        if count > 1000:  # Only clean if we have a lot of files
                            self.logger.info(f"Cleaning up processed files list ({count} entries)")
                            self.processed_files.clear()
                    last_cleanup_time = current_time

                # Wait before next scan
                time.sleep(scan_interval)

        except KeyboardInterrupt:
            self.logger.info("Received keyboard interrupt, shutting down...")
        except Exception as e:
            self.logger.error(f"Unexpected error in main loop: {e}")
            return False
        finally:
            # Clean shutdown
            self.logger.info("Initiating graceful shutdown...")
            self.running = False

            # Wait for current processing to complete (with timeout)
            self.logger.info("Waiting for current file processing to complete...")
            try:
                # Wait for queue to empty, but with a reasonable timeout
                queue_empty = False
                wait_time = 0
                max_wait = 30  # Maximum 30 seconds to wait

                while not queue_empty and wait_time < max_wait:
                    if self.file_queue.empty():
                        queue_empty = True
                    else:
                        time.sleep(1)
                        wait_time += 1

                if not queue_empty:
                    self.logger.warning(f"Queue not empty after {max_wait} seconds, forcing shutdown")

            except Exception as e:
                self.logger.error(f"Error during queue cleanup: {e}")

            # Stop worker threads
            self.logger.info("Stopping worker threads...")
            for _ in range(self.max_workers):
                self.file_queue.put(None)  # Poison pill

            # Wait for workers to finish
            for worker in self.workers:
                worker.join(timeout=3)
                if worker.is_alive():
                    self.logger.warning(f"Worker {worker.name} did not stop gracefully")

            # Log final statistics
            self._log_statistics()
            self.logger.info("PayslipSender service stopped")

    def _log_statistics(self):
        """Log processing statistics."""
        self.logger.info("=== PayslipSender Processing Statistics ===")
        self.logger.info(f"Total files processed: {self.stats['total_processed']}")
        self.logger.info(f"Successful sends: {self.stats['successful_sends']}")
        self.logger.info(f"Failed sends: {self.stats['failed_sends']}")
        self.logger.info(f"Errors encountered: {self.stats['errors']}")
        self.logger.info(f"Duplicates prevented: {self.stats['duplicates_prevented']}")

        if self.stats['total_processed'] > 0:
            success_rate = (self.stats['successful_sends'] / self.stats['total_processed']) * 100
            self.logger.info(f"Success rate: {success_rate:.1f}%")

        self.logger.info("============================================")

        return True

    def _validate_startup(self):
        """Validate email and database connections on startup."""
        # Test email connection
        if not self.email_service.test_connection():
            self.logger.error("Email service connection failed - check SMTP configuration")
            return False

        # Test database connection with a simple query
        try:
            with self.get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT 1")
                cursor.fetchone()
            self.logger.info("Database connection validated")
        except Exception as e:
            self.logger.error(f"Database connection failed: {e}")
            return False

        return True

def register_as_service():
    """Register PayslipSender as a Windows service."""
    try:
        import os
        import subprocess

        print("🔧 Registering PayslipSender as a Windows service...")

        # Get the path to the executable
        if getattr(sys, 'frozen', False):
            # Running as executable
            executable_path = sys.executable
        else:
            # Running as script - can't register as service
            print("❌ Cannot register as service when running as script.")
            print("Please create the executable first with: python create_exe.py")
            return False

        # Service details
        service_name = "DSCPayslipSender"
        display_name = "DSC PayslipSender Service"
        description = "Automated payslip email sender for DSC Human Resources"

        # Check if service already exists
        check_cmd = f'sc query "{service_name}"'
        result = subprocess.run(check_cmd, shell=True, capture_output=True, text=True)

        if result.returncode == 0:
            print(f"⚠️ Service '{service_name}' already exists. Removing...")

            # Stop the service if it's running
            subprocess.run(f'sc stop "{service_name}"', shell=True)

            # Wait a moment
            time.sleep(3)

            # Delete the service
            delete_result = subprocess.run(f'sc delete "{service_name}"', shell=True)

            if delete_result.returncode != 0:
                print(f"❌ Failed to remove existing service. Please run as Administrator.")
                return False

            print(f"✅ Existing service removed.")

        # Create the service
        create_cmd = f'sc create "{service_name}" binPath= "\"{executable_path}\"" DisplayName= "{display_name}" start= auto'
        create_result = subprocess.run(create_cmd, shell=True, capture_output=True, text=True)

        if create_result.returncode != 0:
            print(f"❌ Failed to create service. Error: {create_result.stderr}")
            print("Please run as Administrator.")
            return False

        # Set description
        subprocess.run(f'sc description "{service_name}" "{description}"', shell=True)

        # Configure recovery options (restart on failure)
        subprocess.run(f'sc failure "{service_name}" reset= 86400 actions= restart/30000/restart/60000/restart/120000', shell=True)

        print(f"✅ Service '{service_name}' registered successfully!")
        print(f"Service details:")
        print(f"- Name: {service_name}")
        print(f"- Display Name: {display_name}")
        print(f"- Executable: {executable_path}")
        print(f"- Startup: Automatic")
        print(f"- Recovery: Restart on failure")

        # Ask if user wants to start the service
        start_now = input("Do you want to start the service now? (Y/N): ")
        if start_now.lower() == 'y':
            print(f"Starting service '{service_name}'...")
            start_result = subprocess.run(f'sc start "{service_name}"', shell=True)

            if start_result.returncode == 0:
                print(f"✅ Service started successfully!")
            else:
                print(f"❌ Failed to start service. Try starting manually:")
                print(f"sc start \"{service_name}\"")

        print("\nService Management Commands:")
        print(f"- Start:  sc start \"{service_name}\"")
        print(f"- Stop:   sc stop \"{service_name}\"")
        print(f"- Status: sc query \"{service_name}\"")
        print(f"- Remove: sc delete \"{service_name}\"")

        return True

    except Exception as e:
        print(f"❌ Error registering service: {e}")
        print("Please run as Administrator.")
        return False

def main():
    """Main entry point for the PayslipSender service."""
    # Check for command-line arguments
    if len(sys.argv) > 1:
        # Handle --register-service argument
        if sys.argv[1] == "--register-service":
            register_as_service()
            return
        # Handle --help argument
        elif sys.argv[1] == "--help" or sys.argv[1] == "-h":
            print("PayslipSender Usage:")
            print("  PayslipSender.exe                   - Run the application normally")
            print("  PayslipSender.exe --register-service - Register as Windows service")
            print("  PayslipSender.exe --help            - Show this help message")
            return

    sender = None
    try:
        print("🚀 Starting PayslipSender with bulletproof singleton protection...")

        # The bulletproof singleton lock is handled in PayslipSender.__init__()
        # If another instance is running, it will exit there with clear error message
        sender = PayslipSender()

        print("🎯 PayslipSender initialized successfully - starting service...")
        success = sender.run()

        print(f"🏁 PayslipSender service ended - Success: {success}")
        sys.exit(0 if success else 1)

    except KeyboardInterrupt:
        print("\n🛑 PayslipSender interrupted by user (Ctrl+C)")
        if sender and hasattr(sender, 'singleton'):
            sender.singleton.release()
        sys.exit(0)
    except Exception as e:
        print(f"❌ FATAL ERROR in PayslipSender: {e}")
        if sender and hasattr(sender, 'singleton'):
            sender.singleton.release()
        sys.exit(1)
    finally:
        # Ensure singleton is always released
        if sender and hasattr(sender, 'singleton'):
            sender.singleton.release()

if __name__ == "__main__":
    main()
