# PayslipSender Windows Service Installation Guide

This guide explains how to install and manage PayslipSender as a Windows service using the provided batch files.

## 📋 Prerequisites

1. **Administrator Rights**: All service operations require administrator privileges
2. **PayslipSender Executable**: Run `python create_exe.py` to create `dist\PayslipSender.exe`
3. **Windows 7/8/10/11**: Compatible with all modern Windows versions

## 🚀 Quick Start

### Step 1: Install as Service
```cmd
# Right-click and "Run as administrator"
install_service.bat
```

### Step 2: Manage Service
```cmd
# Right-click and "Run as administrator"  
manage_service.bat
```

## 📁 Batch Files Overview

### `install_service.bat`
**Purpose**: Installs PayslipSender as a Windows service

**Features**:
- ✅ Downloads NSSM automatically if not present
- ✅ Configures service with proper settings
- ✅ Sets up automatic startup on boot
- ✅ Configures logging and error handling
- ✅ Removes existing service if present

**Usage**:
```cmd
# Run as Administrator
install_service.bat
```

### `uninstall_service.bat`
**Purpose**: Completely removes PayslipSender service

**Features**:
- ✅ Stops running service gracefully
- ✅ Removes service from Windows
- ✅ Option to delete log files
- ✅ Fallback removal methods

**Usage**:
```cmd
# Run as Administrator
uninstall_service.bat
```

### `manage_service.bat`
**Purpose**: Interactive service management menu

**Features**:
- ✅ Start/Stop/Restart service
- ✅ View service status and configuration
- ✅ View and monitor log files
- ✅ Edit service configuration
- ✅ Test executable functionality

**Usage**:
```cmd
# Run as Administrator
manage_service.bat
```

## 🔧 Service Configuration

### Service Details
- **Service Name**: `DSCPayslipSender`
- **Display Name**: `DSC PayslipSender Service`
- **Startup Type**: Automatic (starts on boot)
- **Recovery**: Restarts on failure after 30 seconds

### File Locations
- **Executable**: `dist\PayslipSender.exe`
- **Working Directory**: Current folder
- **Application Logs**: `logs\payslip_sender.log`
- **Service Output**: `logs\service_output.log`
- **Service Errors**: `logs\service_error.log`

## 📊 Service Management Commands

### Using Windows Services Manager
```cmd
# Open Services Manager
services.msc

# Find "DSC PayslipSender Service"
# Right-click for Start/Stop/Properties
```

### Using Command Line
```cmd
# Start service
sc start DSCPayslipSender

# Stop service
sc stop DSCPayslipSender

# Check status
sc query DSCPayslipSender

# Delete service (if needed)
sc delete DSCPayslipSender
```

### Using NSSM (if available)
```cmd
# Edit service configuration
nssm.exe edit DSCPayslipSender

# Start service
nssm.exe start DSCPayslipSender

# Stop service
nssm.exe stop DSCPayslipSender
```

## 🔍 Troubleshooting

### Service Won't Start
1. **Check executable exists**: `dist\PayslipSender.exe`
2. **Check logs**: `logs\service_error.log`
3. **Test executable manually**: Run `dist\PayslipSender.exe` directly
4. **Check permissions**: Ensure service has proper file access
5. **Check database connection**: Verify .env configuration

### Service Stops Unexpectedly
1. **Check application logs**: `logs\payslip_sender.log`
2. **Check service logs**: `logs\service_output.log` and `logs\service_error.log`
3. **Check Windows Event Viewer**: Look for service-related errors
4. **Verify .env configuration**: Database and email settings

### Installation Issues
1. **Run as Administrator**: Required for service installation
2. **Check NSSM download**: Script downloads automatically
3. **Antivirus interference**: Temporarily disable if blocking
4. **Windows Defender**: Add folder to exclusions if needed

## 📝 Log Monitoring

### Real-time Log Monitoring
```cmd
# Monitor application logs
powershell "Get-Content logs\payslip_sender.log -Wait -Tail 20"

# Monitor service output
powershell "Get-Content logs\service_output.log -Wait -Tail 20"

# Monitor service errors
powershell "Get-Content logs\service_error.log -Wait -Tail 20"
```

### Log Rotation
- Logs are automatically managed by the application
- Service logs are managed by NSSM
- Consider setting up log rotation for long-term operation

## 🔄 Service Updates

### Updating PayslipSender
1. **Stop the service**: `sc stop DSCPayslipSender`
2. **Update code**: Make your changes
3. **Rebuild executable**: `python create_exe.py`
4. **Start the service**: `sc start DSCPayslipSender`

### Updating Service Configuration
1. **Use manage_service.bat**: Option 6 - Edit Service Configuration
2. **Or use NSSM directly**: `nssm.exe edit DSCPayslipSender`
3. **Restart service**: Apply changes

## 🛡️ Security Considerations

### Service Account
- Service runs under Local System account by default
- Consider creating dedicated service account for production
- Ensure account has necessary permissions for:
  - File system access (payslips folder, logs)
  - Database connectivity
  - Network access (email sending)

### File Permissions
- Ensure service account can read/write to:
  - Application directory
  - Payslips folder
  - Logs folder
  - Archive folder

## 🎯 Production Deployment

### Recommended Steps
1. **Test thoroughly** in development environment
2. **Create service account** with minimal required permissions
3. **Configure monitoring** and alerting
4. **Set up log rotation** and archiving
5. **Document recovery procedures**
6. **Schedule regular maintenance**

### Monitoring Recommendations
- Monitor service status
- Monitor log file sizes
- Monitor database connectivity
- Monitor email sending success rates
- Set up alerts for service failures

## 📞 Support

If you encounter issues:
1. Check the troubleshooting section above
2. Review log files for error details
3. Test the executable manually outside of service
4. Verify all configuration settings in .env file

---

**Note**: Always run batch files as Administrator for proper service management!
