@echo off
REM ============================================================================
REM PayslipSender Windows Service Management Script
REM ============================================================================
REM This script provides easy management of PayslipSender Windows service
REM Run as Administrator for service operations
REM ============================================================================

echo.
echo ========================================
echo PayslipSender Service Management
echo ========================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script must be run as Administrator!
    echo Right-click and select "Run as administrator"
    echo.
    pause
    exit /b 1
)

REM Set variables
set SERVICE_NAME=DSCPayslipSender
set CURRENT_DIR=%~dp0
set NSSM_PATH=%CURRENT_DIR%nssm.exe

:MAIN_MENU
cls
echo.
echo ========================================
echo PayslipSender Service Management
echo ========================================
echo.
echo Service Name: %SERVICE_NAME%
echo.

REM Check service status
sc query "%SERVICE_NAME%" >nul 2>&1
if %errorLevel% equ 0 (
    for /f "tokens=3" %%i in ('sc query "%SERVICE_NAME%" ^| find "STATE"') do set SERVICE_STATE=%%i
    echo Current Status: !SERVICE_STATE!
) else (
    echo Current Status: NOT INSTALLED
    set SERVICE_STATE=NOT_INSTALLED
)

echo.
echo Available Operations:
echo.
echo 1. Start Service
echo 2. Stop Service
echo 3. Restart Service
echo 4. View Service Status
echo 5. View Service Logs
echo 6. Edit Service Configuration
echo 7. Install Service (if not installed)
echo 8. Uninstall Service
echo 9. Test Service Executable
echo 0. Exit
echo.

set /p CHOICE="Select an option (0-9): "

if "%CHOICE%"=="1" goto START_SERVICE
if "%CHOICE%"=="2" goto STOP_SERVICE
if "%CHOICE%"=="3" goto RESTART_SERVICE
if "%CHOICE%"=="4" goto VIEW_STATUS
if "%CHOICE%"=="5" goto VIEW_LOGS
if "%CHOICE%"=="6" goto EDIT_CONFIG
if "%CHOICE%"=="7" goto INSTALL_SERVICE
if "%CHOICE%"=="8" goto UNINSTALL_SERVICE
if "%CHOICE%"=="9" goto TEST_EXECUTABLE
if "%CHOICE%"=="0" goto EXIT

echo Invalid choice. Please try again.
timeout /t 2 /nobreak >nul
goto MAIN_MENU

:START_SERVICE
echo.
echo Starting PayslipSender service...
sc start "%SERVICE_NAME%"
if %errorLevel% equ 0 (
    echo ✅ Service start command sent successfully
) else (
    echo ❌ Failed to start service
)
echo.
pause
goto MAIN_MENU

:STOP_SERVICE
echo.
echo Stopping PayslipSender service...
sc stop "%SERVICE_NAME%"
if %errorLevel% equ 0 (
    echo ✅ Service stop command sent successfully
) else (
    echo ❌ Failed to stop service
)
echo.
pause
goto MAIN_MENU

:RESTART_SERVICE
echo.
echo Restarting PayslipSender service...
echo Stopping service...
sc stop "%SERVICE_NAME%"
timeout /t 5 /nobreak >nul
echo Starting service...
sc start "%SERVICE_NAME%"
if %errorLevel% equ 0 (
    echo ✅ Service restarted successfully
) else (
    echo ❌ Failed to restart service
)
echo.
pause
goto MAIN_MENU

:VIEW_STATUS
echo.
echo ========================================
echo Service Status Details
echo ========================================
sc query "%SERVICE_NAME%"
echo.
echo ========================================
echo Service Configuration
echo ========================================
sc qc "%SERVICE_NAME%"
echo.
pause
goto MAIN_MENU

:VIEW_LOGS
echo.
echo ========================================
echo PayslipSender Logs
echo ========================================
echo.
echo Available log files:
echo.

if exist "%CURRENT_DIR%logs\payslip_sender.log" (
    echo 1. Application Log: logs\payslip_sender.log
) else (
    echo 1. Application Log: NOT FOUND
)

if exist "%CURRENT_DIR%logs\service_output.log" (
    echo 2. Service Output: logs\service_output.log
) else (
    echo 2. Service Output: NOT FOUND
)

if exist "%CURRENT_DIR%logs\service_error.log" (
    echo 3. Service Errors: logs\service_error.log
) else (
    echo 3. Service Errors: NOT FOUND
)

echo 4. Open logs folder
echo 0. Back to main menu
echo.

set /p LOG_CHOICE="Select log to view (0-4): "

if "%LOG_CHOICE%"=="1" (
    if exist "%CURRENT_DIR%logs\payslip_sender.log" (
        echo.
        echo Last 20 lines of application log:
        echo ========================================
        powershell "Get-Content '%CURRENT_DIR%logs\payslip_sender.log' -Tail 20"
    ) else (
        echo Application log not found
    )
)

if "%LOG_CHOICE%"=="2" (
    if exist "%CURRENT_DIR%logs\service_output.log" (
        echo.
        echo Last 20 lines of service output:
        echo ========================================
        powershell "Get-Content '%CURRENT_DIR%logs\service_output.log' -Tail 20"
    ) else (
        echo Service output log not found
    )
)

if "%LOG_CHOICE%"=="3" (
    if exist "%CURRENT_DIR%logs\service_error.log" (
        echo.
        echo Last 20 lines of service errors:
        echo ========================================
        powershell "Get-Content '%CURRENT_DIR%logs\service_error.log' -Tail 20"
    ) else (
        echo Service error log not found
    )
)

if "%LOG_CHOICE%"=="4" (
    if exist "%CURRENT_DIR%logs" (
        explorer "%CURRENT_DIR%logs"
    ) else (
        echo Logs folder not found
    )
)

if "%LOG_CHOICE%"=="0" goto MAIN_MENU

echo.
pause
goto MAIN_MENU

:EDIT_CONFIG
echo.
if exist "%NSSM_PATH%" (
    echo Opening NSSM service configuration...
    "%NSSM_PATH%" edit "%SERVICE_NAME%"
) else (
    echo NSSM not found. Cannot edit service configuration.
    echo You can modify service settings using:
    echo - Services.msc (Windows Services Manager)
    echo - Registry Editor (advanced users only)
)
echo.
pause
goto MAIN_MENU

:INSTALL_SERVICE
echo.
echo Launching service installation...
call "%CURRENT_DIR%install_service.bat"
echo.
pause
goto MAIN_MENU

:UNINSTALL_SERVICE
echo.
echo Launching service uninstallation...
call "%CURRENT_DIR%uninstall_service.bat"
echo.
pause
goto MAIN_MENU

:TEST_EXECUTABLE
echo.
echo Testing PayslipSender executable...
echo.
if exist "%CURRENT_DIR%dist\PayslipSender.exe" (
    echo Executable found: %CURRENT_DIR%dist\PayslipSender.exe
    echo.
    echo Running executable for 10 seconds (test mode)...
    echo Press Ctrl+C to stop early if needed
    echo.
    timeout /t 3 /nobreak >nul
    
    REM Run executable with timeout
    start /wait timeout /t 10 /nobreak
    "%CURRENT_DIR%dist\PayslipSender.exe" &
    timeout /t 10 /nobreak >nul
    taskkill /f /im PayslipSender.exe >nul 2>&1
    
    echo.
    echo Test completed. Check logs for any issues.
) else (
    echo ❌ Executable not found: %CURRENT_DIR%dist\PayslipSender.exe
    echo Please run 'python create_exe.py' first
)
echo.
pause
goto MAIN_MENU

:EXIT
echo.
echo Goodbye!
echo.
exit /b 0
