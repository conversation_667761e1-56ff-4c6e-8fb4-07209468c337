#!/usr/bin/env python3
"""
Test script to verify the bulletproof singleton implementation is working correctly.
"""

import os
import time
import subprocess
import threading
from pathlib import Path
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - TEST - %(message)s')
logger = logging.getLogger(__name__)

class SingletonTester:
    def __init__(self):
        self.test_results = []
        self.executable_path = Path("dist/PayslipSender.exe")
        self.script_path = Path("payslip_sender.py")
        
    def cleanup_lock_files(self):
        """Clean up any existing lock files."""
        lock_files = [
            "payslip_sender.lock",
            "payslipsender_singleton.lock",
            "PayslipSender_singleton.lock"
        ]
        
        for lock_file in lock_files:
            lock_path = Path(lock_file)
            if lock_path.exists():
                try:
                    lock_path.unlink()
                    logger.info(f"🧹 Removed existing lock file: {lock_file}")
                except Exception as e:
                    logger.warning(f"⚠️ Could not remove {lock_file}: {e}")
    
    def test_single_instance_startup(self):
        """Test that a single instance starts successfully."""
        logger.info("🧪 Test 1: Single instance startup")
        
        self.cleanup_lock_files()
        
        if self.executable_path.exists():
            cmd = [str(self.executable_path)]
        else:
            cmd = ["python", str(self.script_path)]
        
        try:
            # Start process and let it initialize
            process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            time.sleep(3)  # Let it initialize
            
            # Check if it's still running
            if process.poll() is None:
                logger.info("✅ Single instance started successfully")
                self.test_results.append("✅ Single instance startup: PASSED")
                
                # Terminate the process
                process.terminate()
                process.wait(timeout=5)
                return True
            else:
                stdout, stderr = process.communicate()
                logger.error(f"❌ Single instance failed to start")
                logger.error(f"STDOUT: {stdout}")
                logger.error(f"STDERR: {stderr}")
                self.test_results.append("❌ Single instance startup: FAILED")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error testing single instance: {e}")
            self.test_results.append(f"❌ Single instance startup: ERROR - {e}")
            return False
    
    def test_multiple_instance_prevention(self):
        """Test that multiple instances are prevented."""
        logger.info("🧪 Test 2: Multiple instance prevention")
        
        self.cleanup_lock_files()
        
        if self.executable_path.exists():
            cmd = [str(self.executable_path)]
        else:
            cmd = ["python", str(self.script_path)]
        
        try:
            # Start first instance
            logger.info("Starting first instance...")
            process1 = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            time.sleep(2)  # Let it initialize and acquire lock
            
            if process1.poll() is not None:
                stdout, stderr = process1.communicate()
                logger.error(f"❌ First instance failed to start")
                logger.error(f"STDOUT: {stdout}")
                logger.error(f"STDERR: {stderr}")
                self.test_results.append("❌ Multiple instance prevention: FAILED (first instance died)")
                return False
            
            # Try to start second instance
            logger.info("Attempting to start second instance...")
            process2 = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            
            # Wait for second instance to complete (should exit quickly)
            stdout2, stderr2 = process2.communicate(timeout=10)
            
            # Check if second instance was rejected
            if process2.returncode != 0:
                if "already running" in stdout2.lower() or "already running" in stderr2.lower():
                    logger.info("✅ Second instance was correctly rejected")
                    self.test_results.append("✅ Multiple instance prevention: PASSED")
                    result = True
                else:
                    logger.error(f"❌ Second instance failed for wrong reason")
                    logger.error(f"STDOUT: {stdout2}")
                    logger.error(f"STDERR: {stderr2}")
                    self.test_results.append("❌ Multiple instance prevention: FAILED (wrong error)")
                    result = False
            else:
                logger.error("❌ Second instance was allowed to start!")
                self.test_results.append("❌ Multiple instance prevention: FAILED (second instance started)")
                result = False
            
            # Clean up first instance
            process1.terminate()
            process1.wait(timeout=5)
            
            return result
            
        except Exception as e:
            logger.error(f"❌ Error testing multiple instance prevention: {e}")
            self.test_results.append(f"❌ Multiple instance prevention: ERROR - {e}")
            return False
    
    def test_rapid_startup_attempts(self):
        """Test rapid startup attempts to check for race conditions."""
        logger.info("🧪 Test 3: Rapid startup attempts (race condition test)")
        
        self.cleanup_lock_files()
        
        if self.executable_path.exists():
            cmd = [str(self.executable_path)]
        else:
            cmd = ["python", str(self.script_path)]
        
        def start_instance(instance_id):
            try:
                process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
                stdout, stderr = process.communicate(timeout=10)
                return {
                    'id': instance_id,
                    'returncode': process.returncode,
                    'stdout': stdout,
                    'stderr': stderr
                }
            except Exception as e:
                return {
                    'id': instance_id,
                    'error': str(e)
                }
        
        # Start 5 instances simultaneously
        threads = []
        results = []
        
        for i in range(5):
            thread = threading.Thread(target=lambda i=i: results.append(start_instance(i)))
            threads.append(thread)
        
        # Start all threads at once
        for thread in threads:
            thread.start()
        
        # Wait for all to complete
        for thread in threads:
            thread.join(timeout=15)
        
        # Analyze results
        successful_starts = 0
        rejected_starts = 0
        
        for result in results:
            if 'error' in result:
                logger.error(f"Instance {result['id']}: Error - {result['error']}")
            elif result['returncode'] == 0:
                successful_starts += 1
                logger.info(f"Instance {result['id']}: Started successfully")
            else:
                rejected_starts += 1
                if "already running" in result['stdout'].lower() or "already running" in result['stderr'].lower():
                    logger.info(f"Instance {result['id']}: Correctly rejected")
                else:
                    logger.warning(f"Instance {result['id']}: Rejected for other reason")
        
        if successful_starts == 1 and rejected_starts == 4:
            logger.info("✅ Rapid startup test passed - exactly 1 instance started, 4 rejected")
            self.test_results.append("✅ Rapid startup test: PASSED")
            return True
        else:
            logger.error(f"❌ Rapid startup test failed - {successful_starts} started, {rejected_starts} rejected")
            self.test_results.append(f"❌ Rapid startup test: FAILED ({successful_starts} started, {rejected_starts} rejected)")
            return False
    
    def run_all_tests(self):
        """Run all singleton tests."""
        logger.info("🚀 Starting PayslipSender Singleton Tests")
        logger.info("=" * 60)
        
        # Kill any existing processes
        try:
            subprocess.run(["taskkill", "/F", "/IM", "PayslipSender.exe"], 
                         capture_output=True, check=False)
        except:
            pass
        
        time.sleep(1)
        self.cleanup_lock_files()
        
        # Run tests
        test1_passed = self.test_single_instance_startup()
        time.sleep(2)
        
        test2_passed = self.test_multiple_instance_prevention()
        time.sleep(2)
        
        test3_passed = self.test_rapid_startup_attempts()
        
        # Summary
        logger.info("=" * 60)
        logger.info("🏁 TEST RESULTS SUMMARY")
        logger.info("=" * 60)
        
        for result in self.test_results:
            logger.info(result)
        
        all_passed = test1_passed and test2_passed and test3_passed
        
        if all_passed:
            logger.info("🎉 ALL TESTS PASSED - Singleton protection is working!")
            logger.info("✅ PayslipSender will now prevent duplicate email sending")
        else:
            logger.error("❌ SOME TESTS FAILED - Singleton protection needs fixing")
        
        return all_passed

if __name__ == "__main__":
    tester = SingletonTester()
    success = tester.run_all_tests()
    exit(0 if success else 1)
