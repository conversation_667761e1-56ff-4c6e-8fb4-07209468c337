# PayslipSender - Installation Checklist

## 📋 Pre-Installation Information Gathering

### Database Information
- [ ] **SQL Server Name**: ________________________________
- [ ] **Database Name**: ___________________________________
- [ ] **Username**: _______________________________________
- [ ] **Password**: _______________________________________
- [ ] **Employee Table Name**: _____________________________
- [ ] **Employee ID Column**: ______________________________
- [ ] **Employee Name Column**: ____________________________
- [ ] **Employee Email Column**: ___________________________

### Email Information  
- [ ] **Gmail Account**: ___________________________________
- [ ] **Gmail App Password**: ______________________________
- [ ] **Sender Name**: ____________________________________
- [ ] **Company Name**: ___________________________________

### System Information
- [ ] **Installation Path**: C:\PayslipSender (recommended)
- [ ] **Administrator Access**: Confirmed ✓
- [ ] **Internet Connection**: Available ✓

---

## 🔧 Installation Steps

### Step 1: Python Installation
- [ ] Downloaded Python from Microsoft Store or python.org
- [ ] Installed Python (checked "Add to PATH" if using python.org)
- [ ] Verified installation: `python --version` works
- [ ] Verified pip: `pip --version` works

### Step 2: PayslipSender Setup
- [ ] Created directory: `C:\PayslipSender`
- [ ] Copied all PayslipSender files to the directory
- [ ] Installed dependencies: `pip install -r requirements.txt`
- [ ] Created subdirectories:
  - [ ] `C:\PayslipSender\payslips`
  - [ ] `C:\PayslipSender\archived_payslips`  
  - [ ] `C:\PayslipSender\logs`

### Step 3: Configuration (.env file)
- [ ] Created `.env` file in `C:\PayslipSender\`
- [ ] Configured email settings (SMTP_USERNAME, SMTP_PASSWORD)
- [ ] Configured database settings (DATABASE_SERVER, DATABASE_NAME, etc.)
- [ ] Configured email template (EMAIL_FROM_NAME, EMAIL_SENDER_NAME)
- [ ] Configured file paths (WATCH_DIRECTORY, ARCHIVED_DIRECTORY)

### Step 4: Testing
- [ ] Ran database compatibility test: `python sql_server_compatibility.py`
- [ ] Database connection: ✅ Success / ❌ Failed
- [ ] Tested email configuration
- [ ] Email connection: ✅ Success / ❌ Failed
- [ ] Created test payslip file: `Payslip_[EmployeeID]_20250121.pdf`
- [ ] Ran manual test: `python payslip_sender.py`
- [ ] Test result: ✅ Success / ❌ Failed

### Step 5: NSSM Installation
- [ ] Downloaded NSSM from https://nssm.cc/download
- [ ] Extracted to `C:\nssm\`
- [ ] Added to PATH: `C:\nssm\win64`
- [ ] Verified installation: `nssm version` works

### Step 6: Service Installation
**Run as Administrator:**
- [ ] Installed service: `nssm install DSCPayslipSender ...`
- [ ] Configured service settings
- [ ] Started service: `nssm start DSCPayslipSender`
- [ ] Verified service status: `sc query DSCPayslipSender`
- [ ] Service status: ✅ Running / ❌ Stopped

### Step 7: Final Validation
- [ ] Placed test payslip in `payslips` folder
- [ ] File processed within 30 seconds
- [ ] File moved to `archived_payslips` folder
- [ ] Employee received email with payslip
- [ ] Log file shows success message
- [ ] Service restarts automatically after reboot

---

## ✅ Post-Installation Verification

### Service Verification
```cmd
# Check service status
sc query DSCPayslipSender
# Should show: STATE: 4 RUNNING

# Check service configuration  
nssm status DSCPayslipSender
# Should show: SERVICE_RUNNING
```

### File System Verification
- [ ] `C:\PayslipSender\payslips\` - Empty (ready for new files)
- [ ] `C:\PayslipSender\archived_payslips\` - Contains processed test file
- [ ] `C:\PayslipSender\logs\payslip_sender.log` - Contains recent log entries

### Database Verification
- [ ] Can connect to employee database
- [ ] Can read employee records
- [ ] `PayslipFailedLogs` table created (if needed)

### Email Verification
- [ ] Test email sent successfully
- [ ] Email format matches requirements:
  - [ ] From: "DSC Human Resources <<EMAIL>>"
  - [ ] Subject: "Payslip for [date] - [date]"
  - [ ] Body includes sender name and disclaimer
  - [ ] PDF attachment included

---

## 🚨 Troubleshooting Checklist

### If Service Won't Start:
- [ ] Check Python path: `where python`
- [ ] Verify file permissions on `C:\PayslipSender\`
- [ ] Check Windows Event Viewer for errors
- [ ] Try running manually: `python payslip_sender.py`

### If Database Connection Fails:
- [ ] Verify SQL Server is running
- [ ] Check server name and database name
- [ ] Test with SQL Server Management Studio
- [ ] Run: `python sql_server_compatibility.py`

### If Emails Don't Send:
- [ ] Verify Gmail App Password (16 characters, no spaces)
- [ ] Check 2-factor authentication is enabled
- [ ] Test internet connection
- [ ] Verify employee email addresses in database

### If Files Don't Process:
- [ ] Check filename format: `Payslip_EmployeeID_YYYYMMDD.pdf`
- [ ] Verify employee ID exists in database
- [ ] Check file permissions on payslips folder
- [ ] Review log file for specific errors

---

## 📞 Installation Support

### Information Needed for Support:
- [ ] **Error messages** from log files
- [ ] **Service status** output
- [ ] **Python version**: `python --version`
- [ ] **PyODBC version**: `pip show pyodbc`
- [ ] **Windows version**: `winver`

### Log File Locations:
- **Application Log**: `C:\PayslipSender\logs\payslip_sender.log`
- **Windows Event Log**: Event Viewer → Windows Logs → Application

### Diagnostic Commands:
```cmd
# System diagnostics
python --version
pip --version
nssm version
sc query DSCPayslipSender

# PayslipSender diagnostics
cd C:\PayslipSender
python sql_server_compatibility.py
```

---

## 📝 Installation Notes

### Installation Date: _______________

### Installed By: ____________________

### Configuration Notes:
```
Database Server: ________________________
Database Name: __________________________
Email Account: __________________________
Service Status: _________________________
```

### Test Results:
- **Database Connection**: ✅ / ❌
- **Email Connection**: ✅ / ❌  
- **File Processing**: ✅ / ❌
- **Service Installation**: ✅ / ❌

### Issues Encountered:
```
_________________________________________________
_________________________________________________
_________________________________________________
```

### Resolution:
```
_________________________________________________
_________________________________________________
_________________________________________________
```

---

## 🎯 Handover to HR Staff

### Training Completed:
- [ ] **Daily operations** (how to place payslip files)
- [ ] **File naming convention** explained
- [ ] **Log file monitoring** demonstrated
- [ ] **Service management** (start/stop/restart)
- [ ] **Basic troubleshooting** covered
- [ ] **Emergency contacts** provided

### Documentation Provided:
- [ ] `HR_DEPLOYMENT_GUIDE.md` - Complete deployment guide
- [ ] `QUICK_REFERENCE.md` - Daily operations reference
- [ ] `INSTALLATION_CHECKLIST.md` - This checklist
- [ ] Contact information for technical support

### Sign-off:
**Installer**: _________________________ Date: _________

**HR Manager**: _______________________ Date: _________

**IT Administrator**: __________________ Date: _________

---

**✅ Installation Complete - PayslipSender is ready for production use!**
