#!/usr/bin/env python3
"""
SQL Server Compatibility Checker and Configuration Helper
Helps diagnose and configure PayslipSender for different SQL Server versions.
"""

import pyodbc
import sys
import os
from dotenv import load_dotenv

def check_pyodbc_version():
    """Check pyodbc version and compatibility."""
    print("🔍 PyODBC Version Check")
    print("=" * 50)
    
    try:
        print(f"PyODBC Version: {pyodbc.version}")
        
        # Check Python version compatibility
        python_version = sys.version_info
        print(f"Python Version: {python_version.major}.{python_version.minor}.{python_version.micro}")
        
        if python_version >= (3, 13) and pyodbc.version < '5.0.0':
            print("⚠️  WARNING: PyODBC version may be incompatible with Python 3.13+")
            print("   Recommended: pip install --upgrade pyodbc>=5.0.0")
        else:
            print("✅ PyODBC version appears compatible")
            
    except Exception as e:
        print(f"❌ Error checking PyODBC: {e}")

def list_available_drivers():
    """List all available ODBC drivers."""
    print("\n🔌 Available ODBC Drivers")
    print("=" * 50)
    
    try:
        drivers = pyodbc.drivers()
        sql_server_drivers = []
        other_drivers = []
        
        for driver in drivers:
            if "SQL Server" in driver:
                sql_server_drivers.append(driver)
            else:
                other_drivers.append(driver)
        
        print("📊 SQL Server Drivers:")
        if sql_server_drivers:
            for i, driver in enumerate(sql_server_drivers, 1):
                print(f"  {i}. {driver}")
        else:
            print("  ❌ No SQL Server drivers found!")
        
        print(f"\n📋 Other Drivers ({len(other_drivers)}):")
        for driver in other_drivers[:5]:  # Show first 5
            print(f"  - {driver}")
        if len(other_drivers) > 5:
            print(f"  ... and {len(other_drivers) - 5} more")
            
        return sql_server_drivers
        
    except Exception as e:
        print(f"❌ Error listing drivers: {e}")
        return []

def recommend_driver(sql_server_drivers):
    """Recommend the best driver to use."""
    print("\n💡 Driver Recommendations")
    print("=" * 50)
    
    if not sql_server_drivers:
        print("❌ No SQL Server drivers available!")
        print("\n📥 Installation Options:")
        print("1. Download from: https://docs.microsoft.com/en-us/sql/connect/odbc/download-odbc-driver-for-sql-server")
        print("2. Install SQL Server Management Studio (includes drivers)")
        print("3. Install SQL Server Express (includes drivers)")
        return None
    
    # Driver preference order (newest to oldest)
    preferred_order = [
        "ODBC Driver 18 for SQL Server",
        "ODBC Driver 17 for SQL Server", 
        "ODBC Driver 13 for SQL Server",
        "ODBC Driver 11 for SQL Server",
        "SQL Server Native Client 11.0",
        "SQL Server Native Client 10.0",
        "SQL Server"
    ]
    
    recommended = None
    for preferred in preferred_order:
        if preferred in sql_server_drivers:
            recommended = preferred
            break
    
    if not recommended:
        recommended = sql_server_drivers[0]  # Use first available
    
    print(f"✅ Recommended Driver: {recommended}")
    
    # Version-specific notes
    if "18" in recommended:
        print("📝 Notes for ODBC Driver 18:")
        print("  - Latest driver with best security features")
        print("  - Requires TrustServerCertificate=yes for local connections")
        print("  - Compatible with SQL Server 2008+ and Azure SQL")
    elif "17" in recommended:
        print("📝 Notes for ODBC Driver 17:")
        print("  - Stable and widely compatible")
        print("  - Good for SQL Server 2008+ and Azure SQL")
    elif "Native Client" in recommended:
        print("📝 Notes for SQL Server Native Client:")
        print("  - Older driver, may have limitations")
        print("  - Consider upgrading to newer ODBC driver")
    
    return recommended

def test_connection_with_driver(driver_name):
    """Test database connection with specific driver."""
    print(f"\n🧪 Testing Connection with: {driver_name}")
    print("=" * 50)
    
    load_dotenv()
    
    server = os.getenv('DATABASE_SERVER', 'localhost')
    port = os.getenv('DATABASE_PORT', '1433')
    database = os.getenv('DATABASE_NAME', '')
    username = os.getenv('DATABASE_USERNAME', '')
    password = os.getenv('DATABASE_PASSWORD', '')
    trusted = os.getenv('DATABASE_TRUSTED_CONNECTION', 'no').lower() == 'yes'
    
    if trusted:
        conn_str = f"DRIVER={{{driver_name}}};SERVER={server},{port};DATABASE={database};Trusted_Connection=yes;TrustServerCertificate=yes;"
    else:
        conn_str = f"DRIVER={{{driver_name}}};SERVER={server},{port};DATABASE={database};UID={username};PWD={password};TrustServerCertificate=yes;"
    
    try:
        print(f"Connection String: {conn_str[:80]}...")
        
        with pyodbc.connect(conn_str, timeout=10) as conn:
            cursor = conn.cursor()
            
            # Test basic connection
            cursor.execute("SELECT @@VERSION, @@SERVERNAME")
            version, server_name = cursor.fetchone()
            
            print("✅ Connection Successful!")
            print(f"📊 Server: {server_name}")
            print(f"🔢 Version: {version[:60]}...")
            
            # Test database access
            cursor.execute("SELECT DB_NAME()")
            db_name = cursor.fetchone()[0]
            print(f"💾 Database: {db_name}")
            
            return True
            
    except Exception as e:
        print(f"❌ Connection Failed: {e}")
        
        # Provide troubleshooting tips
        if "TCP Provider" in str(e):
            print("💡 Troubleshooting: Network/Server issue")
            print("  - Check server name and port")
            print("  - Verify SQL Server is running")
            print("  - Check firewall settings")
        elif "Login failed" in str(e):
            print("💡 Troubleshooting: Authentication issue")
            print("  - Check username/password")
            print("  - Try Windows Authentication (TRUSTED_CONNECTION=yes)")
        elif "driver" in str(e).lower():
            print("💡 Troubleshooting: Driver issue")
            print("  - Try a different ODBC driver")
            print("  - Reinstall ODBC driver")
        
        return False

def generate_config_recommendation(recommended_driver):
    """Generate .env configuration recommendation."""
    print(f"\n⚙️  Configuration Recommendation")
    print("=" * 50)
    
    print("Add this to your .env file:")
    print(f"ODBC_DRIVER={recommended_driver}")
    print()
    print("Or update your requirements.txt based on your SQL Server version:")
    
    if "18" in recommended_driver or "17" in recommended_driver:
        print("# For newer SQL Server (2016+) with latest drivers")
        print("pyodbc>=5.0.0")
    elif "13" in recommended_driver or "11" in recommended_driver:
        print("# For SQL Server 2012-2016")
        print("pyodbc>=4.0.30,<6.0.0")
    else:
        print("# For older SQL Server versions")
        print("pyodbc>=4.0.24,<5.0.0")

def main():
    """Main compatibility checker."""
    print("🔧 SQL Server Compatibility Checker for PayslipSender")
    print("=" * 60)
    
    # Check PyODBC version
    check_pyodbc_version()
    
    # List available drivers
    sql_server_drivers = list_available_drivers()
    
    # Recommend best driver
    recommended_driver = recommend_driver(sql_server_drivers)
    
    if recommended_driver:
        # Test connection
        success = test_connection_with_driver(recommended_driver)
        
        if success:
            print("\n🎉 SUCCESS! Your system is ready for PayslipSender")
        
        # Generate configuration recommendation
        generate_config_recommendation(recommended_driver)
    
    print(f"\n" + "=" * 60)
    print("💡 Next Steps:")
    print("1. Update your .env file with the recommended ODBC_DRIVER")
    print("2. Update requirements.txt if needed")
    print("3. Run: pip install -r requirements.txt")
    print("4. Test PayslipSender: python payslip_sender.py")

if __name__ == "__main__":
    main()
