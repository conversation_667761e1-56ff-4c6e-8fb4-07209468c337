@echo off
REM ============================================================================
REM PayslipSender Windows Service Unregistration
REM ============================================================================
REM This script removes PayslipSender Windows service using SC command
REM Run as Administrator
REM ============================================================================

echo.
echo ========================================
echo PayslipSender Service Unregistration
echo ========================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script must be run as Administrator!
    echo Right-click and select "Run as administrator"
    echo.
    pause
    exit /b 1
)

REM Set variables
set SERVICE_NAME=DSCPayslipSender

echo Service Name: %SERVICE_NAME%
echo.

REM Check if service exists
echo Checking if service exists...
sc query "%SERVICE_NAME%" >nul 2>&1
if %errorLevel% neq 0 (
    echo Service "%SERVICE_NAME%" is not registered
    echo Nothing to unregister
    echo.
    pause
    exit /b 0
)

echo Service found: %SERVICE_NAME%
echo.

REM Show current service status
echo Current service status:
sc query "%SERVICE_NAME%"
echo.

REM Confirm removal
set /p CONFIRM="Are you sure you want to remove the PayslipSender service? (Y/N): "
if /i not "%CONFIRM%"=="Y" (
    echo Unregistration cancelled
    echo.
    pause
    exit /b 0
)

echo.
echo Unregistering PayslipSender service...

REM Stop the service first
echo Stopping service...
sc stop "%SERVICE_NAME%"
if %errorLevel% equ 0 (
    echo Service stop command sent
    echo Waiting for service to stop...
    timeout /t 5 /nobreak >nul
) else (
    echo Service was not running or already stopped
)

REM Remove the service
echo Removing service...
sc delete "%SERVICE_NAME%"

if %errorLevel% equ 0 (
    echo.
    echo ✅ PayslipSender service removed successfully!
    echo.
    echo The service has been completely unregistered from Windows.
    echo The executable and log files remain in the application folder.
    echo.
) else (
    echo.
    echo ❌ Failed to remove service
    echo.
    echo The service may still be running or in use.
    echo Try stopping all PayslipSender processes and run this script again.
    echo.
    echo Manual removal steps:
    echo 1. Open Task Manager and end any PayslipSender.exe processes
    echo 2. Open Services.msc and stop the service manually
    echo 3. Run this script again
    echo.
)

echo ========================================
echo Unregistration Complete
echo ========================================
echo.

pause
