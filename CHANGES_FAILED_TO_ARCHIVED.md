# Changes: Failed Files Moved to Archived Directory

## Summary
Modified the PayslipSender system so that all failed or invalid files are moved to the archived directory instead of being left in the watch directory or moved to a separate error directory.

## Key Changes Made

### 1. Enhanced `move_to_archived()` Method
- **File**: `payslip_sender.py` (lines 825-864)
- **Changes**:
  - Added `is_failed` parameter to distinguish between successful and failed files
  - Added `failure_reason` parameter to capture the reason for failure
  - Failed files are prefixed with `FAILED_YYYYMMDD_HHMMSS_` timestamp
  - Creates accompanying `_info.txt` files with failure details for failed files
  - Successful files continue to be archived normally

### 2. Updated `process_payslip_file()` Method
- **File**: `payslip_sender.py` (lines 866-973)
- **Changes**:
  - All failure scenarios now call `move_to_archived()` with `is_failed=True`
  - Failures handled:
    - Database connection errors
    - Employee not found in database
    - Missing or invalid email addresses
    - Empty PDF files
    - File access errors
    - Email sending failures
    - Unexpected processing errors

### 3. Updated `scan_directory()` Method
- **File**: `payslip_sender.py` (lines 1056-1101)
- **Changes**:
  - Replaced `_move_to_error_folder()` calls with `move_to_archived()` calls
  - File validation failures now move files to archived directory
  - Invalid filename formats now move files to archived directory
  - File check errors now move files to archived directory

### 4. Updated `_process_single_file()` Method
- **File**: `payslip_sender.py` (lines 1133-1159)
- **Changes**:
  - Unexpected processing errors now move files to archived directory
  - Added existence check before attempting to move files

### 5. Removed Error Directory Functionality
- **File**: `payslip_sender.py`
- **Changes**:
  - Removed `error_directory` configuration and creation
  - Removed `_move_to_error_folder()` method entirely
  - Updated initialization comments to reflect new behavior

## New Behavior

### Successful Files
- Moved to `archived/` directory with original filename
- If filename conflicts exist, timestamp is added: `filename_YYYYMMDD_HHMMSS.pdf`

### Failed Files
- Moved to `archived/` directory with prefix: `FAILED_YYYYMMDD_HHMMSS_originalname.pdf`
- Accompanying info file created: `FAILED_YYYYMMDD_HHMMSS_originalname_info.txt`
- Info file contains:
  - Original filename
  - Failure timestamp
  - Detailed failure reason
  - File size information

## Failure Scenarios Handled

1. **Database Issues**:
   - Connection failures
   - Query errors
   - Employee not found

2. **Email Issues**:
   - Missing email addresses
   - Invalid email formats
   - SMTP sending failures

3. **File Issues**:
   - Invalid filename formats
   - Empty files
   - File access permissions
   - Corrupted PDF headers

4. **System Issues**:
   - Unexpected processing errors
   - File system errors

## Benefits

1. **Centralized Storage**: All processed files (successful and failed) are in one location
2. **Clear Identification**: Failed files are easily identifiable with `FAILED_` prefix
3. **Detailed Logging**: Info files provide comprehensive failure information
4. **No Abandoned Files**: No files are left in the watch directory
5. **Audit Trail**: Complete history of all file processing attempts
6. **Easy Recovery**: Failed files can be easily identified and reprocessed after fixing issues

## Testing

Created comprehensive tests in `test_simple_archived.py` that verify:
- Successful files are archived normally
- Failed files are archived with `FAILED_` prefix
- Info files are created with proper failure details
- All files are moved from source to archived directory
- No files are abandoned in the watch directory

## Environment Variables

No new environment variables required. The existing `ARCHIVED_DIRECTORY` setting is used for both successful and failed files.

## Backward Compatibility

- The `ERROR_DIRECTORY` environment variable is no longer used
- Existing successful file archiving behavior is unchanged
- Log messages have been updated to reflect the new behavior