#!/usr/bin/env python3
"""
Test version of PayslipSender with console output
"""

import os
from dotenv import load_dotenv
from payslip_sender import PayslipSender

# Load environment
load_dotenv()

# Override log settings to show console output
os.environ['LOG_TO_CONSOLE'] = 'true'
os.environ['LOG_LEVEL'] = 'INFO'

print("🚀 Starting PayslipSender test...")
print("Press Ctrl+C to stop")
print("=" * 50)

try:
    sender = PayslipSender()
    print("✅ PayslipSender initialized")
    
    print("🔍 Testing startup validation...")
    if sender._validate_startup():
        print("✅ Startup validation passed")
        
        print(f"📁 Monitoring: {sender.watch_directory}")
        print(f"📁 Archive to: {sender.archived_directory}")
        print("🔄 Service is running... (Press Ctrl+C to stop)")
        
        # Run for a short time to test
        import time
        for i in range(3):  # Run for 3 cycles instead of infinite
            print(f"🔍 Scanning directory... (cycle {i+1}/3)")
            sender.scan_directory()
            if i < 2:  # Don't sleep on last iteration
                print("⏳ Waiting 10 seconds...")
                time.sleep(10)
        
        print("✅ Test completed successfully!")
    else:
        print("❌ Startup validation failed")
        
except KeyboardInterrupt:
    print("\n🛑 Stopped by user")
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
