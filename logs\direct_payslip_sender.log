Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1301, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1329, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1319, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1408, in main
    print("\U0001f680 Starting PayslipSender with bulletproof singleton protection...")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 0: character maps to <undefined>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1436, in <module>
    main()
  File "C:\Users\<USER>\Desktop\PayslipSender\payslip_sender.py", line 1426, in main
    print(f"\u274c FATAL ERROR in PayslipSender: {e}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 0: character maps to <undefined>
