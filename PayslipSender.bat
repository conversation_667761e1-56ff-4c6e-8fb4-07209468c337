@echo off
REM PayslipSender Standalone Launcher
REM This batch file acts like an executable

REM Set working directory to the script location
cd /d "%~dp0"

REM Check if Python exists
if not exist "C:\Python312\python.exe" (
    echo ERROR: Python not found at C:\Python312\python.exe
    echo Please install Python 3.12 or update the path in this batch file
    pause
    exit /b 1
)

REM Check if script exists
if not exist "payslip_sender.py" (
    echo ERROR: payslip_sender.py not found in current directory
    echo Make sure this batch file is in the same folder as payslip_sender.py
    pause
    exit /b 1
)

REM Run the PayslipSender
echo Starting PayslipSender...
"C:\Python312\python.exe" payslip_sender.py

REM If script exits, show message
echo PayslipSender has stopped.
pause
