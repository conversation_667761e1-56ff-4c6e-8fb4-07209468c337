# PayslipSender Configuration Template
# Copy this file to .env and update with your settings

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# SQL Server connection details
DATABASE_SERVER=your-sql-server.company.com
DATABASE_PORT=1433
DATABASE_NAME=HRDatabase
DATABASE_USERNAME=hr_user
DATABASE_PASSWORD=your_secure_password

# Alternative: Use Windows Authentication (set to 'yes' to use current Windows user)
DATABASE_TRUSTED_CONNECTION=no

# Employee table configuration
EMPLOYEE_TABLE_NAME=Employees
EMPLOYEE_ID_COLUMN=EmployeeId
EMPLOYEE_NAME_COLUMN=FullName
EMPLOYEE_EMAIL_COLUMN=EmailAddress

# =============================================================================
# EMAIL CONFIGURATION
# =============================================================================

# SMTP server settings (Gmail example)
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_gmail_app_password
SMTP_USE_TLS=True

# Email template settings
EMAIL_SENDER_NAME=DSC Human Resources
EMAIL_SIGNATURE_NAME=Emily R. Planas

# =============================================================================
# FILE PROCESSING CONFIGURATION
# =============================================================================

# Directory settings (relative to application folder)
WATCH_DIRECTORY=./payslips
ARCHIVED_DIRECTORY=./archived_payslips

# Processing performance settings
SCAN_INTERVAL_SECONDS=1
MAX_WORKER_THREADS=5

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Log level: DEBUG, INFO, WARNING, ERROR
LOG_LEVEL=INFO
LOG_FILE=logs/payslip_sender.log
LOG_TO_CONSOLE=true

# =============================================================================
# ADVANCED SETTINGS (Optional)
# =============================================================================

# Email retry settings
EMAIL_RETRY_ATTEMPTS=3
EMAIL_RETRY_DELAY=5

# File processing timeout (seconds)
FILE_PROCESSING_TIMEOUT=30

# Database connection timeout (seconds)
DATABASE_TIMEOUT=30
