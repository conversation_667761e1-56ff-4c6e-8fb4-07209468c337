#!/usr/bin/env python3
"""
Rebuild PayslipSender executable without removing existing files
"""

import os
import sys
import subprocess
import shutil

def rebuild_executable():
    print("🚀 Rebuilding PayslipSender executable...")
    
    # Check if we're in the right directory
    if not os.path.exists('payslip_sender.py'):
        print("❌ Error: payslip_sender.py not found!")
        return False
    
    # Create the executable with improved configuration
    print("📦 Creating executable...")
    try:
        cmd = [
            sys.executable, '-m', 'pyinstaller',
            '--onefile',
            '--name', 'PayslipSender_New',
            '--distpath', 'dist',
            '--workpath', 'build_new',
            '--add-data', '.env;.',
            '--add-data', 'email_service.py;.',
            '--hidden-import', 'pyodbc',
            '--hidden-import', 'dotenv',
            '--hidden-import', 'email.mime.text',
            '--hidden-import', 'email.mime.multipart',
            '--hidden-import', 'email.mime.base',
            '--console',
            '--noupx',
            'payslip_sender.py'
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ New executable created successfully!")
            
            # Check if the new exe exists
            new_exe_path = os.path.join('dist', 'PayslipSender_New.exe')
            old_exe_path = os.path.join('dist', 'payslipsender.exe')
            final_exe_path = os.path.join('dist', 'PayslipSender.exe')
            
            if os.path.exists(new_exe_path):
                print(f"✅ New executable found: {new_exe_path}")
                print(f"📁 File size: {os.path.getsize(new_exe_path) / (1024*1024):.1f} MB")
                
                # Try to replace the old executable
                try:
                    if os.path.exists(old_exe_path):
                        os.remove(old_exe_path)
                        print("✅ Removed old payslipsender.exe")
                    
                    if os.path.exists(final_exe_path):
                        os.remove(final_exe_path)
                        print("✅ Removed old PayslipSender.exe")
                    
                    # Rename new executable
                    os.rename(new_exe_path, final_exe_path)
                    print("✅ New executable renamed to PayslipSender.exe")
                    
                except Exception as e:
                    print(f"⚠️  Could not replace old executable: {e}")
                    print(f"✅ New executable available as: {new_exe_path}")
                
                # Ensure .env file is in dist directory
                dist_env = os.path.join('dist', '.env')
                if not os.path.exists(dist_env):
                    shutil.copy2('.env', dist_env)
                    print("✅ .env file copied to dist directory")
                
                return True
            else:
                print("❌ New executable not found")
                return False
        else:
            print("❌ PyInstaller failed:")
            print("STDERR:", result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Error creating executable: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("PayslipSender Executable Rebuilder")
    print("=" * 60)
    
    if rebuild_executable():
        print("\n🎉 Rebuild completed successfully!")
        print("📁 Location: dist\\PayslipSender.exe")
        print("\n🧪 Test the new executable by running it from the dist folder")
    else:
        print("\n❌ Rebuild failed")
    
    input("\nPress Enter to exit...")