#!/usr/bin/env python3
"""
Comprehensive test script for PayslipSender error handling and duplicate prevention fixes.
"""

import os
import time
import shutil
from pathlib import Path
from datetime import datetime
import logging

# Setup logging for test
logging.basicConfig(level=logging.INFO, format='%(asctime)s - TEST - %(message)s')
logger = logging.getLogger(__name__)

class PayslipSenderTester:
    def __init__(self):
        self.test_dir = Path("test_payslips")
        self.error_dir = Path("error_payslips")
        self.archive_dir = Path("archived_payslips")
        
        # Create test directories
        self.test_dir.mkdir(exist_ok=True)
        self.error_dir.mkdir(exist_ok=True)
        self.archive_dir.mkdir(exist_ok=True)
        
        # Clean up any existing test files
        self.cleanup_test_files()
    
    def cleanup_test_files(self):
        """Clean up test files from previous runs."""
        for pattern in ["Test_*.pdf", "Invalid_*.pdf", "Payslip_*.pdf"]:
            for file in self.test_dir.glob(pattern):
                file.unlink()
            for file in self.error_dir.glob(pattern):
                file.unlink()
            for file in self.archive_dir.glob(pattern):
                file.unlink()
    
    def create_test_pdf(self, filename: str, content: str = "Valid PDF content", size_bytes: int = 1024):
        """Create a test PDF file."""
        file_path = self.test_dir / filename
        
        if size_bytes == 0:
            # Create empty file
            file_path.touch()
        else:
            # Create file with PDF header and content
            with open(file_path, 'wb') as f:
                f.write(b'%PDF-1.4\n')  # PDF header
                f.write(content.encode('utf-8'))
                # Pad to desired size
                remaining = size_bytes - len(content) - 8
                if remaining > 0:
                    f.write(b'X' * remaining)
        
        return file_path
    
    def test_invalid_filename_formats(self):
        """Test various invalid filename formats."""
        logger.info("🧪 Testing invalid filename formats...")
        
        invalid_files = [
            "payslip_501_20250125.pdf",  # Wrong case
            "Payslip-501-20250125.pdf",  # Wrong separators
            "Payslip_501_2025-01-25.pdf",  # Wrong date format
            "Payslip_501.pdf",  # Missing date
            "Payslip__20250125.pdf",  # Missing employee ID
            "NotAPayslip_501_20250125.pdf",  # Wrong prefix
            "Payslip_501_20250125.txt",  # Wrong extension
        ]
        
        for filename in invalid_files:
            self.create_test_pdf(filename)
            logger.info(f"Created invalid file: {filename}")
        
        return len(invalid_files)
    
    def test_corrupted_files(self):
        """Test corrupted and problematic PDF files."""
        logger.info("🧪 Testing corrupted and problematic files...")
        
        test_cases = [
            ("Payslip_501_20250125.pdf", "", 0),  # Empty file
            ("Payslip_502_20250125.pdf", "Not a PDF", 50),  # No PDF header
            ("Payslip_503_20250125.pdf", "Tiny file", 10),  # Very small file
        ]
        
        for filename, content, size in test_cases:
            self.create_test_pdf(filename, content, size)
            logger.info(f"Created problematic file: {filename} ({size} bytes)")
        
        return len(test_cases)
    
    def test_valid_files(self):
        """Create valid test files."""
        logger.info("🧪 Creating valid test files...")
        
        valid_files = [
            "Payslip_504_20250125.pdf",
            "Payslip_505_20250125.pdf",
            "Payslip_506_20250125.pdf",
        ]
        
        for filename in valid_files:
            self.create_test_pdf(filename, f"Valid payslip content for {filename}", 2048)
            logger.info(f"Created valid file: {filename}")
        
        return len(valid_files)
    
    def test_duplicate_prevention(self):
        """Test duplicate file prevention."""
        logger.info("🧪 Testing duplicate prevention...")
        
        # Create the same file multiple times rapidly
        filename = "Payslip_507_20250125.pdf"
        
        for i in range(5):
            file_path = self.create_test_pdf(f"duplicate_{i}_{filename}", f"Duplicate test {i}", 1024)
            # Rename to same filename to simulate rapid uploads
            target_path = self.test_dir / filename
            if target_path.exists():
                target_path.unlink()
            file_path.rename(target_path)
            logger.info(f"Created duplicate file attempt {i+1}")
            time.sleep(0.1)  # Small delay
        
        return 1  # Only one file should be processed
    
    def run_comprehensive_test(self):
        """Run all tests."""
        logger.info("🚀 Starting comprehensive PayslipSender test...")
        
        # Test different error scenarios
        invalid_count = self.test_invalid_filename_formats()
        corrupted_count = self.test_corrupted_files()
        valid_count = self.test_valid_files()
        duplicate_count = self.test_duplicate_prevention()
        
        total_files = invalid_count + corrupted_count + valid_count + duplicate_count
        
        logger.info(f"📊 Test Summary:")
        logger.info(f"   Invalid filename formats: {invalid_count}")
        logger.info(f"   Corrupted/problematic files: {corrupted_count}")
        logger.info(f"   Valid files: {valid_count}")
        logger.info(f"   Duplicate test files: {duplicate_count}")
        logger.info(f"   Total test files created: {total_files}")
        
        logger.info("🎯 Expected Results:")
        logger.info(f"   - {invalid_count + corrupted_count} files should be moved to error folder")
        logger.info(f"   - {valid_count} files should be processed successfully (if employees exist)")
        logger.info(f"   - Only 1 email should be sent for the duplicate test")
        logger.info(f"   - All errors should be logged to FailedToSendLogs table")
        
        logger.info("✅ Test files created. Now run PayslipSender to see results!")
        logger.info("📁 Check these folders after running:")
        logger.info(f"   - Error files: {self.error_dir}")
        logger.info(f"   - Archived files: {self.archive_dir}")
        logger.info(f"   - Logs: logs/payslip_sender.log")
        
        return total_files

if __name__ == "__main__":
    tester = PayslipSenderTester()
    tester.run_comprehensive_test()
