#!/usr/bin/env python3
"""
Improved script to create PayslipSender executable with better configuration
"""

import os
import sys
import subprocess
import shutil

def create_improved_executable():
    print("🚀 Creating improved PayslipSender executable...")
    
    # Check if we're in the right directory
    if not os.path.exists('payslip_sender.py'):
        print("❌ Error: payslip_sender.py not found!")
        print("Make sure you're running this from the PayslipSender directory")
        return False
    
    # Check required files
    required_files = ['payslip_sender.py', 'email_service.py', '.env']
    for file in required_files:
        if not os.path.exists(file):
            print(f"❌ Error: {file} not found!")
            return False
    
    # Try to import PyInstaller
    try:
        import PyInstaller
        print(f"✅ PyInstaller found: version {PyInstaller.__version__}")
    except ImportError:
        print("❌ PyInstaller not found. Installing...")
        try:
            subprocess.run([sys.executable, '-m', 'pip', 'install', 'pyinstaller'], check=True)
            print("✅ PyInstaller installed successfully")
        except subprocess.CalledProcessError:
            print("❌ Failed to install PyInstaller")
            return False
    
    # Clean previous builds
    if os.path.exists('dist'):
        print("🧹 Cleaning previous build...")
        shutil.rmtree('dist')
    if os.path.exists('build'):
        shutil.rmtree('build')
    
    # Create the executable with improved configuration
    print("📦 Creating executable with improved configuration...")
    try:
        cmd = [
            sys.executable, '-m', 'pyinstaller',
            '--onefile',
            '--name', 'PayslipSender',
            '--distpath', 'dist',
            '--workpath', 'build',
            '--add-data', '.env;.',
            '--add-data', 'email_service.py;.',
            '--hidden-import', 'pyodbc',
            '--hidden-import', 'dotenv',
            '--hidden-import', 'email.mime.text',
            '--hidden-import', 'email.mime.multipart',
            '--hidden-import', 'email.mime.base',
            '--console',
            '--noupx',  # Disable UPX compression for better compatibility
            'payslip_sender.py'
        ]
        
        print("Running command:", ' '.join(cmd))
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Executable created successfully!")
            
            # Check if the exe exists
            exe_path = os.path.join('dist', 'PayslipSender.exe')
            if os.path.exists(exe_path):
                print(f"✅ PayslipSender.exe found at: {exe_path}")
                print(f"📁 File size: {os.path.getsize(exe_path) / (1024*1024):.1f} MB")
                
                # Copy .env file to dist directory
                dist_env = os.path.join('dist', '.env')
                if not os.path.exists(dist_env):
                    shutil.copy2('.env', dist_env)
                    print("✅ .env file copied to dist directory")
                
                return True
            else:
                print("❌ Executable not found in dist folder")
                return False
        else:
            print("❌ PyInstaller failed:")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Error creating executable: {e}")
        return False

def create_launcher_script():
    """Create a launcher script that ensures proper working directory"""
    launcher_content = '''@echo off
REM PayslipSender Launcher - Ensures proper working directory
REM This script ensures the executable runs from the correct directory

REM Get the directory where this batch file is located
set "SCRIPT_DIR=%~dp0"

REM Change to the script directory
cd /d "%SCRIPT_DIR%"

REM Check if PayslipSender.exe exists
if not exist "PayslipSender.exe" (
    echo ERROR: PayslipSender.exe not found in current directory
    echo Make sure this batch file is in the same folder as PayslipSender.exe
    pause
    exit /b 1
)

REM Check if .env file exists
if not exist ".env" (
    echo ERROR: .env file not found in current directory
    echo Make sure the .env configuration file is present
    pause
    exit /b 1
)

REM Run the PayslipSender executable
echo Starting PayslipSender from directory: %CD%
echo Monitoring for payslip files...
echo Press Ctrl+C to stop the service
echo.
PayslipSender.exe

REM If executable exits, show message
echo.
echo PayslipSender has stopped.
pause
'''
    
    launcher_path = os.path.join('dist', 'StartPayslipSender.bat')
    with open(launcher_path, 'w') as f:
        f.write(launcher_content)
    
    print(f"✅ Launcher script created: {launcher_path}")

def test_executable():
    """Test the executable quickly"""
    exe_path = os.path.join('dist', 'PayslipSender.exe')
    if not os.path.exists(exe_path):
        print("❌ Executable not found for testing")
        return False
    
    print("🧪 Testing executable (will run for 5 seconds)...")
    try:
        # Run the executable for a short time to test initialization
        process = subprocess.Popen([exe_path], cwd='dist')
        import time
        time.sleep(5)  # Let it run for 5 seconds
        process.terminate()
        process.wait(timeout=5)
        print("✅ Executable test completed successfully")
        return True
    except Exception as e:
        print(f"⚠️  Executable test had issues: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("PayslipSender Improved Executable Creator")
    print("=" * 60)
    
    if create_improved_executable():
        create_launcher_script()
        
        print("\n" + "=" * 60)
        test_choice = input("Test the executable? (y/n): ").lower()
        if test_choice == 'y':
            test_executable()
        
        print("\n🎉 Done! Your improved PayslipSender.exe is ready!")
        print("📁 Location: dist\\PayslipSender.exe")
        print("🚀 Launcher: dist\\StartPayslipSender.bat")
        print("\n📋 To use:")
        print("1. Navigate to the dist folder")
        print("2. Double-click StartPayslipSender.bat to run")
        print("3. The service will monitor for PDF files automatically")
        print("4. Check logs in the logs folder for status")
    else:
        print("\n❌ Failed to create executable")
    
    input("\nPress Enter to exit...")