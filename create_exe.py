#!/usr/bin/env python3
"""
Script to create PayslipSender executable
"""

import os
import sys
import subprocess

def create_executable():
    print("🚀 Creating PayslipSender executable...")
    
    # Check if we're in the right directory
    if not os.path.exists('payslip_sender.py'):
        print("❌ Error: payslip_sender.py not found!")
        print("Make sure you're running this from the PayslipSender directory")
        return False
    
    # Try to import PyInstaller
    try:
        import PyInstaller
        print(f"✅ PyInstaller found: version {PyInstaller.__version__}")
    except ImportError:
        print("❌ PyInstaller not found. Installing...")
        try:
            subprocess.run([sys.executable, '-m', 'pip', 'install', 'pyinstaller'], check=True)
            print("✅ PyInstaller installed successfully")
        except subprocess.CalledProcessError:
            print("❌ Failed to install PyInstaller")
            return False
    
    # Create the executable
    print("📦 Creating executable...")
    try:
        cmd = [
            sys.executable, '-m', 'pyinstaller',
            '--onefile',
            '--name', 'PayslipSender',
            '--distpath', 'dist',
            '--workpath', 'build',
            'payslip_sender.py'
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Executable created successfully!")
            
            # Check if the exe exists
            exe_path = os.path.join('dist', 'PayslipSender.exe')
            if os.path.exists(exe_path):
                print(f"✅ PayslipSender.exe found at: {exe_path}")
                print(f"📁 File size: {os.path.getsize(exe_path) / (1024*1024):.1f} MB")
                return True
            else:
                print("❌ Executable not found in dist folder")
                return False
        else:
            print("❌ PyInstaller failed:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Error creating executable: {e}")
        return False

def setup_autostart():
    """Setup the executable to start automatically"""
    exe_path = os.path.join('dist', 'PayslipSender.exe')
    if not os.path.exists(exe_path):
        print("❌ Executable not found. Create it first.")
        return False
    
    startup_folder = os.path.expandvars(r'%APPDATA%\Microsoft\Windows\Start Menu\Programs\Startup')
    
    try:
        # Copy executable to startup folder
        import shutil
        startup_exe = os.path.join(startup_folder, 'PayslipSender.exe')
        shutil.copy2(exe_path, startup_exe)
        
        # Copy .env file too
        if os.path.exists('.env'):
            startup_env = os.path.join(startup_folder, '.env')
            shutil.copy2('.env', startup_env)
        
        print(f"✅ PayslipSender.exe copied to startup folder")
        print(f"📁 Location: {startup_exe}")
        print("🚀 PayslipSender will now start automatically with Windows!")
        return True
        
    except Exception as e:
        print(f"❌ Error setting up autostart: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("PayslipSender Executable Creator")
    print("=" * 60)
    
    if create_executable():
        print("\n" + "=" * 60)
        setup_choice = input("Setup auto-start with Windows? (y/n): ").lower()
        if setup_choice == 'y':
            setup_autostart()
        
        print("\n🎉 Done! Your PayslipSender.exe is ready!")
        print("📁 Location: dist\\PayslipSender.exe")
        print("🧪 Test it by running: dist\\PayslipSender.exe")
    else:
        print("\n❌ Failed to create executable")
    
    input("\nPress Enter to exit...")
