#!/usr/bin/env python3
"""
Script to create PayslipSender executable
"""

import os
import sys
import subprocess

def create_executable():
    print("🚀 Creating PayslipSender executable...")
    
    # Check if we're in the right directory
    if not os.path.exists('payslip_sender.py'):
        print("❌ Error: payslip_sender.py not found!")
        print("Make sure you're running this from the PayslipSender directory")
        return False
    
    # Try to import PyInstaller
    try:
        import PyInstaller
        print(f"✅ PyInstaller found: version {PyInstaller.__version__}")
    except ImportError:
        print("❌ PyInstaller not found. Installing...")
        try:
            subprocess.run([sys.executable, '-m', 'pip', 'install', 'pyinstaller'], check=True)
            print("✅ PyInstaller installed successfully")
        except subprocess.CalledProcessError:
            print("❌ Failed to install PyInstaller")
            return False
    
    # Create the executable
    print("📦 Creating executable...")
    try:
        # Try multiple ways to call PyInstaller
        pyinstaller_commands = [
            # Method 1: Direct pyinstaller command (windowless)
            ['pyinstaller', '--onefile', '--noconsole', '--name', 'PayslipSender', 'payslip_sender.py'],
            # Method 2: Python module (windowless)
            [sys.executable, '-m', 'pyinstaller', '--onefile', '--noconsole', '--name', 'PayslipSender', 'payslip_sender.py'],
            # Method 3: Scripts directory (windowless)
            [r'C:\Python312\Scripts\pyinstaller.exe', '--onefile', '--noconsole', '--name', 'PayslipSender', 'payslip_sender.py'],
            # Method 4: Direct import and run (windowless)
            [sys.executable, '-c', 'import PyInstaller.__main__; PyInstaller.__main__.run()', '--onefile', '--noconsole', '--name', 'PayslipSender', 'payslip_sender.py']
        ]

        for i, cmd in enumerate(pyinstaller_commands, 1):
            print(f"🔄 Trying method {i}: {' '.join(cmd[:2])}")
            try:
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)

                if result.returncode == 0:
                    print("✅ Executable created successfully!")

                    # Check if the exe exists
                    exe_path = os.path.join('dist', 'PayslipSender.exe')
                    if os.path.exists(exe_path):
                        print(f"✅ PayslipSender.exe found at: {exe_path}")
                        print(f"📁 File size: {os.path.getsize(exe_path) / (1024*1024):.1f} MB")
                        return True
                    else:
                        print("❌ Executable not found in dist folder")
                        continue
                else:
                    print(f"❌ Method {i} failed: {result.stderr[:200]}")
                    continue

            except FileNotFoundError:
                print(f"❌ Method {i} - Command not found")
                continue
            except subprocess.TimeoutExpired:
                print(f"❌ Method {i} - Timeout")
                continue
            except Exception as e:
                print(f"❌ Method {i} - Error: {e}")
                continue

        # Final fallback - create a spec file and try manual approach
        print("🔄 Trying manual PyInstaller approach...")
        try:
            # Create a simple spec file
            spec_content = '''
# -*- mode: python ; coding: utf-8 -*-

a = Analysis(
    ['payslip_sender.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=['pyodbc', 'dotenv', 'watchdog'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='PayslipSender',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
            with open('PayslipSender.spec', 'w') as f:
                f.write(spec_content)

            # Try to run with spec file
            import PyInstaller.__main__
            PyInstaller.__main__.run(['PayslipSender.spec'])

            # Check if exe was created
            exe_path = os.path.join('dist', 'PayslipSender.exe')
            if os.path.exists(exe_path):
                print("✅ Manual method succeeded!")
                print(f"✅ PayslipSender.exe found at: {exe_path}")
                print(f"📁 File size: {os.path.getsize(exe_path) / (1024*1024):.1f} MB")
                return True

        except Exception as e:
            print(f"❌ Manual method failed: {e}")

        print("❌ All PyInstaller methods failed")
        return False

    except Exception as e:
        print(f"❌ Error creating executable: {e}")
        return False

def setup_autostart():
    """Setup the executable to start automatically"""
    exe_path = os.path.join('dist', 'PayslipSender.exe')
    if not os.path.exists(exe_path):
        print("❌ Executable not found. Create it first.")
        return False
    
    startup_folder = os.path.expandvars(r'%APPDATA%\Microsoft\Windows\Start Menu\Programs\Startup')
    
    try:
        # Copy executable to startup folder
        import shutil
        startup_exe = os.path.join(startup_folder, 'PayslipSender.exe')
        shutil.copy2(exe_path, startup_exe)
        
        # Copy .env file too
        if os.path.exists('.env'):
            startup_env = os.path.join(startup_folder, '.env')
            shutil.copy2('.env', startup_env)
        
        print(f"✅ PayslipSender.exe copied to startup folder")
        print(f"📁 Location: {startup_exe}")
        print("🚀 PayslipSender will now start automatically with Windows!")
        return True
        
    except Exception as e:
        print(f"❌ Error setting up autostart: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("PayslipSender Executable Creator")
    print("=" * 60)
    
    if create_executable():
        print("\n" + "=" * 60)
        setup_choice = input("Setup auto-start with Windows? (y/n): ").lower()
        if setup_choice == 'y':
            setup_autostart()
        
        print("\n🎉 Done! Your PayslipSender.exe is ready!")
        print("📁 Location: dist\\PayslipSender.exe")
        print("🧪 Test it by running: dist\\PayslipSender.exe")
    else:
        print("\n❌ Failed to create executable")
    
    input("\nPress Enter to exit...")
