# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['payslip_sender.py'],
    pathex=[],
    binaries=[],
    datas=[('.env', '.'), ('email_service.py', '.')],
    hiddenimports=['pyodbc', 'dotenv', 'email.mime.text', 'email.mime.multipart', 'email.mime.base'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='payslipsender',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
