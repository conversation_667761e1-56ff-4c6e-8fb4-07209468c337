@echo off
REM ============================================================================
REM PayslipSender Windows Service Uninstallation Script
REM ============================================================================
REM This script removes PayslipSender Windows service
REM Run as Administrator for proper service removal
REM ============================================================================

echo.
echo ========================================
echo PayslipSender Service Uninstallation
echo ========================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script must be run as Administrator!
    echo Right-click and select "Run as administrator"
    echo.
    pause
    exit /b 1
)

REM Set variables
set SERVICE_NAME=DSCPayslipSender
set CURRENT_DIR=%~dp0
set NSSM_PATH=%CURRENT_DIR%nssm.exe

echo Service Name: %SERVICE_NAME%
echo Current Directory: %CURRENT_DIR%
echo.

REM Check if NSSM exists
if not exist "%NSSM_PATH%" (
    echo WARNING: NSSM not found at: %NSSM_PATH%
    echo Attempting to remove service using Windows SC command...
    echo.
    
    REM Try to stop and remove service using sc command
    sc query "%SERVICE_NAME%" >nul 2>&1
    if %errorLevel% equ 0 (
        echo Stopping service...
        sc stop "%SERVICE_NAME%"
        timeout /t 5 /nobreak >nul
        
        echo Removing service...
        sc delete "%SERVICE_NAME%"
        
        if %errorLevel% equ 0 (
            echo ✅ Service removed successfully using SC command
        ) else (
            echo ❌ Failed to remove service using SC command
        )
    ) else (
        echo Service "%SERVICE_NAME%" not found
    )
    
    echo.
    pause
    exit /b 0
)

REM Check if service exists
echo Checking if service exists...
sc query "%SERVICE_NAME%" >nul 2>&1
if %errorLevel% neq 0 (
    echo Service "%SERVICE_NAME%" is not installed
    echo Nothing to uninstall
    echo.
    pause
    exit /b 0
)

echo Service found: %SERVICE_NAME%
echo.

REM Confirm uninstallation
set /p CONFIRM="Are you sure you want to remove the PayslipSender service? (Y/N): "
if /i not "%CONFIRM%"=="Y" (
    echo Uninstallation cancelled
    echo.
    pause
    exit /b 0
)

echo.
echo Uninstalling PayslipSender service...
echo.

REM Stop the service first
echo Stopping service...
"%NSSM_PATH%" stop "%SERVICE_NAME%"
timeout /t 5 /nobreak >nul

REM Remove the service
echo Removing service...
"%NSSM_PATH%" remove "%SERVICE_NAME%" confirm

if %errorLevel% equ 0 (
    echo.
    echo ✅ PayslipSender service removed successfully!
    echo.
    echo The service has been completely uninstalled from Windows.
    echo.
    echo Note: Log files and application files are still present:
    echo - Application: %CURRENT_DIR%
    echo - Logs: %CURRENT_DIR%logs\
    echo.
    set /p DELETE_LOGS="Do you want to delete log files? (Y/N): "
    if /i "!DELETE_LOGS!"=="Y" (
        if exist "%CURRENT_DIR%logs" (
            echo Deleting log files...
            rmdir /s /q "%CURRENT_DIR%logs"
            echo Log files deleted
        )
    )
) else (
    echo.
    echo ❌ Failed to remove service
    echo.
    echo You can try manual removal:
    echo 1. Open Services.msc
    echo 2. Find "%SERVICE_NAME%"
    echo 3. Stop and delete the service
    echo.
    echo Or try: sc delete "%SERVICE_NAME%"
)

echo.
echo ========================================
echo Uninstallation Complete
echo ========================================
echo.

pause
