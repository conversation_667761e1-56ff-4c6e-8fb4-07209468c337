"""
Email service for sending payslip PDFs to employees.
"""

import os
import smtplib
import logging
import mimetypes
from pathlib import Path
from datetime import datetime, timedelta
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.base import MIMEBase
from email import encoders
from typing import Tuple

logger = logging.getLogger(__name__)


class EmailService:
    """Handles email sending functionality for payslip distribution."""
    
    def __init__(self, smtp_server: str, smtp_port: int, username: str, 
                 password: str, use_tls: bool = True):
        """
        Initialize the email service.
        
        Args:
            smtp_server: SMTP server hostname
            smtp_port: SMTP server port
            username: SMTP username
            password: SMTP password
            use_tls: Whether to use TLS encryption
        """
        self.smtp_server = smtp_server
        self.smtp_port = smtp_port
        self.username = username
        self.password = password
        self.use_tls = use_tls
        
        logger.info(f"Email service initialized for {smtp_server}:{smtp_port}")
    
    def calculate_pay_period(self, payslip_date_str: str) -> Tuple[str, str]:
        """
        Calculate the pay period dates based on the payslip date.
        Assumes bi-weekly pay periods ending on the payslip date.
        
        Args:
            payslip_date_str: Date string in YYYYMMDD format
            
        Returns:
            Tuple of (start_date, end_date) in MM/DD/YYYY format
        """
        try:
            # Parse the payslip date
            payslip_date = datetime.strptime(payslip_date_str, '%Y%m%d')
            
            # Calculate start date (14 days before payslip date)
            start_date = payslip_date - timedelta(days=14)  # 13 days before to make it 14-day period
            
            # End date is the payslip date
            end_date = payslip_date
            
            # Format dates as MM/DD/YYYY
            start_date_formatted = start_date.strftime('%m/%d/%Y')
            end_date_formatted = end_date.strftime('%m/%d/%Y')
            
            return start_date_formatted, end_date_formatted
            
        except ValueError as e:
            logger.error(f"Error parsing payslip date {payslip_date_str}: {e}")
            # Return default values if parsing fails
            return "Unknown", "Unknown"
    
    def create_email_message(self, to_email: str, employee_name: str, 
                           payslip_date_str: str, pdf_path: str) -> MIMEMultipart:
        """
        Create an email message with the payslip PDF attachment.
        
        Args:
            to_email: Recipient email address
            employee_name: Employee's full name
            payslip_date_str: Date string in YYYYMMDD format
            pdf_path: Path to the PDF file
            
        Returns:
            MIMEMultipart email message
        """
        # Calculate pay period dates
        start_date, end_date = self.calculate_pay_period(payslip_date_str)
        
        # Load email template configuration from environment
        from_name = os.getenv('EMAIL_FROM_NAME')
        sender_name = os.getenv('EMAIL_SENDER_NAME')
        company_domain = os.getenv('EMAIL_COMPANY_DOMAIN')

        # Create message
        msg = MIMEMultipart()
        msg['From'] = f"{from_name} <{self.username}>"
        msg['To'] = to_email
        msg['Subject'] = f"Payslip for {start_date} - {end_date}"

        # Professional email body template
        body = f"""Dear {employee_name},

Please see attached for payslip report in PDF format.


Regards,
{sender_name}


This email is intended only for the person to whom it is addressed and/or otherwise authorized personnel. The information contained herein and attached is confidential and the property of {company_domain}.

If you are not the intended recipient, please be advised that viewing this message and any attachments, as well as copying, forwarding, printing, and disseminating any information related to this email is prohibited, and that you should not take any action based on the content of this email and/or its attachments. If you received this message in error, please contact the sender and destroy all copies of this email and any attachment. Please note that the views and opinions expressed herein are solely those of the author and do not necessarily reflect those of the company. While antivirus protection tools have been employed, you should check this email and attachments for the presence of viruses. No warranties or assurances are made in relation to the safety and content of this email and attachments. {company_domain} accepts no liability for any damage caused by any virus transmitted by or contained in this email and attachments. No liability is accepted for any consequences arising from this email."""

        msg.attach(MIMEText(body, 'plain'))
        
        # Attach PDF file
        try:
            pdf_file = Path(pdf_path)
            
            if not pdf_file.exists():
                raise FileNotFoundError(f"PDF file not found: {pdf_path}")
            
            # Determine content type
            content_type, encoding = mimetypes.guess_type(pdf_path)
            if content_type is None or encoding is not None:
                content_type = 'application/octet-stream'
            
            main_type, sub_type = content_type.split('/', 1)
            
            with open(pdf_path, 'rb') as attachment:
                part = MIMEBase(main_type, sub_type)
                part.set_payload(attachment.read())
            
            encoders.encode_base64(part)
            part.add_header(
                'Content-Disposition',
                f'attachment; filename= {pdf_file.name}'
            )
            
            msg.attach(part)
            logger.debug(f"Attached PDF file: {pdf_file.name}")
            
        except Exception as e:
            logger.error(f"Error attaching PDF file {pdf_path}: {e}")
            raise
        
        return msg
    
    def send_email(self, to_email: str, employee_name: str, 
                   payslip_date_str: str, pdf_path: str) -> bool:
        """
        Send payslip email to an employee.
        
        Args:
            to_email: Recipient email address
            employee_name: Employee's full name
            payslip_date_str: Date string in YYYYMMDD format
            pdf_path: Path to the PDF file
            
        Returns:
            True if email sent successfully, False otherwise
        """
        try:
            # Create email message
            msg = self.create_email_message(to_email, employee_name, payslip_date_str, pdf_path)
            
            # Connect to SMTP server and send email
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                if self.use_tls:
                    server.starttls()
                
                server.login(self.username, self.password)
                
                text = msg.as_string()
                server.sendmail(self.username, to_email, text)
            
            logger.info(f"Email sent successfully to {to_email} ({employee_name})")
            return True
            
        except smtplib.SMTPAuthenticationError as e:
            logger.error(f"SMTP authentication failed: {e}")
            return False
        except smtplib.SMTPRecipientsRefused as e:
            logger.error(f"Recipient refused: {e}")
            return False
        except smtplib.SMTPServerDisconnected as e:
            logger.error(f"SMTP server disconnected: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error sending email to {to_email}: {e}")
            return False
    
    def test_connection(self) -> bool:
        """
        Test the SMTP connection and authentication.
        
        Returns:
            True if connection successful, False otherwise
        """
        try:
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                if self.use_tls:
                    server.starttls()
                server.login(self.username, self.password)
            
            logger.info("SMTP connection test successful")
            return True
            
        except Exception as e:
            logger.error(f"SMTP connection test failed: {e}")
            return False
