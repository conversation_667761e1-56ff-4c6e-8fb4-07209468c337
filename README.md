# 📧 PayslipSender - Automated HR Payslip Distribution System

**Professional, production-ready payslip distribution system for HR departments.**

Automatically monitors a directory for payslip PDF files and emails them to employees using your existing HR database. Features parallel processing, queue-based file handling, and can be deployed as a Windows service or standalone executable.

---

## 📋 Table of Contents

- [✨ Features](#-features)
- [📋 Requirements](#-requirements)
- [🚀 Quick Setup](#-quick-setup)
- [⚙️ Configuration](#️-configuration)
- [🧪 Testing](#-testing)
- [📦 Deployment Options](#-deployment-options)
- [🔧 Troubleshooting](#-troubleshooting)
- [📁 File Structure](#-file-structure)

---

## ✨ Features

- 🔄 **Parallel Processing** - Handles multiple payslip files simultaneously
- ⚡ **Queue-Based System** - No delays when uploading multiple files
- 🗄️ **Database Integration** - Connects to existing HR employee databases
- 📧 **Professional Email Templates** - Branded emails with confidentiality disclaimers
- 🔒 **Secure File Handling** - Automatic archiving of processed files
- 🪟 **Windows Service Ready** - Can run as background service
- 📊 **Comprehensive Logging** - Detailed logs for monitoring and debugging
- 🎯 **Smart File Detection** - Handles duplicate filenames intelligently

---

## 📋 Requirements

### **System Requirements**
- Windows 10/11 or Windows Server 2016+
- 4GB RAM minimum (8GB recommended for high volume)
- 1GB free disk space

### **Software Requirements**
- **Python 3.8+** (Python 3.12 recommended)
- **SQL Server** (or compatible database)
- **SMTP Email Account** (Gmail, Outlook, etc.)

### **Network Requirements**
- Database server access
- SMTP server access (port 587/465)
- Internet connection for email sending

---

## 🚀 Quick Setup

### **Step 1: Install Python**

Download and install Python 3.12 from [python.org](https://www.python.org/downloads/)

**⚠️ Important:** During installation, check "Add Python to PATH"

### **Step 2: Download PayslipSender**

```cmd
# Clone or download the PayslipSender files to:
C:\PayslipSender\
```

### **Step 3: Install Dependencies**

Open Command Prompt as Administrator and run:

```cmd
cd C:\PayslipSender
pip install pyodbc python-dotenv watchdog
```

**For Windows Service Deployment:**
```cmd
# Use full Python path to ensure service compatibility
"C:\Python312\python.exe" -m pip install pyodbc
"C:\Python312\python.exe" -m pip install python-dotenv
"C:\Python312\python.exe" -m pip install watchdog
```

### **Step 4: Configure Settings**

Copy `.env.example` to `.env` and edit with your settings:

```cmd
copy .env.example .env
notepad .env
```

---

## ⚙️ Configuration

### **Database Configuration**

```env
# SQL Server Settings
DATABASE_SERVER=your-sql-server.company.com
DATABASE_PORT=1433
DATABASE_NAME=HRDatabase
DATABASE_USERNAME=hr_user
DATABASE_PASSWORD=your_password

# Employee Table Settings
EMPLOYEE_TABLE_NAME=Employees
EMPLOYEE_ID_COLUMN=EmployeeId
EMPLOYEE_NAME_COLUMN=FullName
EMPLOYEE_EMAIL_COLUMN=EmailAddress
```

### **Email Configuration**

```env
# SMTP Settings (Gmail Example)
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password
SMTP_USE_TLS=True

# Email Template Settings
EMAIL_SENDER_NAME=DSC Human Resources
EMAIL_SIGNATURE_NAME=Emily R. Planas
```

### **File Processing Settings**

```env
# Directory Settings
WATCH_DIRECTORY=./payslips
ARCHIVED_DIRECTORY=./archived_payslips

# Performance Settings
SCAN_INTERVAL_SECONDS=1
MAX_WORKER_THREADS=5

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/payslip_sender.log
```

---

## 🧪 Testing

### **Step 1: Test Database Connection**

```cmd
cd C:\PayslipSender
python -c "from payslip_sender import PayslipSender; ps = PayslipSender(); print('Database connection:', ps._validate_startup())"
```

### **Step 2: Test Email Configuration**

```cmd
python -c "from email_service import EmailService; import os; from dotenv import load_dotenv; load_dotenv(); es = EmailService(os.getenv('SMTP_SERVER'), int(os.getenv('SMTP_PORT')), os.getenv('SMTP_USERNAME'), os.getenv('SMTP_PASSWORD')); print('Email test:', es.test_connection())"
```

### **Step 3: Test with Sample Files**

1. Create test payslip files in the `payslips` folder:
   ```
   Payslip_501_20250125.pdf
   Payslip_502_20250125.pdf
   ```

2. Run the application:
   ```cmd
   python payslip_sender.py
   ```

3. Check logs for processing results:
   ```cmd
   type logs\payslip_sender.log
   ```

---

## 📦 Deployment Options

### **Option 1: Standalone Executable (Recommended)**

**Best for:** Production HR environments, easy deployment

1. **Create Executable:**
   ```cmd
   cd C:\PayslipSender
   python create_exe.py
   ```

2. **Test Executable:**
   ```cmd
   dist\PayslipSender.exe
   ```

3. **Auto-Start with Windows:**
   ```cmd
   # Copy to startup folder
   copy "dist\PayslipSender.exe" "%APPDATA%\Microsoft\Windows\Start Menu\Programs\Startup\"
   copy ".env" "%APPDATA%\Microsoft\Windows\Start Menu\Programs\Startup\"
   ```

**✅ Benefits:**
- No Python installation needed on target machines
- Faster startup and processing
- Professional appearance (no console windows)
- Single file deployment

### **Option 2: Windows Service with NSSM**

**Best for:** Server environments, IT-managed deployments

1. **Download NSSM:**
   - Download from [nssm.cc](https://nssm.cc/download)
   - Extract to `C:\nssm\`

2. **Install Service:**
   ```cmd
   # Using Python script
   C:\nssm\nssm.exe install PayslipSender "C:\Python312\python.exe" "C:\PayslipSender\payslip_sender.py"
   C:\nssm\nssm.exe set PayslipSender AppDirectory "C:\PayslipSender"
   C:\nssm\nssm.exe start PayslipSender

   # Or using executable
   C:\nssm\nssm.exe install PayslipSender "C:\PayslipSender\dist\PayslipSender.exe"
   C:\nssm\nssm.exe set PayslipSender AppDirectory "C:\PayslipSender"
   C:\nssm\nssm.exe start PayslipSender
   ```

3. **Manage Service:**
   ```cmd
   # Check status
   C:\nssm\nssm.exe status PayslipSender

   # Stop service
   C:\nssm\nssm.exe stop PayslipSender

   # Remove service
   C:\nssm\nssm.exe remove PayslipSender confirm
   ```

### **Option 3: Manual Execution**

**Best for:** Testing, development, small-scale use

```cmd
cd C:\PayslipSender
python payslip_sender.py
```

---

## 🔧 Troubleshooting

### **Common Issues**


#### **Database Connection Failed**
- Verify SQL Server is running and accessible
- Check firewall settings (port 1433)
- Confirm database credentials
- Test with SQL Server Management Studio

#### **Email Authentication Failed**
- Use App Passwords for Gmail (not regular password)
- Enable "Less secure app access" if required
- Check SMTP server and port settings
- Verify TLS/SSL settings

#### **Files Not Processing**
- Check file naming format: `Payslip_EmployeeID_YYYYMMDD.pdf`
- Verify employee IDs exist in database
- Check logs for detailed error messages
- Ensure proper folder permissions

#### **Service Won't Start**
- Run Command Prompt as Administrator
- Check Windows Event Viewer for detailed errors
- Verify all file paths are correct
- Ensure .env file is in the correct location

### **Debug Mode**

Enable detailed logging by setting in `.env`:
```env
LOG_LEVEL=DEBUG
```

Then check logs:
```cmd
type logs\payslip_sender.log | findstr "DEBUG\|ERROR"
```

### **Performance Tuning**

For high-volume processing, adjust in `.env`:
```env
SCAN_INTERVAL_SECONDS=1          # Faster file detection
MAX_WORKER_THREADS=10            # More parallel processing
```

---

## 📁 File Structure

```
C:\PayslipSender\
├── payslip_sender.py           # Main application
├── email_service.py            # Email handling
├── create_exe.py               # Executable creator
├── .env                        # Configuration file
├── .env.example               # Configuration template
├── README.md                  # This file
├── requirements.txt           # Python dependencies
├── payslips\                  # Watch directory (auto-created)
├── archived_payslips\         # Processed files (auto-created)
├── logs\                      # Log files (auto-created)
│   └── payslip_sender.log
└── dist\                      # Executable output (after build)
    └── PayslipSender.exe
```

### **File Naming Convention**

Payslip files must follow this exact format:
```
Payslip_[EmployeeID]_[YYYYMMDD].pdf

Examples:
✅ Payslip_501_20250125.pdf
✅ Payslip_EMP001_20250125.pdf
❌ payslip_501_20250125.pdf     (wrong case)
❌ Payslip-501-20250125.pdf     (wrong separators)
❌ Payslip_501_2025-01-25.pdf   (wrong date format)
```

---

## 🎯 Production Checklist

Before deploying in production:

- [ ] **Test database connection** with production credentials
- [ ] **Test email sending** with production SMTP settings
- [ ] **Verify employee data** matches expected format
- [ ] **Test file processing** with sample payslips
- [ ] **Configure logging** appropriately for production
- [ ] **Set up monitoring** of log files
- [ ] **Document** employee ID format and database schema
- [ ] **Train HR staff** on file naming conventions
- [ ] **Set up backup** of configuration files
- [ ] **Test failover** scenarios

---

## 📞 Support

### **Log Files**
- Application logs: `logs\payslip_sender.log`
- Service logs: Check Windows Event Viewer
- Email logs: Included in application logs

### **Monitoring**
Monitor these key metrics:
- Files processed per day
- Email delivery success rate
- Database connection status
- Queue processing times

### **Maintenance**
- Regularly clean archived payslips folder
- Monitor log file sizes
- Update employee database as needed
- Review and rotate email credentials

---

## 🔒 Security Notes

- Store database credentials securely
- Use App Passwords for email accounts
- Regularly rotate passwords
- Monitor access to payslip files
- Ensure HTTPS/TLS for all connections
- Keep Python and dependencies updated

---

**🎉 Your PayslipSender system is now ready for production use!**

For additional support or customization, refer to the source code comments or contact your system administrator.