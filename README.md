# PayslipSender - HR Department

Production-ready automated payslip distribution system for HR departments. Connects to your existing employee database and automatically emails payslips to employees.

## 🚀 Quick Start

1. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

2. **Configure Database & Email**
   ```bash
   # Edit .env file with your settings
   DATABASE_SERVER=your_sql_server
   DATABASE_NAME=your_database
   SMTP_USERNAME=<EMAIL>
   SMTP_PASSWORD=your_gmail_app_password
   ```

3. **Test Application**
   ```bash
   python payslip_sender.py
   ```

4. **Deploy as Windows Service**

## ⚠️ **Important for Windows Services**

If you're deploying as a Windows service with NSSM, install dependencies using the **exact Python path** that the service will use:

```cmd
# Use full Python path for service deployment
"C:\Python312\python.exe" -m pip install pyodbc
"C:\Python312\python.exe" -m pip install python-dotenv
"C:\Python312\python.exe" -m pip install watchdog
```

This ensures the Windows service can find all required modules.

---

   **Using NSSM GUI (Recommended)**:
   1. Download NSSM from https://nssm.cc/download
   2. Run as Administrator: `nssm install DSCPayslipSender`
   3. Configure in the NSSM GUI:
      - **Path**: `C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps\python.exe`
      - **Startup directory**: `C:\PayslipSender` (your project folder)
      - **Arguments**: `payslip_sender.py`
   4. Click "Install service"
   5. Start: `nssm start DSCPayslipSender`

   **Alternative Python Paths** (if above doesn't work):
   - `C:\Python312\python.exe` (python.org installation)
   - `C:\Program Files\Python312\python.exe` (system-wide)
   - Find yours: `where python` in Command Prompt

## 🎛️ Service Management

```cmd
nssm start DSCPayslipSender    # Start service
nssm stop DSCPayslipSender     # Stop service
nssm restart DSCPayslipSender  # Restart service
nssm status DSCPayslipSender   # Check status
sc query DSCPayslipSender      # Windows service status
```

## 📋 Features

- ✅ **Direct Database Connection**: Connects to your existing HR employee database
- ✅ **Automatic Processing**: Monitors folder for `Payslip_EmployeeID_YYYYMMDD.pdf` files
- ✅ **Email Distribution**: Sends payslips via Gmail SMTP to employee email addresses
- ✅ **File Archiving**: Automatically moves processed files to archive folder
- ✅ **Windows Service**: Runs 24/7 as a background service using NSSM
- ✅ **Production Logging**: Comprehensive logging for monitoring and troubleshooting
- ✅ **Error Handling**: Failed sends logged to database for review

## 📁 File Structure

```
PayslipSender/
├── payslip_sender.py          # Main application
├── email_service.py           # Email functionality
├── requirements.txt           # Dependencies
├── .env                       # Configuration
├── DEPLOYMENT_GUIDE.md        # Detailed setup guide
├── payslips/                  # Drop payslips here
├── archived_payslips/         # Processed files
└── logs/                      # Application logs
```

## 🔧 Configuration

All settings are managed through the `.env` file:

```env
# Database (connects to your existing HR database)
DATABASE_SERVER=localhost
DATABASE_NAME=your_hr_database
DATABASE_USERNAME=your_username
DATABASE_PASSWORD=your_password

# Employee table schema
EMPLOYEE_TABLE_NAME=Employees
EMPLOYEE_ID_COLUMN=EmployeeID
EMPLOYEE_NAME_COLUMN=Name
EMPLOYEE_EMAIL_COLUMN=EmployeeEmail

# Email settings
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_gmail_app_password
```

## 🚨 Troubleshooting

### Service Won't Start
- **Check Python path**: Run `where python` and use exact path in NSSM
- **Verify startup directory**: Must be your project folder (e.g., `C:\PayslipSender`)
- **Check logs**: `logs\service_error.log` for service errors

### Files Not Processing
- **File naming**: Must be `Payslip_EmployeeID_YYYYMMDD.pdf`
- **Check service status**: `nssm status DSCPayslipSender`
- **View logs**: `logs\payslip_sender.log` for processing details

## 📖 Documentation

- **[DEPLOYMENT_GUIDE.md](DEPLOYMENT_GUIDE.md)** - Complete setup and deployment instructions
- **[NSSM_CONFIGURATION_GUIDE.md](NSSM_CONFIGURATION_GUIDE.md)** - Detailed NSSM service setup
- **File Naming**: `Payslip_EmployeeID_YYYYMMDD.pdf` (e.g., `Payslip_501_20250125.pdf`)
- **Logs**: Check `logs/payslip_sender.log` for activity and errors