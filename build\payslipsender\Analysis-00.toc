(['C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslip_sender.py'],
 ['C:\\Users\\<USER>\\Desktop\\PayslipSender'],
 [],
 ['C:\\Python312\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
  'C:\\Python312\\Lib\\site-packages\\_pyinstaller_hooks_contrib'],
 {},
 [],
 [],
 False,
 {},
 [],
 [],
 '3.12.0 (tags/v3.12.0:0fb18b0, Oct  2 2023, 13:03:39) [MSC v.1935 64 bit '
 '(AMD64)]',
 [('pyi_rth_inspect',
   'C:\\Python312\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('payslip_sender',
   'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslip_sender.py',
   'PYSOURCE')],
 [('inspect', 'C:\\Python312\\Lib\\inspect.py', 'PYMODULE'),
  ('importlib', 'C:\\Python312\\Lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Python312\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Python312\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('typing', 'C:\\Python312\\Lib\\typing.py', 'PYMODULE'),
  ('importlib.abc', 'C:\\Python312\\Lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Python312\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Python312\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'C:\\Python312\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Python312\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Python312\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('tempfile', 'C:\\Python312\\Lib\\tempfile.py', 'PYMODULE'),
  ('random', 'C:\\Python312\\Lib\\random.py', 'PYMODULE'),
  ('statistics', 'C:\\Python312\\Lib\\statistics.py', 'PYMODULE'),
  ('decimal', 'C:\\Python312\\Lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal', 'C:\\Python312\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('contextvars', 'C:\\Python312\\Lib\\contextvars.py', 'PYMODULE'),
  ('fractions', 'C:\\Python312\\Lib\\fractions.py', 'PYMODULE'),
  ('numbers', 'C:\\Python312\\Lib\\numbers.py', 'PYMODULE'),
  ('hashlib', 'C:\\Python312\\Lib\\hashlib.py', 'PYMODULE'),
  ('bisect', 'C:\\Python312\\Lib\\bisect.py', 'PYMODULE'),
  ('shutil', 'C:\\Python312\\Lib\\shutil.py', 'PYMODULE'),
  ('tarfile', 'C:\\Python312\\Lib\\tarfile.py', 'PYMODULE'),
  ('gzip', 'C:\\Python312\\Lib\\gzip.py', 'PYMODULE'),
  ('_compression', 'C:\\Python312\\Lib\\_compression.py', 'PYMODULE'),
  ('copy', 'C:\\Python312\\Lib\\copy.py', 'PYMODULE'),
  ('struct', 'C:\\Python312\\Lib\\struct.py', 'PYMODULE'),
  ('lzma', 'C:\\Python312\\Lib\\lzma.py', 'PYMODULE'),
  ('bz2', 'C:\\Python312\\Lib\\bz2.py', 'PYMODULE'),
  ('fnmatch', 'C:\\Python312\\Lib\\fnmatch.py', 'PYMODULE'),
  ('importlib._abc', 'C:\\Python312\\Lib\\importlib\\_abc.py', 'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Python312\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Python312\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Python312\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Python312\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Python312\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Python312\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('email.message', 'C:\\Python312\\Lib\\email\\message.py', 'PYMODULE'),
  ('email.policy', 'C:\\Python312\\Lib\\email\\policy.py', 'PYMODULE'),
  ('email.contentmanager',
   'C:\\Python312\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime', 'C:\\Python312\\Lib\\email\\quoprimime.py', 'PYMODULE'),
  ('string', 'C:\\Python312\\Lib\\string.py', 'PYMODULE'),
  ('email.headerregistry',
   'C:\\Python312\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Python312\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('urllib', 'C:\\Python312\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('email.iterators', 'C:\\Python312\\Lib\\email\\iterators.py', 'PYMODULE'),
  ('email.generator', 'C:\\Python312\\Lib\\email\\generator.py', 'PYMODULE'),
  ('email._encoded_words',
   'C:\\Python312\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64', 'C:\\Python312\\Lib\\base64.py', 'PYMODULE'),
  ('getopt', 'C:\\Python312\\Lib\\getopt.py', 'PYMODULE'),
  ('gettext', 'C:\\Python312\\Lib\\gettext.py', 'PYMODULE'),
  ('email.charset', 'C:\\Python312\\Lib\\email\\charset.py', 'PYMODULE'),
  ('email.encoders', 'C:\\Python312\\Lib\\email\\encoders.py', 'PYMODULE'),
  ('email.base64mime', 'C:\\Python312\\Lib\\email\\base64mime.py', 'PYMODULE'),
  ('email._policybase',
   'C:\\Python312\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.header', 'C:\\Python312\\Lib\\email\\header.py', 'PYMODULE'),
  ('email.errors', 'C:\\Python312\\Lib\\email\\errors.py', 'PYMODULE'),
  ('email.utils', 'C:\\Python312\\Lib\\email\\utils.py', 'PYMODULE'),
  ('email._parseaddr', 'C:\\Python312\\Lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('calendar', 'C:\\Python312\\Lib\\calendar.py', 'PYMODULE'),
  ('urllib.parse', 'C:\\Python312\\Lib\\urllib\\parse.py', 'PYMODULE'),
  ('ipaddress', 'C:\\Python312\\Lib\\ipaddress.py', 'PYMODULE'),
  ('socket', 'C:\\Python312\\Lib\\socket.py', 'PYMODULE'),
  ('selectors', 'C:\\Python312\\Lib\\selectors.py', 'PYMODULE'),
  ('quopri', 'C:\\Python312\\Lib\\quopri.py', 'PYMODULE'),
  ('contextlib', 'C:\\Python312\\Lib\\contextlib.py', 'PYMODULE'),
  ('textwrap', 'C:\\Python312\\Lib\\textwrap.py', 'PYMODULE'),
  ('zipfile', 'C:\\Python312\\Lib\\zipfile\\__init__.py', 'PYMODULE'),
  ('zipfile.__main__', 'C:\\Python312\\Lib\\zipfile\\__main__.py', 'PYMODULE'),
  ('zipfile._path',
   'C:\\Python312\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'C:\\Python312\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('py_compile', 'C:\\Python312\\Lib\\py_compile.py', 'PYMODULE'),
  ('threading', 'C:\\Python312\\Lib\\threading.py', 'PYMODULE'),
  ('_threading_local', 'C:\\Python312\\Lib\\_threading_local.py', 'PYMODULE'),
  ('importlib.util', 'C:\\Python312\\Lib\\importlib\\util.py', 'PYMODULE'),
  ('email', 'C:\\Python312\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email.parser', 'C:\\Python312\\Lib\\email\\parser.py', 'PYMODULE'),
  ('email.feedparser', 'C:\\Python312\\Lib\\email\\feedparser.py', 'PYMODULE'),
  ('csv', 'C:\\Python312\\Lib\\csv.py', 'PYMODULE'),
  ('importlib.readers',
   'C:\\Python312\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Python312\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Python312\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Python312\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('argparse', 'C:\\Python312\\Lib\\argparse.py', 'PYMODULE'),
  ('token', 'C:\\Python312\\Lib\\token.py', 'PYMODULE'),
  ('tokenize', 'C:\\Python312\\Lib\\tokenize.py', 'PYMODULE'),
  ('importlib.machinery',
   'C:\\Python312\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('dis', 'C:\\Python312\\Lib\\dis.py', 'PYMODULE'),
  ('opcode', 'C:\\Python312\\Lib\\opcode.py', 'PYMODULE'),
  ('ast', 'C:\\Python312\\Lib\\ast.py', 'PYMODULE'),
  ('_py_abc', 'C:\\Python312\\Lib\\_py_abc.py', 'PYMODULE'),
  ('stringprep', 'C:\\Python312\\Lib\\stringprep.py', 'PYMODULE'),
  ('tracemalloc', 'C:\\Python312\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('pickle', 'C:\\Python312\\Lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'C:\\Python312\\Lib\\pprint.py', 'PYMODULE'),
  ('dataclasses', 'C:\\Python312\\Lib\\dataclasses.py', 'PYMODULE'),
  ('_compat_pickle', 'C:\\Python312\\Lib\\_compat_pickle.py', 'PYMODULE'),
  ('email_service',
   'C:\\Users\\<USER>\\Desktop\\PayslipSender\\email_service.py',
   'PYMODULE'),
  ('email.mime.base', 'C:\\Python312\\Lib\\email\\mime\\base.py', 'PYMODULE'),
  ('email.mime', 'C:\\Python312\\Lib\\email\\mime\\__init__.py', 'PYMODULE'),
  ('email.mime.text', 'C:\\Python312\\Lib\\email\\mime\\text.py', 'PYMODULE'),
  ('email.mime.nonmultipart',
   'C:\\Python312\\Lib\\email\\mime\\nonmultipart.py',
   'PYMODULE'),
  ('email.mime.multipart',
   'C:\\Python312\\Lib\\email\\mime\\multipart.py',
   'PYMODULE'),
  ('mimetypes', 'C:\\Python312\\Lib\\mimetypes.py', 'PYMODULE'),
  ('smtplib', 'C:\\Python312\\Lib\\smtplib.py', 'PYMODULE'),
  ('ssl', 'C:\\Python312\\Lib\\ssl.py', 'PYMODULE'),
  ('hmac', 'C:\\Python312\\Lib\\hmac.py', 'PYMODULE'),
  ('dotenv',
   'C:\\Python312\\Lib\\site-packages\\dotenv\\__init__.py',
   'PYMODULE'),
  ('dotenv.ipython',
   'C:\\Python312\\Lib\\site-packages\\dotenv\\ipython.py',
   'PYMODULE'),
  ('dotenv.main',
   'C:\\Python312\\Lib\\site-packages\\dotenv\\main.py',
   'PYMODULE'),
  ('dotenv.variables',
   'C:\\Python312\\Lib\\site-packages\\dotenv\\variables.py',
   'PYMODULE'),
  ('dotenv.parser',
   'C:\\Python312\\Lib\\site-packages\\dotenv\\parser.py',
   'PYMODULE'),
  ('datetime', 'C:\\Python312\\Lib\\datetime.py', 'PYMODULE'),
  ('_pydatetime', 'C:\\Python312\\Lib\\_pydatetime.py', 'PYMODULE'),
  ('_strptime', 'C:\\Python312\\Lib\\_strptime.py', 'PYMODULE'),
  ('pathlib', 'C:\\Python312\\Lib\\pathlib.py', 'PYMODULE'),
  ('signal', 'C:\\Python312\\Lib\\signal.py', 'PYMODULE'),
  ('logging', 'C:\\Python312\\Lib\\logging\\__init__.py', 'PYMODULE'),
  ('subprocess', 'C:\\Python312\\Lib\\subprocess.py', 'PYMODULE')],
 [('python312.dll', 'C:\\Python312\\python312.dll', 'BINARY'),
  ('_decimal.pyd', 'C:\\Python312\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'C:\\Python312\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'C:\\Python312\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'C:\\Python312\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('unicodedata.pyd', 'C:\\Python312\\DLLs\\unicodedata.pyd', 'EXTENSION'),
  ('select.pyd', 'C:\\Python312\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd', 'C:\\Python312\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'C:\\Python312\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('pyodbc.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\pyodbc.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll', 'C:\\Python312\\VCRUNTIME140.dll', 'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-17\\bin\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('libcrypto-3.dll', 'C:\\Python312\\DLLs\\libcrypto-3.dll', 'BINARY'),
  ('libssl-3.dll', 'C:\\Python312\\DLLs\\libssl-3.dll', 'BINARY'),
  ('ucrtbase.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\ucrtbase.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-17\\bin\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-17\\bin\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Program '
   'Files\\Java\\jdk-17\\bin\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-17\\bin\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-17\\bin\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-17\\bin\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-17\\bin\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-17\\bin\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-17\\bin\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY')],
 [],
 [],
 [('base_library.zip',
   'C:\\Users\\<USER>\\Desktop\\PayslipSender\\build\\payslipsender\\base_library.zip',
   'DATA')])
