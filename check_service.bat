@echo off
REM ============================================================================
REM Check PayslipSender Service Status
REM ============================================================================

echo.
echo ========================================
echo PayslipSender Service Status Check
echo ========================================
echo.

set SERVICE_NAME=DSCPayslipSender

echo Checking for service: %SERVICE_NAME%
echo.

REM Check if service exists
sc query "%SERVICE_NAME%" >nul 2>&1
if %errorLevel% equ 0 (
    echo ✅ Service FOUND: %SERVICE_NAME%
    echo.
    echo ========================================
    echo Service Details:
    echo ========================================
    sc query "%SERVICE_NAME%"
    echo.
    echo ========================================
    echo Service Configuration:
    echo ========================================
    sc qc "%SERVICE_NAME%"
    echo.
) else (
    echo ❌ Service NOT FOUND: %SERVICE_NAME%
    echo.
    echo The PayslipSender service is not registered in Windows Services.
    echo.
    echo To register the service, run: register_service.bat (as Administrator)
    echo.
)

echo ========================================
echo All Services containing "Payslip":
echo ========================================
sc query type= service state= all | findstr /i "payslip" >nul 2>&1
if %errorLevel% equ 0 (
    sc query type= service state= all | findstr /i /C:"SERVICE_NAME" /C:"DISPLAY_NAME" | findstr /i "payslip"
) else (
    echo No services found containing "Payslip"
)

echo.
echo ========================================
echo All Services containing "DSC":
echo ========================================
sc query type= service state= all | findstr /i "dsc" >nul 2>&1
if %errorLevel% equ 0 (
    sc query type= service state= all | findstr /i /C:"SERVICE_NAME" /C:"DISPLAY_NAME" | findstr /i "dsc"
) else (
    echo No services found containing "DSC"
)

echo.
pause
