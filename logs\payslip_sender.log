2025-07-20 19:32:54,582 - config - INFO - Logging configured successfully
2025-07-20 19:32:54,584 - database - INFO - Database initialized successfully
2025-07-20 19:32:54,585 - email_service - INFO - Email service initialized for smtp.gmail.com:587
2025-07-20 19:32:54,586 - file_monitor - INFO - File monitor initialized for directory: payslips
2025-07-20 19:32:54,586 - __main__ - INFO - PayslipSender application initialized
2025-07-20 19:32:54,594 - __main__ - INFO - Starting PayslipSender application...
2025-07-20 19:32:56,383 - email_service - INFO - SMTP connection test successful
2025-07-20 19:32:56,389 - file_monitor - INFO - Processing existing files in directory...
2025-07-20 19:32:56,391 - file_monitor - INFO - Processing existing file: Invalid_filename.pdf
2025-07-20 19:32:56,392 - file_monitor - WARNING - Skipping invalid filename: Invalid_filename.pdf
2025-07-20 19:32:56,392 - file_monitor - INFO - Processing existing file: Payslip_501_20250720.pdf
2025-07-20 19:32:56,407 - __main__ - INFO - Processing payslip for employee 501, date 20250720
2025-07-20 19:33:00,103 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel)
2025-07-20 19:33:00,104 - __main__ - INFO - Successfully sent payslip to Jose Daniel (<EMAIL>)
2025-07-20 19:33:00,107 - __main__ - INFO - Moved processed file to: processed_payslips\Payslip_501_20250720.pdf
2025-07-20 19:33:00,108 - file_monitor - INFO - Processing existing file: Payslip_502_20250706.pdf
2025-07-20 19:33:00,109 - __main__ - INFO - Processing payslip for employee 502, date 20250706
2025-07-20 19:33:03,824 - email_service - INFO - Email sent <NAME_EMAIL> (Jane Smith)
2025-07-20 19:33:03,825 - __main__ - INFO - Successfully sent payslip to Jane Smith (<EMAIL>)
2025-07-20 19:33:03,827 - __main__ - INFO - Moved processed file to: processed_payslips\Payslip_502_20250706.pdf
2025-07-20 19:33:03,828 - file_monitor - INFO - Processing existing file: Payslip_503_20250713.pdf
2025-07-20 19:33:03,829 - __main__ - INFO - Processing payslip for employee 503, date 20250713
2025-07-20 19:33:07,645 - email_service - INFO - Email sent <NAME_EMAIL> (Bob Johnson)
2025-07-20 19:33:07,645 - __main__ - INFO - Successfully sent payslip to Bob Johnson (<EMAIL>)
2025-07-20 19:33:07,654 - __main__ - INFO - Moved processed file to: processed_payslips\Payslip_503_20250713.pdf
2025-07-20 19:33:07,655 - file_monitor - INFO - Processing existing file: Payslip_504_20250625.pdf
2025-07-20 19:33:07,655 - __main__ - INFO - Processing payslip for employee 504, date 20250625
2025-07-20 19:33:11,279 - email_service - INFO - Email sent <NAME_EMAIL> (Alice Brown)
2025-07-20 19:33:11,280 - __main__ - INFO - Successfully sent payslip to Alice Brown (<EMAIL>)
2025-07-20 19:33:11,283 - __main__ - INFO - Moved processed file to: processed_payslips\Payslip_504_20250625.pdf
2025-07-20 19:33:11,284 - __main__ - ERROR - Fatal error: 'handle' must be a _ThreadHandle
2025-07-20 19:37:55,819 - config - INFO - Logging configured successfully
2025-07-20 19:37:55,821 - database - INFO - Database initialized successfully
2025-07-20 19:37:55,821 - email_service - INFO - Email service initialized for smtp.gmail.com:587
2025-07-20 19:37:55,822 - file_monitor - INFO - File monitor initialized for directory: payslips
2025-07-20 19:37:55,822 - __main__ - INFO - PayslipSender application initialized
2025-07-20 19:37:55,822 - __main__ - INFO - Starting PayslipSender application...
2025-07-20 19:37:57,646 - email_service - INFO - SMTP connection test successful
2025-07-20 19:37:57,650 - file_monitor - INFO - Processing existing files in directory...
2025-07-20 19:37:57,651 - file_monitor - INFO - Processing existing file: Invalid_filename.pdf
2025-07-20 19:37:57,651 - file_monitor - WARNING - Skipping invalid filename: Invalid_filename.pdf
2025-07-20 19:37:57,652 - file_monitor - INFO - Processing existing file: Payslip_501_20250720_test.pdf
2025-07-20 19:37:57,652 - file_monitor - WARNING - Skipping invalid filename: Payslip_501_20250720_test.pdf
2025-07-20 19:37:57,653 - __main__ - ERROR - Fatal error: 'handle' must be a _ThreadHandle
2025-07-20 19:38:24,919 - config - INFO - Logging configured successfully
2025-07-20 19:38:24,921 - database - INFO - Database initialized successfully
2025-07-20 19:38:24,922 - email_service - INFO - Email service initialized for smtp.gmail.com:587
2025-07-20 19:38:24,922 - file_monitor - INFO - File monitor initialized for directory: payslips
2025-07-20 19:38:24,923 - __main__ - INFO - PayslipSender application initialized
2025-07-20 19:38:24,923 - __main__ - INFO - Starting PayslipSender application...
2025-07-20 19:38:26,762 - email_service - INFO - SMTP connection test successful
2025-07-20 19:38:26,764 - file_monitor - INFO - Processing existing files in directory...
2025-07-20 19:38:26,765 - file_monitor - INFO - Processing existing file: Invalid_filename.pdf
2025-07-20 19:38:26,765 - file_monitor - WARNING - Skipping invalid filename: Invalid_filename.pdf
2025-07-20 19:38:26,765 - file_monitor - INFO - Processing existing file: Payslip_501_20250720.pdf
2025-07-20 19:38:26,769 - __main__ - INFO - Processing payslip for employee 501, date 20250720
2025-07-20 19:38:30,309 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel)
2025-07-20 19:38:30,310 - __main__ - INFO - Successfully sent payslip to Jose Daniel (<EMAIL>)
2025-07-20 19:38:30,311 - __main__ - INFO - Moved processed file to archived directory: archived_payslips\Payslip_501_20250720.pdf
2025-07-20 19:38:30,312 - file_monitor - INFO - Processing existing file: Payslip_501_20250720_test.pdf
2025-07-20 19:38:30,312 - file_monitor - WARNING - Skipping invalid filename: Payslip_501_20250720_test.pdf
2025-07-20 19:38:30,313 - __main__ - ERROR - Fatal error: 'handle' must be a _ThreadHandle
2025-07-20 19:40:33,024 - config - INFO - Logging configured successfully
2025-07-20 19:40:33,027 - database - INFO - Database initialized successfully
2025-07-20 19:40:33,027 - email_service - INFO - Email service initialized for smtp.gmail.com:587
2025-07-20 19:40:33,028 - file_monitor - INFO - File monitor initialized for directory: payslips
2025-07-20 19:40:33,028 - __main__ - INFO - PayslipSender application initialized
2025-07-20 19:40:33,029 - __main__ - INFO - Starting PayslipSender application...
2025-07-20 19:40:34,839 - email_service - INFO - SMTP connection test successful
2025-07-20 19:40:34,840 - file_monitor - INFO - Processing existing files in directory...
2025-07-20 19:40:34,841 - file_monitor - INFO - Processing existing file: Invalid_filename.pdf
2025-07-20 19:40:34,841 - file_monitor - WARNING - Skipping invalid filename: Invalid_filename.pdf
2025-07-20 19:40:34,842 - file_monitor - INFO - Processing existing file: Payslip_501_20250720.pdf
2025-07-20 19:40:34,853 - __main__ - INFO - Processing payslip for employee 501, date 20250720
2025-07-20 19:40:38,550 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel)
2025-07-20 19:40:38,551 - __main__ - INFO - Successfully sent payslip to Jose Daniel (<EMAIL>)
2025-07-20 19:40:38,554 - __main__ - INFO - Moved processed file to archived directory: archived_payslips\Payslip_501_20250720_20250720_194038.pdf
2025-07-20 19:40:38,556 - __main__ - ERROR - Fatal error: 'handle' must be a _ThreadHandle
2025-07-20 19:41:40,463 - config - INFO - Logging configured successfully
2025-07-20 19:41:40,465 - database - INFO - Database initialized successfully
2025-07-20 19:41:40,465 - email_service - INFO - Email service initialized for smtp.gmail.com:587
2025-07-20 19:41:40,466 - __main__ - INFO - SimplePayslipMonitor initialized
2025-07-20 19:41:40,466 - __main__ - INFO - Starting SimplePayslipMonitor...
2025-07-20 19:41:42,201 - email_service - INFO - SMTP connection test successful
2025-07-20 19:41:42,203 - __main__ - INFO - SimplePayslipMonitor is now running. Press Ctrl+C to stop.
2025-07-20 19:41:42,203 - __main__ - INFO - Monitoring directory for new payslip files...
2025-07-20 19:41:42,204 - __main__ - WARNING - Invalid filename format: Invalid_filename.pdf
2025-07-20 19:42:32,235 - __main__ - INFO - New valid payslip file detected: Payslip_501_20250719.pdf
2025-07-20 19:42:32,236 - __main__ - INFO - Processing payslip for employee 501, date 20250719
2025-07-20 19:42:35,672 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel)
2025-07-20 19:42:35,673 - __main__ - INFO - Successfully sent payslip to Jose Daniel (<EMAIL>)
2025-07-20 19:42:35,676 - __main__ - INFO - Moved processed file to archived directory: archived_payslips\Payslip_501_20250719.pdf
2025-07-20 19:44:30,739 - __main__ - INFO - New valid payslip file detected: Payslip_501_20250720.pdf
2025-07-20 19:44:30,739 - __main__ - INFO - Processing payslip for employee 501, date 20250720
2025-07-20 19:44:34,238 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel)
2025-07-20 19:44:34,239 - __main__ - INFO - Successfully sent payslip to Jose Daniel (<EMAIL>)
2025-07-20 19:44:34,241 - __main__ - INFO - Moved processed file to archived directory: archived_payslips\Payslip_501_20250720_20250720_194434.pdf
2025-07-20 19:50:36,844 - config - INFO - Logging configured successfully
2025-07-20 19:50:36,846 - database - INFO - Database initialized successfully
2025-07-20 19:50:36,846 - email_service - INFO - Email service initialized for smtp.gmail.com:587
2025-07-20 19:50:36,846 - __main__ - INFO - SimplePayslipMonitor initialized
2025-07-20 19:50:36,847 - __main__ - INFO - Starting SimplePayslipMonitor...
2025-07-20 19:50:38,745 - email_service - INFO - SMTP connection test successful
2025-07-20 19:50:38,748 - __main__ - INFO - SimplePayslipMonitor is now running. Press Ctrl+C to stop.
2025-07-20 19:50:38,751 - __main__ - INFO - Monitoring directory for new payslip files...
2025-07-20 19:50:38,770 - __main__ - WARNING - Invalid filename format: InvalidFormat.pdf
2025-07-20 19:50:38,787 - database - WARNING - Logged failed send for employee InvalidFormat: Invalid filename format: InvalidFormat.pdf
2025-07-20 19:50:38,788 - __main__ - WARNING - Invalid filename format: Invalid_filename.pdf
2025-07-20 19:50:38,808 - database - WARNING - Logged failed send for employee Invalid_filename: Invalid filename format: Invalid_filename.pdf
2025-07-20 19:50:38,821 - __main__ - INFO - New valid payslip file detected: Payslip_999_20250720.pdf
2025-07-20 19:50:38,822 - __main__ - INFO - Processing payslip for employee 999, date 20250720
2025-07-20 19:50:38,831 - __main__ - ERROR - Employee not found in database: 999
2025-07-20 19:50:38,839 - database - WARNING - Logged failed send for employee 999: Employee not found in database: 999
2025-07-20 19:50:38,840 - file_monitor - ERROR - Invalid date format in filename: 20250732
2025-07-20 19:50:38,841 - __main__ - WARNING - Invalid filename format: Payslip_999_20250732.pdf
2025-07-20 19:50:38,855 - database - WARNING - Logged failed send for employee Payslip_999_20250732: Invalid filename format: Payslip_999_20250732.pdf
2025-07-20 19:50:38,857 - __main__ - WARNING - Invalid filename format: Payslip_999_InvalidDate.pdf
2025-07-20 19:50:38,875 - database - WARNING - Logged failed send for employee Payslip_999_InvalidDate: Invalid filename format: Payslip_999_InvalidDate.pdf
2025-07-20 19:50:38,876 - __main__ - WARNING - Invalid filename format: Payslip_ABC_20250720.pdf
2025-07-20 19:50:38,885 - database - WARNING - Logged failed send for employee Payslip_ABC_20250720: Invalid filename format: Payslip_ABC_20250720.pdf
2025-07-20 19:51:18,905 - __main__ - INFO - New valid payslip file detected: Payslip_501_20250720.pdf
2025-07-20 19:51:18,906 - __main__ - INFO - Processing payslip for employee 501, date 20250720
2025-07-20 19:51:22,838 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel)
2025-07-20 19:51:22,839 - __main__ - INFO - Successfully sent payslip to Jose Daniel (<EMAIL>)
2025-07-20 19:51:22,841 - __main__ - INFO - Moved processed file to archived directory: archived_payslips\Payslip_501_20250720_20250720_195122.pdf
2025-07-20 19:51:22,842 - __main__ - INFO - New valid payslip file detected: Payslip_502_20250706.pdf
2025-07-20 19:51:22,843 - __main__ - INFO - Processing payslip for employee 502, date 20250706
2025-07-20 19:51:26,596 - email_service - INFO - Email sent <NAME_EMAIL> (Jane Smith)
2025-07-20 19:51:26,598 - __main__ - INFO - Successfully sent payslip to Jane Smith (<EMAIL>)
2025-07-20 19:51:26,601 - __main__ - INFO - Moved processed file to archived directory: archived_payslips\Payslip_502_20250706.pdf
2025-07-20 19:51:26,602 - __main__ - INFO - New valid payslip file detected: Payslip_503_20250713.pdf
2025-07-20 19:51:26,602 - __main__ - INFO - Processing payslip for employee 503, date 20250713
2025-07-20 19:51:30,067 - email_service - INFO - Email sent <NAME_EMAIL> (Bob Johnson)
2025-07-20 19:51:30,068 - __main__ - INFO - Successfully sent payslip to Bob Johnson (<EMAIL>)
2025-07-20 19:51:30,071 - __main__ - INFO - Moved processed file to archived directory: archived_payslips\Payslip_503_20250713.pdf
2025-07-20 19:51:30,073 - __main__ - INFO - New valid payslip file detected: Payslip_504_20250625.pdf
2025-07-20 19:51:30,073 - __main__ - INFO - Processing payslip for employee 504, date 20250625
2025-07-20 19:51:33,903 - email_service - INFO - Email sent <NAME_EMAIL> (Alice Brown)
2025-07-20 19:51:33,904 - __main__ - INFO - Successfully sent payslip to Alice Brown (<EMAIL>)
2025-07-20 19:51:33,907 - __main__ - INFO - Moved processed file to archived directory: archived_payslips\Payslip_504_20250625.pdf
2025-07-20 19:54:14,002 - __main__ - INFO - New valid payslip file detected: Payslip_501_20250719.pdf
2025-07-20 19:54:14,003 - __main__ - INFO - Processing payslip for employee 501, date 20250719
2025-07-20 19:54:18,216 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel)
2025-07-20 19:54:18,217 - __main__ - INFO - Successfully sent payslip to Jose Daniel (<EMAIL>)
2025-07-20 19:54:18,219 - __main__ - INFO - Moved processed file to archived directory: archived_payslips\Payslip_501_20250719_20250720_195418.pdf
2025-07-20 19:58:43,970 - config - INFO - Logging configured successfully
2025-07-20 19:58:43,972 - database - INFO - Database initialized successfully
2025-07-20 19:58:43,973 - email_service - INFO - Email service initialized for smtp.gmail.com:587
2025-07-20 19:58:43,973 - __main__ - INFO - SimplePayslipMonitor initialized
2025-07-20 19:58:43,974 - __main__ - INFO - Starting SimplePayslipMonitor...
2025-07-20 19:58:45,580 - email_service - INFO - SMTP connection test successful
2025-07-20 19:58:45,583 - __main__ - INFO - SimplePayslipMonitor is now running. Press Ctrl+C to stop.
2025-07-20 19:58:45,583 - __main__ - INFO - Monitoring directory for new payslip files...
2025-07-20 19:58:45,584 - __main__ - WARNING - Invalid filename format: InvalidFormat.pdf
2025-07-20 19:58:45,591 - database - WARNING - Logged failed send for employee UNKNOWN: Invalid format
2025-07-20 19:58:45,596 - __main__ - INFO - New valid payslip file detected: Payslip_502_20250706.pdf
2025-07-20 19:58:45,596 - __main__ - INFO - Processing payslip for employee 502, date 20250706
2025-07-20 19:58:49,368 - email_service - INFO - Email sent <NAME_EMAIL> (Jane Smith)
2025-07-20 19:58:49,369 - __main__ - INFO - Successfully sent payslip to Jane Smith (<EMAIL>)
2025-07-20 19:58:49,372 - __main__ - INFO - Moved processed file to archived directory: archived_payslips\Payslip_502_20250706_20250720_195849.pdf
2025-07-20 19:58:49,374 - __main__ - INFO - New valid payslip file detected: Payslip_999_20250720.pdf
2025-07-20 19:58:49,375 - __main__ - INFO - Processing payslip for employee 999, date 20250720
2025-07-20 19:58:49,377 - __main__ - ERROR - Employee not found in database: 999
2025-07-20 19:58:49,392 - database - WARNING - Logged failed send for employee 999: Missing email address
2025-07-20 19:58:49,393 - file_monitor - ERROR - Invalid date format in filename: 20250732
2025-07-20 19:58:49,394 - __main__ - WARNING - Invalid filename format: Payslip_999_20250732.pdf
2025-07-20 19:58:49,404 - database - WARNING - Logged failed send for employee 999: Invalid format
2025-07-20 19:58:49,405 - __main__ - WARNING - Invalid filename format: Payslip_999_InvalidDate.pdf
2025-07-20 19:58:49,416 - database - WARNING - Logged failed send for employee 999: Invalid format
2025-07-20 19:58:49,417 - __main__ - WARNING - Invalid filename format: Payslip_ABC_20250720.pdf
2025-07-20 19:58:49,427 - database - WARNING - Logged failed send for employee ABC: Invalid format
2025-07-20 20:02:44,529 - __main__ - INFO - New valid payslip file detected: Payslip_501_20250719.pdf
2025-07-20 20:02:44,530 - __main__ - INFO - Processing payslip for employee 501, date 20250719
2025-07-20 20:02:48,260 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel)
2025-07-20 20:02:48,261 - __main__ - INFO - Successfully sent payslip to Jose Daniel (<EMAIL>)
2025-07-20 20:02:48,263 - __main__ - INFO - Moved processed file to archived directory: archived_payslips\Payslip_501_20250719_20250720_200248.pdf
2025-07-20 20:11:48,516 - __main__ - INFO - New valid payslip file detected: Payslip_501_20250720.pdf
2025-07-20 20:11:48,517 - __main__ - INFO - Processing payslip for employee 501, date 20250720
2025-07-20 20:11:59,693 - config - INFO - Logging configured successfully
2025-07-20 20:11:59,695 - database - INFO - Database initialized successfully
2025-07-20 20:11:59,696 - email_service - INFO - Email service initialized for smtp.gmail.com:587
2025-07-20 20:11:59,696 - __main__ - INFO - SimplePayslipMonitor initialized
2025-07-20 20:11:59,696 - __main__ - INFO - Starting SimplePayslipMonitor...
2025-07-20 20:12:01,656 - email_service - INFO - SMTP connection test successful
2025-07-20 20:12:01,661 - __main__ - INFO - SimplePayslipMonitor is now running. Press Ctrl+C to stop.
2025-07-20 20:12:01,661 - __main__ - INFO - Monitoring directory for new payslip files...
2025-07-20 20:12:01,663 - __main__ - WARNING - Invalid filename format: InvalidFormat.pdf
2025-07-20 20:12:01,675 - database - WARNING - Logged failed send for employee UNKNOWN: Invalid format
2025-07-20 20:12:01,681 - __main__ - INFO - New valid payslip file detected: Payslip_501_20250720.pdf
2025-07-20 20:12:01,682 - __main__ - INFO - Processing payslip for employee 501, date 20250720
2025-07-20 20:12:05,724 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel)
2025-07-20 20:12:05,725 - __main__ - INFO - Successfully sent payslip to Jose Daniel (<EMAIL>)
2025-07-20 20:12:05,728 - __main__ - INFO - Moved processed file to archived directory: archived_payslips\Payslip_501_20250720_20250720_201205.pdf
2025-07-20 20:12:05,729 - __main__ - INFO - New valid payslip file detected: Payslip_502_20250706.pdf
2025-07-20 20:12:05,729 - __main__ - INFO - Processing payslip for employee 502, date 20250706
2025-07-20 20:12:09,410 - email_service - INFO - Email sent <NAME_EMAIL> (Jane Smith)
2025-07-20 20:12:09,411 - __main__ - INFO - Successfully sent payslip to Jane Smith (<EMAIL>)
2025-07-20 20:12:09,414 - __main__ - INFO - Moved processed file to archived directory: archived_payslips\Payslip_502_20250706_20250720_201209.pdf
2025-07-20 20:12:09,415 - __main__ - INFO - New valid payslip file detected: Payslip_502_20250719.pdf
2025-07-20 20:12:09,415 - __main__ - INFO - Processing payslip for employee 502, date 20250719
2025-07-20 20:12:12,869 - email_service - INFO - Email sent <NAME_EMAIL> (Jane Smith)
2025-07-20 20:12:12,870 - __main__ - INFO - Successfully sent payslip to Jane Smith (<EMAIL>)
2025-07-20 20:12:12,872 - __main__ - INFO - Moved processed file to archived directory: archived_payslips\Payslip_502_20250719.pdf
2025-07-20 20:12:12,872 - __main__ - INFO - New valid payslip file detected: Payslip_503_20250718.pdf
2025-07-20 20:12:12,873 - __main__ - INFO - Processing payslip for employee 503, date 20250718
2025-07-20 20:12:16,366 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel Test 2)
2025-07-20 20:12:16,368 - __main__ - INFO - Successfully sent payslip to Jose Daniel Test 2 (<EMAIL>)
2025-07-20 20:12:16,370 - __main__ - INFO - Moved processed file to archived directory: archived_payslips\Payslip_503_20250718.pdf
2025-07-20 20:12:16,371 - __main__ - INFO - New valid payslip file detected: Payslip_504_20250717.pdf
2025-07-20 20:12:16,371 - __main__ - INFO - Processing payslip for employee 504, date 20250717
2025-07-20 20:12:19,957 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel Test 3)
2025-07-20 20:12:19,958 - __main__ - INFO - Successfully sent payslip to Jose Daniel Test 3 (<EMAIL>)
2025-07-20 20:12:19,967 - __main__ - INFO - Moved processed file to archived directory: archived_payslips\Payslip_504_20250717.pdf
2025-07-20 20:12:19,968 - __main__ - INFO - New valid payslip file detected: Payslip_999_20250716.pdf
2025-07-20 20:12:19,969 - __main__ - INFO - Processing payslip for employee 999, date 20250716
2025-07-20 20:12:19,971 - __main__ - ERROR - Employee not found in database: 999
2025-07-20 20:12:19,980 - database - WARNING - Logged failed send for employee 999: Missing email address
2025-07-20 20:13:20,005 - __main__ - WARNING - Invalid filename format: Payslip_502_20250720_201315.pdf
2025-07-20 20:13:20,017 - database - WARNING - Logged failed send for employee 502_20250720: Invalid format
2025-07-20 20:13:55,038 - __main__ - INFO - New valid payslip file detected: Payslip_502_20250715.pdf
2025-07-20 20:13:55,039 - __main__ - INFO - Processing payslip for employee 502, date 20250715
2025-07-20 20:13:58,453 - email_service - INFO - Email sent <NAME_EMAIL> (Jane Smith)
2025-07-20 20:13:58,454 - __main__ - INFO - Successfully sent payslip to Jane Smith (<EMAIL>)
2025-07-20 20:13:58,457 - __main__ - INFO - Moved processed file to archived directory: archived_payslips\Payslip_502_20250715.pdf
2025-07-20 20:23:13,698 - __main__ - WARNING - Invalid filename format: Payslip_502_20250718_extra.pdf
2025-07-20 20:23:13,708 - database - WARNING - Logged failed send for employee 502_20250718: Invalid format
2025-07-20 20:23:13,708 - __main__ - WARNING - Invalid filename format: Payslip_502_InvalidDate.pdf
2025-07-20 20:23:13,717 - database - WARNING - Logged failed send for employee 502: Invalid format
2025-07-20 20:23:13,717 - __main__ - INFO - New valid payslip file detected: Payslip_999_20250720.pdf
2025-07-20 20:23:13,718 - __main__ - INFO - Processing payslip for employee 999, date 20250720
2025-07-20 20:23:13,720 - __main__ - ERROR - Employee not found in database: 999
2025-07-20 20:23:13,724 - database - WARNING - Logged failed send for employee 999: Missing email address
2025-07-20 20:23:13,725 - __main__ - WARNING - Invalid filename format: Payslip_ABC_20250719.pdf
2025-07-20 20:23:13,733 - database - WARNING - Logged failed send for employee ABC: Invalid format
2025-07-20 20:26:15,070 - config - INFO - Logging configured successfully
2025-07-20 20:26:15,072 - database - INFO - Database initialized successfully
2025-07-20 20:26:15,072 - email_service - INFO - Email service initialized for smtp.gmail.com:587
2025-07-20 20:26:15,073 - __main__ - INFO - SimplePayslipMonitor initialized
2025-07-20 20:26:15,073 - __main__ - INFO - Starting SimplePayslipMonitor...
2025-07-20 20:26:16,657 - email_service - INFO - SMTP connection test successful
2025-07-20 20:26:16,661 - __main__ - INFO - SimplePayslipMonitor is now running. Press Ctrl+C to stop.
2025-07-20 20:26:16,661 - __main__ - INFO - Monitoring directory for new payslip files...
2025-07-20 20:26:16,663 - __main__ - WARNING - Invalid filename format: InvalidFormat.pdf
2025-07-20 20:26:16,679 - database - WARNING - Logged failed send for employee UNKNOWN: Invalid filename format: InvalidFormat.pdf does not match Payslip_EmployeeId_YYYYMMDD.pdf pattern
2025-07-20 20:26:16,680 - __main__ - WARNING - Invalid filename format: Payslip_502_20250718_extra.pdf
2025-07-20 20:26:16,691 - database - WARNING - Logged failed send for employee 502: Invalid filename format: Payslip_502_20250718_extra.pdf does not match Payslip_EmployeeId_YYYYMMDD.pdf pattern
2025-07-20 20:26:16,692 - __main__ - WARNING - Invalid filename format: Payslip_502_InvalidDate.pdf
2025-07-20 20:26:16,702 - database - WARNING - Logged failed send for employee 502: Invalid filename format: Payslip_502_InvalidDate.pdf does not match Payslip_EmployeeId_YYYYMMDD.pdf pattern
2025-07-20 20:26:16,714 - __main__ - INFO - New valid payslip file detected: Payslip_999_20250720.pdf
2025-07-20 20:26:16,715 - __main__ - INFO - Processing payslip for employee 999, date 20250720
2025-07-20 20:26:16,718 - __main__ - ERROR - Employee not found in database: 999
2025-07-20 20:26:16,725 - database - WARNING - Logged failed send for employee 999: Employee ID 999 not found in database
2025-07-20 20:26:16,726 - __main__ - WARNING - Invalid filename format: Payslip_ABC_20250719.pdf
2025-07-20 20:26:16,735 - database - WARNING - Logged failed send for employee ABC: Invalid filename format: Payslip_ABC_20250719.pdf does not match Payslip_EmployeeId_YYYYMMDD.pdf pattern
2025-07-20 20:29:11,807 - __main__ - INFO - New valid payslip file detected: Payslip_501_20250719.pdf
2025-07-20 20:29:11,808 - __main__ - INFO - Processing payslip for employee 501, date 20250719
2025-07-20 20:29:16,060 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel)
2025-07-20 20:29:16,060 - __main__ - INFO - Successfully sent payslip to Jose Daniel (<EMAIL>)
2025-07-20 20:29:16,063 - __main__ - INFO - Moved processed file to archived directory: archived_payslips\Payslip_501_20250719.pdf
2025-07-20 20:29:16,064 - __main__ - INFO - New valid payslip file detected: Payslip_503_20250713.pdf
2025-07-20 20:29:16,064 - __main__ - INFO - Processing payslip for employee 503, date 20250713
2025-07-20 20:29:19,542 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel Test 2)
2025-07-20 20:29:19,543 - __main__ - INFO - Successfully sent payslip to Jose Daniel Test 2 (<EMAIL>)
2025-07-20 20:29:19,545 - __main__ - INFO - Moved processed file to archived directory: archived_payslips\Payslip_503_20250713.pdf
2025-07-20 20:29:19,546 - __main__ - INFO - New valid payslip file detected: Payslip_503_20250718.pdf
2025-07-20 20:29:19,547 - __main__ - INFO - Processing payslip for employee 503, date 20250718
2025-07-20 20:29:23,221 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel Test 2)
2025-07-20 20:29:23,222 - __main__ - INFO - Successfully sent payslip to Jose Daniel Test 2 (<EMAIL>)
2025-07-20 20:29:23,224 - __main__ - INFO - Moved processed file to archived directory: archived_payslips\Payslip_503_20250718.pdf
2025-07-20 20:29:23,225 - __main__ - INFO - New valid payslip file detected: Payslip_504_20250625.pdf
2025-07-20 20:29:23,226 - __main__ - INFO - Processing payslip for employee 504, date 20250625
2025-07-20 20:29:26,914 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel Test 3)
2025-07-20 20:29:26,915 - __main__ - INFO - Successfully sent payslip to Jose Daniel Test 3 (<EMAIL>)
2025-07-20 20:29:26,917 - __main__ - INFO - Moved processed file to archived directory: archived_payslips\Payslip_504_20250625.pdf
2025-07-20 20:29:26,918 - __main__ - INFO - New valid payslip file detected: Payslip_504_20250717.pdf
2025-07-20 20:29:26,918 - __main__ - INFO - Processing payslip for employee 504, date 20250717
2025-07-20 20:29:30,805 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel Test 3)
2025-07-20 20:29:30,806 - __main__ - INFO - Successfully sent payslip to Jose Daniel Test 3 (<EMAIL>)
2025-07-20 20:29:30,809 - __main__ - INFO - Moved processed file to archived directory: archived_payslips\Payslip_504_20250717.pdf
2025-07-20 22:30:09,190 - config - INFO - Logging configured successfully
2025-07-20 22:30:09,192 - database - INFO - Database initialized successfully
2025-07-20 22:30:09,192 - email_service - INFO - Email service initialized for smtp.gmail.com:587
2025-07-20 22:30:09,193 - __main__ - INFO - SimplePayslipMonitor initialized
2025-07-20 22:30:09,193 - __main__ - INFO - Starting SimplePayslipMonitor...
2025-07-20 22:30:10,849 - email_service - INFO - SMTP connection test successful
2025-07-20 22:30:10,855 - __main__ - INFO - SimplePayslipMonitor is now running. Press Ctrl+C to stop.
2025-07-20 22:30:10,855 - __main__ - INFO - Monitoring directory for new payslip files...
2025-07-20 23:00:46,716 - config - INFO - Logging configured successfully
2025-07-20 23:00:46,719 - database - INFO - Database initialized successfully
2025-07-20 23:00:46,719 - email_service - INFO - Email service initialized for smtp.gmail.com:587
2025-07-20 23:00:46,720 - __main__ - INFO - SimplePayslipMonitor initialized
2025-07-20 23:00:46,721 - __main__ - INFO - Starting SimplePayslipMonitor...
2025-07-20 23:00:48,561 - email_service - INFO - SMTP connection test successful
2025-07-20 23:00:48,566 - __main__ - INFO - SimplePayslipMonitor is now running. Press Ctrl+C to stop.
2025-07-20 23:00:48,566 - __main__ - INFO - Monitoring directory for new payslip files...
2025-07-20 23:00:48,583 - __main__ - INFO - New valid payslip file detected: Payslip_501_20250719.pdf
2025-07-20 23:00:48,583 - __main__ - INFO - Processing payslip for employee 501, date 20250719
2025-07-20 23:00:52,554 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel)
2025-07-20 23:00:52,555 - __main__ - INFO - Successfully sent payslip to Jose Daniel (<EMAIL>)
2025-07-20 23:00:52,557 - __main__ - INFO - Moved processed file to archived directory: archived_payslips\Payslip_501_20250719.pdf
2025-07-20 23:00:52,558 - __main__ - INFO - New valid payslip file detected: Payslip_503_20250718.pdf
2025-07-20 23:00:52,559 - __main__ - INFO - Processing payslip for employee 503, date 20250718
2025-07-20 23:00:56,048 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel Test 2)
2025-07-20 23:00:56,048 - __main__ - INFO - Successfully sent payslip to Jose Daniel Test 2 (<EMAIL>)
2025-07-20 23:00:56,050 - __main__ - INFO - Moved processed file to archived directory: archived_payslips\Payslip_503_20250718.pdf
2025-07-20 23:00:56,051 - __main__ - INFO - New valid payslip file detected: Payslip_504_20250625.pdf
2025-07-20 23:00:56,052 - __main__ - INFO - Processing payslip for employee 504, date 20250625
2025-07-20 23:00:59,721 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel Test 3)
2025-07-20 23:00:59,722 - __main__ - INFO - Successfully sent payslip to Jose Daniel Test 3 (<EMAIL>)
2025-07-20 23:00:59,724 - __main__ - INFO - Moved processed file to archived directory: archived_payslips\Payslip_504_20250625.pdf
2025-07-20 23:09:34,097 - __main__ - INFO - Received interrupt signal, shutting down...
2025-07-20 23:09:34,098 - __main__ - INFO - Stopping SimplePayslipMonitor...
2025-07-20 23:09:34,098 - __main__ - INFO - SimplePayslipMonitor stopped
2025-07-21 00:29:47,024 - config - INFO - Logging configured successfully
2025-07-21 00:29:47,026 - database - INFO - Database initialized successfully
2025-07-21 00:29:47,026 - email_service - INFO - Email service initialized for smtp.gmail.com:587
2025-07-21 00:29:47,027 - __main__ - INFO - SimplePayslipMonitor initialized
2025-07-21 00:29:47,028 - __main__ - INFO - Starting SimplePayslipMonitor...
2025-07-21 00:29:49,277 - email_service - INFO - SMTP connection test successful
2025-07-21 00:29:49,284 - __main__ - INFO - SimplePayslipMonitor is now running. Press Ctrl+C to stop.
2025-07-21 00:29:49,286 - __main__ - INFO - Monitoring directory for new payslip files...
2025-07-21 00:30:19,303 - __main__ - INFO - New valid payslip file detected: Payslip_501_20250719.pdf
2025-07-21 00:30:19,304 - __main__ - INFO - Processing payslip for employee 501, date 20250719
2025-07-21 00:30:22,950 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel)
2025-07-21 00:30:22,950 - __main__ - INFO - Successfully sent payslip to Jose Daniel (<EMAIL>)
2025-07-21 00:30:22,953 - __main__ - INFO - Moved processed file to archived directory: archived_payslips\Payslip_501_20250719_20250721_003022.pdf
2025-07-21 00:36:30,519 - __main__ - INFO - Received interrupt signal, shutting down...
2025-07-21 00:36:30,519 - __main__ - INFO - Stopping SimplePayslipMonitor...
2025-07-21 00:36:30,520 - __main__ - INFO - SimplePayslipMonitor stopped
2025-07-21 08:34:05,151 - config - INFO - Logging configured successfully
2025-07-21 08:34:05,152 - database - INFO - Database initialized successfully (data/payslip_system.db)
2025-07-21 08:34:05,152 - email_service - INFO - Email service initialized for smtp.gmail.com:587
2025-07-21 08:34:05,152 - file_monitor - INFO - File monitor initialized for directory: payslips
2025-07-21 08:34:05,153 - __main__ - INFO - PayslipSender application initialized
2025-07-21 08:34:05,153 - __main__ - INFO - Starting PayslipSender application...
2025-07-21 08:34:07,165 - email_service - INFO - SMTP connection test successful
2025-07-21 08:34:07,170 - file_monitor - INFO - Processing existing files in directory...
2025-07-21 08:34:07,172 - file_monitor - INFO - No existing PDF files found
2025-07-21 08:34:07,174 - __main__ - ERROR - Fatal error: 'handle' must be a _ThreadHandle
2025-07-21 08:35:13,788 - config - INFO - Logging configured successfully
2025-07-21 08:35:13,789 - database - INFO - Database initialized successfully (data/payslip_system.db)
2025-07-21 08:35:13,789 - email_service - INFO - Email service initialized for smtp.gmail.com:587
2025-07-21 08:35:13,789 - file_monitor - INFO - File monitor initialized for directory: payslips
2025-07-21 08:35:13,790 - __main__ - INFO - PayslipSender application initialized
2025-07-21 08:35:13,790 - __main__ - INFO - Starting PayslipSender application...
2025-07-21 08:35:15,795 - email_service - INFO - SMTP connection test successful
2025-07-21 08:35:15,799 - file_monitor - INFO - Processing existing files in directory...
2025-07-21 08:35:15,801 - file_monitor - INFO - No existing PDF files found
2025-07-21 08:35:15,805 - file_monitor - INFO - Started monitoring directory: payslips
2025-07-21 08:35:15,806 - __main__ - INFO - PayslipSender is now running. Press Ctrl+C to stop.
2025-07-21 08:36:04,230 - config - INFO - Logging configured successfully
2025-07-21 08:36:04,233 - database - INFO - Database initialized successfully (sqlite)
2025-07-21 08:36:04,234 - email_service - INFO - Email service initialized for smtp.gmail.com:587
2025-07-21 08:36:04,234 - __main__ - INFO - SimplePayslipMonitor initialized
2025-07-21 08:36:04,234 - __main__ - INFO - Starting SimplePayslipMonitor...
2025-07-21 08:36:05,604 - email_service - INFO - SMTP connection test successful
2025-07-21 08:36:05,606 - __main__ - INFO - SimplePayslipMonitor is now running. Press Ctrl+C to stop.
2025-07-21 08:36:05,606 - __main__ - INFO - Monitoring directory for new payslip files...
2025-07-21 08:36:45,624 - __main__ - INFO - New valid payslip file detected: Payslip_508_20250625.pdf
2025-07-21 08:36:45,625 - __main__ - INFO - Processing payslip for employee 508, date 20250625
2025-07-21 08:36:45,626 - __main__ - ERROR - Employee not found in database: 508
2025-07-21 08:36:45,634 - database - WARNING - Logged failed send for employee 508: Employee ID 508 not found in database
2025-07-21 08:40:22,786 - __main__ - INFO - Received interrupt signal, shutting down...
2025-07-21 08:40:22,787 - __main__ - INFO - Stopping SimplePayslipMonitor...
2025-07-21 08:40:22,787 - __main__ - INFO - SimplePayslipMonitor stopped
2025-07-21 08:40:25,046 - config - INFO - Logging configured successfully
2025-07-21 08:40:25,047 - __main__ - ERROR - Fatal error: Configuration validation failed. Please check your .env file.
2025-07-21 08:44:23,868 - config - INFO - Logging configured successfully
2025-07-21 08:44:23,869 - __main__ - ERROR - Fatal error: Configuration validation failed. Please check your .env file.
2025-07-21 08:44:54,743 - config - INFO - Logging configured successfully
2025-07-21 08:44:54,745 - database - ERROR - Failed to initialize database: ('IM002', '[IM002] [Microsoft][ODBC Driver Manager] Data source name not found and no default driver specified (0) (SQLDriverConnect)')
2025-07-21 08:44:54,745 - __main__ - ERROR - Fatal error: ('IM002', '[IM002] [Microsoft][ODBC Driver Manager] Data source name not found and no default driver specified (0) (SQLDriverConnect)')
2025-07-21 08:45:30,597 - config - INFO - Logging configured successfully
2025-07-21 08:45:40,681 - config - INFO - Logging configured successfully
2025-07-21 08:46:02,954 - database - ERROR - Failed to initialize database: ('08001', '[08001] [Microsoft][ODBC Driver 18 for SQL Server]TCP Provider: No such host is known.\r\n (11001) (SQLDriverConnect); [08001] [Microsoft][ODBC Driver 18 for SQL Server]Login timeout expired (0); [08001] [Microsoft][ODBC Driver 18 for SQL Server]A network-related or instance-specific error has occurred while establishing a connection to sqlserver,1433. Server is not found or not accessible. Check if instance name is correct and if SQL Server is configured to allow remote connections. For more information see SQL Server Books Online. (11001)')
2025-07-21 09:11:28,721 - email_service - INFO - Email service initialized for smtp.gmail.com:587
2025-07-21 09:11:28,721 - __main__ - INFO - PayslipSender initialized for production deployment
2025-07-21 09:11:28,721 - __main__ - INFO - Starting PayslipSender service...
2025-07-21 09:11:30,274 - email_service - INFO - SMTP connection test successful
2025-07-21 09:11:30,325 - __main__ - INFO - Database connection validated
2025-07-21 09:11:30,326 - __main__ - INFO - Monitoring directory: payslips
2025-07-21 09:11:30,326 - __main__ - INFO - PayslipSender service is running
2025-07-21 09:15:52,543 - email_service - INFO - Email service initialized for smtp.gmail.com:587
2025-07-21 09:15:52,543 - __main__ - INFO - PayslipSender initialized for production deployment
2025-07-21 09:15:52,544 - __main__ - INFO - Starting PayslipSender service...
2025-07-21 09:15:54,129 - email_service - INFO - SMTP connection test successful
2025-07-21 09:15:54,159 - __main__ - INFO - Database connection validated
2025-07-21 09:15:54,160 - __main__ - INFO - Monitoring directory: payslips
2025-07-21 09:15:54,160 - __main__ - INFO - PayslipSender service is running
2025-07-21 09:28:03,980 - email_service - INFO - Email service initialized for smtp.gmail.com:587
2025-07-21 09:28:03,981 - __main__ - INFO - PayslipSender initialized for production deployment
2025-07-21 09:28:03,981 - __main__ - INFO - Starting PayslipSender service...
2025-07-21 09:28:06,190 - email_service - INFO - SMTP connection test successful
2025-07-21 09:28:06,239 - __main__ - INFO - Database connection validated
2025-07-21 09:28:06,239 - __main__ - INFO - Monitoring directory: payslips
2025-07-21 09:28:06,239 - __main__ - INFO - PayslipSender service is running
2025-07-21 09:28:24,195 - __main__ - INFO - New payslip file detected: Payslip_508_20250625.pdf
2025-07-21 09:28:24,195 - __main__ - INFO - Processing payslip for employee 508, date 20250625
2025-07-21 09:28:33,209 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-21 09:28:33,210 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-21 09:28:33,211 - __main__ - INFO - Moved processed file to: archived_payslips\Payslip_508_20250625_20250721_092833.pdf
2025-07-21 09:30:23,029 - email_service - INFO - Email service initialized for smtp.gmail.com:587
2025-07-21 09:30:23,030 - __main__ - INFO - PayslipSender initialized for production deployment
2025-07-21 09:30:23,030 - __main__ - INFO - Starting PayslipSender service...
2025-07-21 09:30:25,098 - email_service - INFO - SMTP connection test successful
2025-07-21 09:30:25,124 - __main__ - INFO - Database connection validated
2025-07-21 09:30:25,124 - __main__ - INFO - Monitoring directory: payslips
2025-07-21 09:30:25,124 - __main__ - INFO - PayslipSender service is running
2025-07-21 09:30:25,125 - __main__ - INFO - New payslip file detected: Payslip_508_20250625.pdf
2025-07-21 09:30:25,125 - __main__ - INFO - Processing payslip for employee 508, date 20250625
2025-07-21 09:30:33,484 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-21 09:30:33,485 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-21 09:30:33,486 - __main__ - INFO - Moved processed file to: archived_payslips\Payslip_508_20250625_20250721_093033.pdf
2025-07-21 10:40:34,371 - email_service - INFO - Email service initialized for smtp.gmail.com:587
2025-07-21 10:40:34,371 - __main__ - INFO - PayslipSender initialized for production deployment
2025-07-21 10:40:34,371 - __main__ - INFO - Starting PayslipSender service...
2025-07-21 10:40:36,102 - email_service - INFO - SMTP connection test successful
2025-07-21 10:40:36,106 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 10:40:36,144 - __main__ - INFO - Database connection validated
2025-07-21 10:40:36,144 - __main__ - INFO - Monitoring directory: payslips
2025-07-21 10:40:36,144 - __main__ - INFO - PayslipSender service is running
2025-07-21 10:40:50,009 - email_service - INFO - Email service initialized for smtp.gmail.com:587
2025-07-21 10:40:50,009 - __main__ - INFO - PayslipSender initialized for production deployment
2025-07-21 10:40:50,009 - __main__ - INFO - Starting PayslipSender service...
2025-07-21 10:40:51,338 - email_service - INFO - SMTP connection test successful
2025-07-21 10:40:51,352 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 10:40:51,395 - __main__ - INFO - Database connection validated
2025-07-21 10:40:51,396 - __main__ - INFO - Monitoring directory: payslips
2025-07-21 10:40:51,396 - __main__ - INFO - PayslipSender service is running
2025-07-21 10:42:34,337 - __main__ - INFO - Received signal 2, initiating graceful shutdown...
2025-07-21 10:42:35,751 - __main__ - INFO - Received signal 2, initiating graceful shutdown...
2025-07-21 10:42:40,627 - email_service - INFO - Email service initialized for smtp.gmail.com:587
2025-07-21 10:42:40,627 - __main__ - INFO - PayslipSender initialized for production deployment
2025-07-21 10:42:40,627 - __main__ - INFO - Starting PayslipSender service...
2025-07-21 10:42:42,467 - email_service - INFO - SMTP connection test successful
2025-07-21 10:42:42,480 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 10:42:42,544 - __main__ - INFO - Database connection validated
2025-07-21 10:42:42,544 - __main__ - INFO - Monitoring directory: payslips
2025-07-21 10:42:42,544 - __main__ - INFO - PayslipSender service is running
2025-07-21 10:42:51,400 - __main__ - INFO - PayslipSender service stopped
2025-07-21 10:43:50,388 - email_service - INFO - Email service initialized for smtp.gmail.com:587
2025-07-21 10:43:50,388 - __main__ - INFO - PayslipSender initialized for production deployment
2025-07-21 10:43:50,388 - __main__ - INFO - Starting PayslipSender service...
2025-07-21 10:43:52,414 - email_service - INFO - SMTP connection test successful
2025-07-21 10:43:52,418 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 10:43:52,468 - __main__ - INFO - Database connection validated
2025-07-21 10:43:52,469 - __main__ - INFO - Monitoring directory: C:\Users\<USER>\Desktop\PayslipSender\payslips
2025-07-21 10:43:52,469 - __main__ - INFO - PayslipSender service is running
2025-07-21 10:45:05,963 - __main__ - INFO - Received signal 2, initiating graceful shutdown...
2025-07-21 10:46:47,998 - email_service - INFO - Email service initialized for smtp.gmail.com:587
2025-07-21 10:46:47,998 - __main__ - INFO - PayslipSender initialized for production deployment
2025-07-21 10:46:47,999 - __main__ - INFO - Starting PayslipSender service...
2025-07-21 10:46:49,818 - email_service - INFO - SMTP connection test successful
2025-07-21 10:46:49,827 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 10:46:49,879 - __main__ - INFO - Database connection validated
2025-07-21 10:46:49,880 - __main__ - INFO - Monitoring directory: C:\Users\<USER>\Desktop\PayslipSender\payslips
2025-07-21 10:46:49,880 - __main__ - INFO - PayslipSender service is running
2025-07-21 10:47:07,349 - __main__ - INFO - Received signal 2, initiating graceful shutdown...
2025-07-21 10:47:11,810 - email_service - INFO - Email service initialized for smtp.gmail.com:587
2025-07-21 10:47:11,811 - __main__ - INFO - PayslipSender initialized for production deployment
2025-07-21 10:47:11,811 - __main__ - INFO - Starting PayslipSender service...
2025-07-21 10:47:13,803 - email_service - INFO - SMTP connection test successful
2025-07-21 10:47:13,807 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 10:47:13,861 - __main__ - INFO - Database connection validated
2025-07-21 10:47:13,862 - __main__ - INFO - Monitoring directory: C:\Users\<USER>\Desktop\PayslipSender\payslips
2025-07-21 10:47:13,862 - __main__ - INFO - PayslipSender service is running
2025-07-21 10:47:13,862 - __main__ - INFO - New payslip file detected: Payslip_508_20250625.pdf
2025-07-21 10:47:13,862 - __main__ - INFO - Processing payslip for employee 508, date 20250625
2025-07-21 10:47:13,866 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 10:47:19,882 - __main__ - INFO - PayslipSender service stopped
2025-07-21 10:47:23,948 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-21 10:47:23,949 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-21 10:47:23,951 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_508_20250625_20250721_104723.pdf
2025-07-21 11:01:45,184 - __main__ - INFO - Received signal 2, initiating graceful shutdown...
2025-07-21 11:01:48,781 - email_service - INFO - Email service initialized for smtp.gmail.com:587
2025-07-21 11:01:48,781 - __main__ - INFO - PayslipSender initialized for production deployment
2025-07-21 11:01:48,781 - __main__ - INFO - Starting PayslipSender service...
2025-07-21 11:01:50,404 - email_service - INFO - SMTP connection test successful
2025-07-21 11:01:50,409 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 11:01:50,457 - __main__ - INFO - Database connection validated
2025-07-21 11:01:50,458 - __main__ - INFO - Monitoring directory: C:\Users\<USER>\Desktop\PayslipSender\payslips
2025-07-21 11:01:50,458 - __main__ - INFO - PayslipSender service is running
2025-07-21 11:01:50,458 - __main__ - INFO - New payslip file detected: Payslip_508_20250625.pdf
2025-07-21 11:01:50,458 - __main__ - INFO - Processing payslip for employee 508, date 20250625
2025-07-21 11:01:50,461 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 11:01:53,990 - __main__ - INFO - PayslipSender service stopped
2025-07-21 11:02:01,833 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-21 11:02:01,833 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-21 11:02:01,833 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_508_20250625_20250721_110201.pdf
2025-07-21 11:07:23,221 - __main__ - INFO - Received signal 2, initiating graceful shutdown...
2025-07-21 11:07:31,864 - __main__ - INFO - PayslipSender service stopped
2025-07-21 11:30:19,028 - email_service - INFO - Email service initialized for smtp.gmail.com:587
2025-07-21 11:30:19,028 - __main__ - INFO - PayslipSender initialized for production deployment
2025-07-21 11:30:19,028 - __main__ - INFO - Starting PayslipSender service...
2025-07-21 11:30:21,289 - email_service - INFO - SMTP connection test successful
2025-07-21 11:30:21,303 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 11:30:21,355 - __main__ - INFO - Database connection validated
2025-07-21 11:30:21,356 - __main__ - INFO - Monitoring directory: payslips
2025-07-21 11:30:21,356 - __main__ - INFO - PayslipSender service is running
2025-07-21 11:30:21,356 - __main__ - INFO - New payslip file detected: Payslip_508_20250625.pdf
2025-07-21 11:30:21,356 - __main__ - INFO - Processing payslip for employee 508, date 20250625
2025-07-21 11:30:21,359 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 11:30:31,272 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-21 11:30:31,272 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-21 11:30:31,272 - __main__ - INFO - Moved processed file to: archived_payslips\Payslip_508_20250625_20250721_113031.pdf
2025-07-21 11:56:52,894 - email_service - INFO - Email service initialized for smtp.gmail.com:587
2025-07-21 11:56:52,894 - __main__ - INFO - PayslipSender initialized for production deployment
2025-07-21 11:56:52,894 - __main__ - INFO - Starting PayslipSender service...
2025-07-21 11:56:55,035 - email_service - INFO - SMTP connection test successful
2025-07-21 11:56:55,049 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 11:56:55,210 - __main__ - INFO - Database connection validated
2025-07-21 11:56:55,210 - __main__ - INFO - Monitoring directory: C:\Users\<USER>\Desktop\PayslipSender\payslips
2025-07-21 11:56:55,210 - __main__ - INFO - PayslipSender service is running
2025-07-21 11:57:02,239 - __main__ - INFO - Received signal 2, initiating graceful shutdown...
2025-07-21 11:57:33,147 - email_service - INFO - Email service initialized for smtp.gmail.com:587
2025-07-21 11:57:33,147 - __main__ - INFO - PayslipSender initialized for production deployment
2025-07-21 11:57:33,147 - __main__ - INFO - Starting PayslipSender service...
2025-07-21 11:57:35,294 - email_service - INFO - SMTP connection test successful
2025-07-21 11:57:35,310 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 11:57:35,357 - __main__ - INFO - Database connection validated
2025-07-21 11:57:35,357 - __main__ - INFO - Monitoring directory: C:\Users\<USER>\Desktop\PayslipSender\payslips
2025-07-21 11:57:35,357 - __main__ - INFO - PayslipSender service is running
2025-07-21 11:58:19,084 - email_service - INFO - Email service initialized for smtp.gmail.com:587
2025-07-21 11:58:19,084 - __main__ - INFO - PayslipSender initialized for production deployment
2025-07-21 11:58:19,084 - __main__ - INFO - Starting PayslipSender service...
2025-07-21 11:58:19,116 - email_service - INFO - Email service initialized for smtp.gmail.com:587
2025-07-21 11:58:19,116 - email_service - INFO - Email service initialized for smtp.gmail.com:587
2025-07-21 11:58:19,116 - __main__ - INFO - PayslipSender initialized for production deployment
2025-07-21 11:58:19,116 - __main__ - INFO - Starting PayslipSender service...
2025-07-21 11:58:19,116 - __main__ - INFO - PayslipSender initialized for production deployment
2025-07-21 11:58:19,116 - __main__ - INFO - Starting PayslipSender service...
2025-07-21 11:58:19,360 - email_service - INFO - Email service initialized for smtp.gmail.com:587
2025-07-21 11:58:19,360 - __main__ - INFO - PayslipSender initialized for production deployment
2025-07-21 11:58:19,360 - __main__ - INFO - Starting PayslipSender service...
2025-07-21 11:58:20,695 - email_service - INFO - SMTP connection test successful
2025-07-21 11:58:20,715 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 11:58:20,758 - __main__ - INFO - Database connection validated
2025-07-21 11:58:20,758 - __main__ - INFO - Monitoring directory: C:\Users\<USER>\Desktop\PayslipSender\payslips
2025-07-21 11:58:20,758 - __main__ - INFO - PayslipSender service is running
2025-07-21 11:58:20,936 - email_service - INFO - SMTP connection test successful
2025-07-21 11:58:20,963 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 11:58:21,016 - email_service - INFO - SMTP connection test successful
2025-07-21 11:58:21,016 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 11:58:21,016 - __main__ - INFO - Database connection validated
2025-07-21 11:58:21,016 - __main__ - INFO - Monitoring directory: C:\Users\<USER>\Desktop\PayslipSender\payslips
2025-07-21 11:58:21,016 - __main__ - INFO - PayslipSender service is running
2025-07-21 11:58:21,061 - __main__ - INFO - Database connection validated
2025-07-21 11:58:21,061 - __main__ - INFO - Monitoring directory: C:\Users\<USER>\Desktop\PayslipSender\payslips
2025-07-21 11:58:21,061 - __main__ - INFO - PayslipSender service is running
2025-07-21 11:58:21,366 - email_service - INFO - SMTP connection test successful
2025-07-21 11:58:21,366 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 11:58:21,414 - __main__ - INFO - Database connection validated
2025-07-21 11:58:21,414 - __main__ - INFO - Monitoring directory: C:\Users\<USER>\Desktop\PayslipSender\payslips
2025-07-21 11:58:21,414 - __main__ - INFO - PayslipSender service is running
2025-07-21 11:59:54,940 - email_service - INFO - Email service initialized for smtp.gmail.com:587
2025-07-21 11:59:54,940 - __main__ - INFO - PayslipSender initialized for production deployment
2025-07-21 11:59:54,940 - __main__ - INFO - Starting PayslipSender service...
2025-07-21 11:59:56,532 - email_service - INFO - SMTP connection test successful
2025-07-21 11:59:56,548 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 11:59:56,612 - __main__ - INFO - Database connection validated
2025-07-21 11:59:56,612 - __main__ - INFO - Monitoring directory: C:\Users\<USER>\Desktop\PayslipSender\payslips
2025-07-21 11:59:56,612 - __main__ - INFO - PayslipSender service is running
2025-07-21 12:39:24,720 - email_service - INFO - Email service initialized for smtp.gmail.com:587
2025-07-21 12:39:24,720 - __main__ - INFO - PayslipSender initialized for production deployment
2025-07-21 12:39:24,720 - __main__ - INFO - Starting PayslipSender service...
2025-07-21 12:39:26,506 - email_service - INFO - SMTP connection test successful
2025-07-21 12:39:26,510 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 12:39:26,560 - __main__ - INFO - Database connection validated
2025-07-21 12:39:26,560 - __main__ - INFO - Monitoring directory: C:\Users\<USER>\Desktop\PayslipSender\payslips
2025-07-21 12:39:26,560 - __main__ - INFO - PayslipSender service is running
2025-07-21 12:39:56,564 - __main__ - INFO - New payslip file detected: Payslip_508_20250625.pdf
2025-07-21 12:39:56,564 - __main__ - INFO - Processing payslip for employee 508, date 20250625
2025-07-21 12:39:56,567 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 12:39:56,743 - __main__ - INFO - New payslip file detected: Payslip_508_20250625.pdf
2025-07-21 12:39:56,743 - __main__ - INFO - Processing payslip for employee 508, date 20250625
2025-07-21 12:39:56,759 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 12:40:05,504 - __main__ - INFO - New payslip file detected: Payslip_508_20250625.pdf
2025-07-21 12:40:05,504 - __main__ - INFO - Processing payslip for employee 508, date 20250625
2025-07-21 12:40:05,504 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 12:40:06,914 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-21 12:40:06,914 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-21 12:40:06,914 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_508_20250625_20250721_124006.pdf
2025-07-21 12:40:07,182 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-21 12:40:07,182 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-21 12:40:07,182 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_508_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_508_20250625_20250721_124007.pdf'
2025-07-21 12:40:13,801 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-21 12:40:13,801 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-21 12:40:13,801 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_508_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_508_20250625_20250721_124013.pdf'
2025-07-21 12:44:36,937 - __main__ - INFO - New payslip file detected: Payslip_501_20250625.pdf
2025-07-21 12:44:36,937 - __main__ - INFO - Processing payslip for employee 501, date 20250625
2025-07-21 12:44:36,942 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 12:44:37,208 - __main__ - INFO - New payslip file detected: Payslip_501_20250625.pdf
2025-07-21 12:44:37,208 - __main__ - INFO - Processing payslip for employee 501, date 20250625
2025-07-21 12:44:37,214 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 12:44:43,824 - __main__ - INFO - New payslip file detected: Payslip_501_20250625.pdf
2025-07-21 12:44:43,824 - __main__ - INFO - Processing payslip for employee 501, date 20250625
2025-07-21 12:44:43,824 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 12:44:46,142 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel )
2025-07-21 12:44:46,142 - __main__ - INFO - Successfully sent payslip to Jose Daniel  (<EMAIL>)
2025-07-21 12:44:46,142 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_501_20250625.pdf
2025-07-21 12:44:46,142 - __main__ - INFO - New payslip file detected: Payslip_502_20250625.pdf
2025-07-21 12:44:46,142 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-21 12:44:46,142 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 12:44:46,844 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel )
2025-07-21 12:44:46,844 - __main__ - INFO - Successfully sent payslip to Jose Daniel  (<EMAIL>)
2025-07-21 12:44:46,844 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_501_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_501_20250625_20250721_124446.pdf'
2025-07-21 12:44:46,844 - __main__ - INFO - New payslip file detected: Payslip_502_20250625.pdf
2025-07-21 12:44:46,844 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-21 12:44:46,860 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 12:44:50,911 - __main__ - INFO - New payslip file detected: Payslip_502_20250625.pdf
2025-07-21 12:44:50,911 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-21 12:44:50,915 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 12:44:51,174 - __main__ - INFO - New payslip file detected: Payslip_502_20250625.pdf
2025-07-21 12:44:51,174 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-21 12:44:51,186 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 12:44:51,215 - __main__ - INFO - New payslip file detected: Payslip_502_20250625.pdf
2025-07-21 12:44:51,215 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-21 12:44:51,218 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 12:44:51,571 - __main__ - INFO - New payslip file detected: Payslip_502_20250625.pdf
2025-07-21 12:44:51,571 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-21 12:44:51,571 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 12:44:52,295 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel )
2025-07-21 12:44:52,295 - __main__ - INFO - Successfully sent payslip to Jose Daniel  (<EMAIL>)
2025-07-21 12:44:52,295 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_501_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_501_20250625_20250721_124452.pdf'
2025-07-21 12:44:52,295 - __main__ - INFO - New payslip file detected: Payslip_502_20250625.pdf
2025-07-21 12:44:52,295 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-21 12:44:52,311 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 12:44:54,860 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-21 12:44:54,860 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-21 12:44:54,860 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_502_20250625.pdf
2025-07-21 12:44:54,860 - __main__ - INFO - New payslip file detected: Payslip_503_20250625.pdf
2025-07-21 12:44:54,860 - __main__ - INFO - Processing payslip for employee 503, date 20250625
2025-07-21 12:44:54,860 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 12:44:55,767 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-21 12:44:55,767 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-21 12:44:55,767 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_502_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_502_20250625_20250721_124455.pdf'
2025-07-21 12:44:55,767 - __main__ - INFO - New payslip file detected: Payslip_503_20250625.pdf
2025-07-21 12:44:55,767 - __main__ - INFO - Processing payslip for employee 503, date 20250625
2025-07-21 12:44:55,783 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 12:45:01,226 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-21 12:45:01,226 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-21 12:45:01,226 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_502_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_502_20250625_20250721_124501.pdf'
2025-07-21 12:45:01,226 - __main__ - INFO - New payslip file detected: Payslip_503_20250625.pdf
2025-07-21 12:45:01,226 - __main__ - INFO - Processing payslip for employee 503, date 20250625
2025-07-21 12:45:01,226 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 12:45:01,930 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-21 12:45:01,939 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-21 12:45:01,939 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_502_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_502_20250625_20250721_124501.pdf'
2025-07-21 12:45:01,939 - __main__ - INFO - New payslip file detected: Payslip_503_20250625.pdf
2025-07-21 12:45:01,939 - __main__ - INFO - Processing payslip for employee 503, date 20250625
2025-07-21 12:45:01,955 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 12:45:02,825 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-21 12:45:02,825 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-21 12:45:02,825 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_502_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_502_20250625_20250721_124502.pdf'
2025-07-21 12:45:02,825 - __main__ - INFO - New payslip file detected: Payslip_503_20250625.pdf
2025-07-21 12:45:02,825 - __main__ - INFO - Processing payslip for employee 503, date 20250625
2025-07-21 12:45:02,841 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 12:45:03,226 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-21 12:45:03,226 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-21 12:45:03,226 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_502_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_502_20250625_20250721_124503.pdf'
2025-07-21 12:45:03,226 - __main__ - INFO - New payslip file detected: Payslip_503_20250625.pdf
2025-07-21 12:45:03,226 - __main__ - INFO - Processing payslip for employee 503, date 20250625
2025-07-21 12:45:03,226 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 12:45:04,027 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-21 12:45:04,027 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-21 12:45:04,027 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_502_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_502_20250625_20250721_124504.pdf'
2025-07-21 12:45:04,027 - __main__ - INFO - New payslip file detected: Payslip_503_20250625.pdf
2025-07-21 12:45:04,027 - __main__ - INFO - Processing payslip for employee 503, date 20250625
2025-07-21 12:45:04,043 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 12:45:05,355 - email_service - INFO - Email sent <NAME_EMAIL> (Test 2 )
2025-07-21 12:45:05,357 - __main__ - INFO - Successfully sent payslip to Test 2  (<EMAIL>)
2025-07-21 12:45:05,357 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_503_20250625.pdf
2025-07-21 12:45:05,357 - __main__ - INFO - New payslip file detected: Payslip_504_20250625.pdf
2025-07-21 12:45:05,357 - __main__ - INFO - Processing payslip for employee 504, date 20250625
2025-07-21 12:45:05,368 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 12:45:06,043 - email_service - INFO - Email sent <NAME_EMAIL> (Test 2 )
2025-07-21 12:45:06,043 - __main__ - INFO - Successfully sent payslip to Test 2  (<EMAIL>)
2025-07-21 12:45:06,043 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_503_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_503_20250625_20250721_124506.pdf'
2025-07-21 12:45:06,043 - __main__ - INFO - New payslip file detected: Payslip_504_20250625.pdf
2025-07-21 12:45:06,043 - __main__ - INFO - Processing payslip for employee 504, date 20250625
2025-07-21 12:45:06,056 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 12:45:11,153 - email_service - INFO - Email sent <NAME_EMAIL> (Test 2 )
2025-07-21 12:45:11,153 - __main__ - INFO - Successfully sent payslip to Test 2  (<EMAIL>)
2025-07-21 12:45:11,153 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_503_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_503_20250625_20250721_124511.pdf'
2025-07-21 12:45:11,153 - __main__ - INFO - New payslip file detected: Payslip_504_20250625.pdf
2025-07-21 12:45:11,153 - __main__ - INFO - Processing payslip for employee 504, date 20250625
2025-07-21 12:45:11,153 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 12:45:12,013 - email_service - INFO - Email sent <NAME_EMAIL> (Test 2 )
2025-07-21 12:45:12,013 - __main__ - INFO - Successfully sent payslip to Test 2  (<EMAIL>)
2025-07-21 12:45:12,029 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_503_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_503_20250625_20250721_124512.pdf'
2025-07-21 12:45:12,029 - __main__ - INFO - New payslip file detected: Payslip_504_20250625.pdf
2025-07-21 12:45:12,029 - __main__ - INFO - Processing payslip for employee 504, date 20250625
2025-07-21 12:45:12,029 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 12:45:12,574 - email_service - INFO - Email sent <NAME_EMAIL> (Test 2 )
2025-07-21 12:45:12,574 - __main__ - INFO - Successfully sent payslip to Test 2  (<EMAIL>)
2025-07-21 12:45:12,574 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_503_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_503_20250625_20250721_124512.pdf'
2025-07-21 12:45:12,574 - __main__ - INFO - New payslip file detected: Payslip_504_20250625.pdf
2025-07-21 12:45:12,574 - __main__ - INFO - Processing payslip for employee 504, date 20250625
2025-07-21 12:45:12,590 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 12:45:13,397 - email_service - INFO - Email sent <NAME_EMAIL> (Test 2 )
2025-07-21 12:45:13,397 - __main__ - INFO - Successfully sent payslip to Test 2  (<EMAIL>)
2025-07-21 12:45:13,397 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_503_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_503_20250625_20250721_124513.pdf'
2025-07-21 12:45:13,397 - __main__ - INFO - New payslip file detected: Payslip_504_20250625.pdf
2025-07-21 12:45:13,397 - __main__ - INFO - Processing payslip for employee 504, date 20250625
2025-07-21 12:45:13,407 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 12:45:14,432 - email_service - INFO - Email sent <NAME_EMAIL> (Iverson)
2025-07-21 12:45:14,432 - __main__ - INFO - Successfully sent payslip to Iverson (<EMAIL>)
2025-07-21 12:45:14,432 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_504_20250625.pdf
2025-07-21 12:45:14,432 - __main__ - INFO - New payslip file detected: Payslip_505_20250625.pdf
2025-07-21 12:45:14,432 - __main__ - INFO - Processing payslip for employee 505, date 20250625
2025-07-21 12:45:14,432 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 12:45:15,183 - email_service - INFO - Email sent <NAME_EMAIL> (Iverson)
2025-07-21 12:45:15,183 - __main__ - INFO - Successfully sent payslip to Iverson (<EMAIL>)
2025-07-21 12:45:15,183 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_504_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_504_20250625_20250721_124515.pdf'
2025-07-21 12:45:15,183 - __main__ - INFO - New payslip file detected: Payslip_505_20250625.pdf
2025-07-21 12:45:15,183 - __main__ - INFO - Processing payslip for employee 505, date 20250625
2025-07-21 12:45:15,191 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 12:45:15,886 - email_service - INFO - Email sent <NAME_EMAIL> (Test 2 )
2025-07-21 12:45:15,902 - __main__ - INFO - Successfully sent payslip to Test 2  (<EMAIL>)
2025-07-21 12:45:15,902 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_503_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_503_20250625_20250721_124515.pdf'
2025-07-21 12:45:15,902 - __main__ - INFO - New payslip file detected: Payslip_504_20250625.pdf
2025-07-21 12:45:15,902 - __main__ - INFO - Processing payslip for employee 504, date 20250625
2025-07-21 12:45:15,902 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 12:45:15,902 - email_service - ERROR - Error attaching PDF file C:\Users\<USER>\Desktop\PayslipSender\payslips\Payslip_504_20250625.pdf: PDF file not found: C:\Users\<USER>\Desktop\PayslipSender\payslips\Payslip_504_20250625.pdf
2025-07-21 12:45:15,902 - email_service - ERROR - Unexpected error sending <NAME_EMAIL>: PDF file not found: C:\Users\<USER>\Desktop\PayslipSender\payslips\Payslip_504_20250625.pdf
2025-07-21 12:45:15,902 - __main__ - ERROR - Failed to send payslip to Iverson: Failed to send email
2025-07-21 12:45:15,902 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 12:45:15,933 - __main__ - WARNING - Logged failed send for employee 504: Failed to send email
2025-07-21 12:45:15,933 - __main__ - INFO - New payslip file detected: Payslip_505_20250625.pdf
2025-07-21 12:45:15,933 - __main__ - INFO - Processing payslip for employee 505, date 20250625
2025-07-21 12:45:15,949 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 12:45:19,762 - email_service - INFO - Email sent <NAME_EMAIL> (Iverson)
2025-07-21 12:45:19,762 - __main__ - INFO - Successfully sent payslip to Iverson (<EMAIL>)
2025-07-21 12:45:19,762 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_504_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_504_20250625_20250721_124519.pdf'
2025-07-21 12:45:19,762 - __main__ - INFO - New payslip file detected: Payslip_505_20250625.pdf
2025-07-21 12:45:19,762 - __main__ - INFO - Processing payslip for employee 505, date 20250625
2025-07-21 12:45:19,762 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 12:45:21,348 - email_service - INFO - Email sent <NAME_EMAIL> (Iverson)
2025-07-21 12:45:21,348 - __main__ - INFO - Successfully sent payslip to Iverson (<EMAIL>)
2025-07-21 12:45:21,361 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_504_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_504_20250625_20250721_124521.pdf'
2025-07-21 12:45:21,361 - __main__ - INFO - New payslip file detected: Payslip_505_20250625.pdf
2025-07-21 12:45:21,361 - __main__ - INFO - Processing payslip for employee 505, date 20250625
2025-07-21 12:45:21,361 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 12:45:21,825 - email_service - INFO - Email sent <NAME_EMAIL> (Iverson)
2025-07-21 12:45:21,825 - __main__ - INFO - Successfully sent payslip to Iverson (<EMAIL>)
2025-07-21 12:45:21,825 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_504_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_504_20250625_20250721_124521.pdf'
2025-07-21 12:45:21,825 - __main__ - INFO - New payslip file detected: Payslip_505_20250625.pdf
2025-07-21 12:45:21,825 - __main__ - INFO - Processing payslip for employee 505, date 20250625
2025-07-21 12:45:21,841 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 12:45:22,899 - email_service - INFO - Email sent <NAME_EMAIL> (Iverson)
2025-07-21 12:45:22,899 - __main__ - INFO - Successfully sent payslip to Iverson (<EMAIL>)
2025-07-21 12:45:22,899 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_504_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_504_20250625_20250721_124522.pdf'
2025-07-21 12:45:22,899 - __main__ - INFO - New payslip file detected: Payslip_505_20250625.pdf
2025-07-21 12:45:22,899 - __main__ - INFO - Processing payslip for employee 505, date 20250625
2025-07-21 12:45:22,905 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 12:45:23,688 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-21 12:45:23,688 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-21 12:45:23,688 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_505_20250625.pdf
2025-07-21 12:45:24,376 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-21 12:45:24,376 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-21 12:45:24,376 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_505_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_505_20250625_20250721_124524.pdf'
2025-07-21 12:45:25,315 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-21 12:45:25,315 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-21 12:45:25,315 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_505_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_505_20250625_20250721_124525.pdf'
2025-07-21 12:45:25,315 - __main__ - INFO - New payslip file detected: Payslip_508_20250625.pdf
2025-07-21 12:45:25,315 - __main__ - INFO - Processing payslip for employee 508, date 20250625
2025-07-21 12:45:25,315 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 12:45:29,633 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-21 12:45:29,633 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-21 12:45:29,633 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_505_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_505_20250625_20250721_124529.pdf'
2025-07-21 12:45:29,633 - __main__ - INFO - New payslip file detected: Payslip_508_20250625.pdf
2025-07-21 12:45:29,633 - __main__ - INFO - Processing payslip for employee 508, date 20250625
2025-07-21 12:45:29,639 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 12:45:30,795 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-21 12:45:30,808 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-21 12:45:30,808 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_505_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_505_20250625_20250721_124530.pdf'
2025-07-21 12:45:30,808 - __main__ - INFO - New payslip file detected: Payslip_508_20250625.pdf
2025-07-21 12:45:30,810 - __main__ - INFO - Processing payslip for employee 508, date 20250625
2025-07-21 12:45:30,813 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 12:45:31,502 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-21 12:45:31,502 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-21 12:45:31,502 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_505_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_505_20250625_20250721_124531.pdf'
2025-07-21 12:45:32,355 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-21 12:45:32,355 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-21 12:45:32,355 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_505_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_505_20250625_20250721_124532.pdf'
2025-07-21 12:45:32,355 - __main__ - INFO - New payslip file detected: Payslip_508_20250625.pdf
2025-07-21 12:45:32,355 - __main__ - INFO - Processing payslip for employee 508, date 20250625
2025-07-21 12:45:32,366 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 12:45:34,495 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-21 12:45:34,495 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-21 12:45:34,511 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_508_20250625_20250721_124534.pdf
2025-07-21 12:45:38,716 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-21 12:45:38,716 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-21 12:45:38,716 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_508_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_508_20250625_20250721_124538.pdf'
2025-07-21 12:45:39,692 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-21 12:45:39,692 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-21 12:45:39,692 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_508_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_508_20250625_20250721_124539.pdf'
2025-07-21 12:45:41,214 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-21 12:45:41,214 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-21 12:45:41,214 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_508_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_508_20250625_20250721_124541.pdf'
2025-07-21 12:58:38,753 - __main__ - INFO - New payslip file detected: Payslip_501_20250625.pdf
2025-07-21 12:58:38,754 - __main__ - INFO - Processing payslip for employee 501, date 20250625
2025-07-21 12:58:38,757 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 12:58:39,737 - __main__ - INFO - New payslip file detected: Payslip_501_20250625.pdf
2025-07-21 12:58:39,737 - __main__ - INFO - Processing payslip for employee 501, date 20250625
2025-07-21 12:58:39,737 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 12:58:41,258 - __main__ - INFO - New payslip file detected: Payslip_501_20250625.pdf
2025-07-21 12:58:41,258 - __main__ - INFO - Processing payslip for employee 501, date 20250625
2025-07-21 12:58:41,258 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 12:58:48,293 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel )
2025-07-21 12:58:48,293 - __main__ - INFO - Successfully sent payslip to Jose Daniel  (<EMAIL>)
2025-07-21 12:58:48,293 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_501_20250625_20250721_125848.pdf
2025-07-21 12:58:49,455 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel )
2025-07-21 12:58:49,455 - __main__ - INFO - Successfully sent payslip to Jose Daniel  (<EMAIL>)
2025-07-21 12:58:49,455 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_501_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_501_20250625_20250721_125849.pdf'
2025-07-21 12:58:50,726 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel )
2025-07-21 12:58:50,728 - __main__ - INFO - Successfully sent payslip to Jose Daniel  (<EMAIL>)
2025-07-21 12:58:50,729 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_501_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_501_20250625_20250721_125850.pdf'
2025-07-21 13:06:01,765 - __main__ - INFO - Received signal 2, initiating graceful shutdown...
2025-07-21 13:06:34,577 - __main__ - INFO - New payslip file detected: Payslip_501_20250625.pdf
2025-07-21 13:06:34,577 - __main__ - INFO - Processing payslip for employee 501, date 20250625
2025-07-21 13:06:34,577 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 13:08:23,450 - email_service - INFO - Email service initialized for smtp.gmail.com:587
2025-07-21 13:08:23,452 - __main__ - INFO - PayslipSender initialized for production deployment
2025-07-21 13:08:23,452 - __main__ - INFO - Starting PayslipSender service...
2025-07-21 13:08:25,030 - email_service - INFO - SMTP connection test successful
2025-07-21 13:08:25,030 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 13:08:25,070 - __main__ - INFO - Database connection validated
2025-07-21 13:08:25,070 - __main__ - INFO - Monitoring directory: C:\Users\<USER>\Desktop\PayslipSender\payslips
2025-07-21 13:08:25,070 - __main__ - INFO - PayslipSender service is running
2025-07-21 13:08:25,071 - __main__ - INFO - New payslip file detected: Payslip_501_20250625.pdf
2025-07-21 13:08:25,071 - __main__ - INFO - Processing payslip for employee 501, date 20250625
2025-07-21 13:08:25,074 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 13:08:36,897 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel )
2025-07-21 13:08:36,897 - __main__ - INFO - Successfully sent payslip to Jose Daniel  (<EMAIL>)
2025-07-21 13:08:36,897 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_501_20250625_20250721_130836.pdf
2025-07-21 13:08:36,897 - __main__ - INFO - New payslip file detected: Payslip_502_20250625.pdf
2025-07-21 13:08:36,897 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-21 13:08:36,897 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 13:08:55,614 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-21 13:08:55,614 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-21 13:08:55,614 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_502_20250625_20250721_130855.pdf
2025-07-21 13:08:55,614 - __main__ - INFO - New payslip file detected: Payslip_503_20250625.pdf
2025-07-21 13:08:55,614 - __main__ - INFO - Processing payslip for employee 503, date 20250625
2025-07-21 13:08:55,630 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 13:09:05,764 - email_service - INFO - Email sent <NAME_EMAIL> (Test 2 )
2025-07-21 13:09:05,764 - __main__ - INFO - Successfully sent payslip to Test 2  (<EMAIL>)
2025-07-21 13:09:05,764 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_503_20250625_20250721_130905.pdf
2025-07-21 13:09:05,764 - __main__ - INFO - New payslip file detected: Payslip_504_20250625.pdf
2025-07-21 13:09:05,765 - __main__ - INFO - Processing payslip for employee 504, date 20250625
2025-07-21 13:09:05,767 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 13:09:16,567 - email_service - INFO - Email sent <NAME_EMAIL> (Iverson)
2025-07-21 13:09:16,567 - __main__ - INFO - Successfully sent payslip to Iverson (<EMAIL>)
2025-07-21 13:09:16,567 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_504_20250625_20250721_130916.pdf
2025-07-21 13:09:16,567 - __main__ - INFO - New payslip file detected: Payslip_505_20250625.pdf
2025-07-21 13:09:16,567 - __main__ - INFO - Processing payslip for employee 505, date 20250625
2025-07-21 13:09:16,579 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 13:09:26,840 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-21 13:09:26,840 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-21 13:09:26,841 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_505_20250625_20250721_130926.pdf
2025-07-21 13:09:26,842 - __main__ - INFO - New payslip file detected: Payslip_508_20250625.pdf
2025-07-21 13:09:26,842 - __main__ - INFO - Processing payslip for employee 508, date 20250625
2025-07-21 13:09:26,845 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 13:09:36,880 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-21 13:09:36,881 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-21 13:09:36,883 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_508_20250625_20250721_130936.pdf
2025-07-21 13:10:35,913 - email_service - INFO - Email service initialized for smtp.gmail.com:587
2025-07-21 13:10:35,913 - __main__ - INFO - PayslipSender initialized for production deployment
2025-07-21 13:10:35,913 - __main__ - INFO - Starting PayslipSender service...
2025-07-21 13:10:37,451 - email_service - INFO - SMTP connection test successful
2025-07-21 13:10:37,451 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 13:10:37,521 - __main__ - INFO - Database connection validated
2025-07-21 13:10:37,521 - __main__ - INFO - Monitoring directory: C:\Users\<USER>\Desktop\PayslipSender\payslips
2025-07-21 13:10:37,521 - __main__ - INFO - PayslipSender service is running
2025-07-21 13:10:37,523 - __main__ - INFO - New payslip file detected: Payslip_501_20250625.pdf
2025-07-21 13:10:37,523 - __main__ - INFO - Processing payslip for employee 501, date 20250625
2025-07-21 13:10:37,525 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 13:10:47,586 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel )
2025-07-21 13:10:47,586 - __main__ - INFO - Successfully sent payslip to Jose Daniel  (<EMAIL>)
2025-07-21 13:10:47,590 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_501_20250625_20250721_131047.pdf
2025-07-21 13:10:47,590 - __main__ - INFO - New payslip file detected: Payslip_502_20250625.pdf
2025-07-21 13:10:47,590 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-21 13:10:47,590 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 13:10:57,585 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-21 13:10:57,585 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-21 13:10:57,585 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_502_20250625_20250721_131057.pdf
2025-07-21 13:10:57,585 - __main__ - INFO - New payslip file detected: Payslip_503_20250625.pdf
2025-07-21 13:10:57,585 - __main__ - INFO - Processing payslip for employee 503, date 20250625
2025-07-21 13:10:57,585 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 13:11:07,588 - email_service - INFO - Email sent <NAME_EMAIL> (Test 2 )
2025-07-21 13:11:07,588 - __main__ - INFO - Successfully sent payslip to Test 2  (<EMAIL>)
2025-07-21 13:11:07,588 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_503_20250625_20250721_131107.pdf
2025-07-21 13:11:07,588 - __main__ - INFO - New payslip file detected: Payslip_504_20250625.pdf
2025-07-21 13:11:07,588 - __main__ - INFO - Processing payslip for employee 504, date 20250625
2025-07-21 13:11:07,604 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 13:11:17,837 - email_service - INFO - Email sent <NAME_EMAIL> (Iverson)
2025-07-21 13:11:17,837 - __main__ - INFO - Successfully sent payslip to Iverson (<EMAIL>)
2025-07-21 13:11:17,853 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_504_20250625_20250721_131117.pdf
2025-07-21 13:11:17,853 - __main__ - INFO - New payslip file detected: Payslip_505_20250625.pdf
2025-07-21 13:11:17,853 - __main__ - INFO - Processing payslip for employee 505, date 20250625
2025-07-21 13:11:17,853 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 13:11:27,713 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-21 13:11:27,713 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-21 13:11:27,713 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_505_20250625_20250721_131127.pdf
2025-07-21 13:11:27,713 - __main__ - INFO - New payslip file detected: Payslip_508_20250625.pdf
2025-07-21 13:11:27,713 - __main__ - INFO - Processing payslip for employee 508, date 20250625
2025-07-21 13:11:27,713 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 13:11:37,602 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-21 13:11:37,602 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-21 13:11:37,602 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_508_20250625_20250721_131137.pdf
2025-07-21 13:16:48,364 - __main__ - INFO - Received signal 2, initiating graceful shutdown...
2025-07-21 13:16:48,955 - __main__ - INFO - PayslipSender service stopped
2025-07-21 13:17:27,605 - email_service - INFO - Email service initialized for smtp.gmail.com:587
2025-07-21 13:17:27,605 - __main__ - INFO - PayslipSender initialized for production deployment
2025-07-21 13:17:27,605 - __main__ - INFO - Starting PayslipSender service...
2025-07-21 13:17:29,624 - email_service - INFO - SMTP connection test successful
2025-07-21 13:17:29,640 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 13:17:29,687 - __main__ - INFO - Database connection validated
2025-07-21 13:17:29,687 - __main__ - INFO - Monitoring directory: C:\Users\<USER>\Desktop\PayslipSender\payslips
2025-07-21 13:17:29,687 - __main__ - INFO - PayslipSender service is running
2025-07-21 13:17:39,689 - __main__ - INFO - New payslip file detected: Payslip_501_20250625.pdf
2025-07-21 13:17:39,689 - __main__ - INFO - Processing payslip for employee 501, date 20250625
2025-07-21 13:17:39,689 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 13:17:48,858 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel )
2025-07-21 13:17:48,874 - __main__ - INFO - Successfully sent payslip to Jose Daniel  (<EMAIL>)
2025-07-21 13:17:48,874 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_501_20250625_20250721_131748.pdf
2025-07-21 13:17:48,874 - __main__ - INFO - New payslip file detected: Payslip_502_20250625.pdf
2025-07-21 13:17:48,874 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-21 13:17:48,874 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 13:17:58,016 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-21 13:17:58,016 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-21 13:17:58,032 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_502_20250625_20250721_131758.pdf
2025-07-21 13:17:58,032 - __main__ - INFO - New payslip file detected: Payslip_503_20250625.pdf
2025-07-21 13:17:58,032 - __main__ - INFO - Processing payslip for employee 503, date 20250625
2025-07-21 13:17:58,032 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 13:18:06,765 - email_service - INFO - Email sent <NAME_EMAIL> (Test 2 )
2025-07-21 13:18:06,765 - __main__ - INFO - Successfully sent payslip to Test 2  (<EMAIL>)
2025-07-21 13:18:06,766 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_503_20250625_20250721_131806.pdf
2025-07-21 13:18:06,767 - __main__ - INFO - New payslip file detected: Payslip_504_20250625.pdf
2025-07-21 13:18:06,767 - __main__ - INFO - Processing payslip for employee 504, date 20250625
2025-07-21 13:18:06,770 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 13:18:15,231 - email_service - INFO - Email sent <NAME_EMAIL> (Iverson)
2025-07-21 13:18:15,231 - __main__ - INFO - Successfully sent payslip to Iverson (<EMAIL>)
2025-07-21 13:18:15,238 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_504_20250625_20250721_131815.pdf
2025-07-21 13:18:15,240 - __main__ - INFO - New payslip file detected: Payslip_505_20250625.pdf
2025-07-21 13:18:15,240 - __main__ - INFO - Processing payslip for employee 505, date 20250625
2025-07-21 13:18:15,247 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 13:18:23,868 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-21 13:18:23,868 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-21 13:18:23,886 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_505_20250625_20250721_131823.pdf
2025-07-21 13:18:23,886 - __main__ - INFO - New payslip file detected: Payslip_508_20250625.pdf
2025-07-21 13:18:23,886 - __main__ - INFO - Processing payslip for employee 508, date 20250625
2025-07-21 13:18:23,897 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 13:18:33,988 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-21 13:18:33,988 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-21 13:18:33,988 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_508_20250625_20250721_131833.pdf
2025-07-21 13:19:37,873 - __main__ - INFO - Received signal 2, initiating graceful shutdown...
2025-07-21 13:19:50,933 - email_service - INFO - Email service initialized for smtp.gmail.com:587
2025-07-21 13:19:50,933 - __main__ - INFO - PayslipSender initialized for production deployment
2025-07-21 13:19:50,933 - __main__ - INFO - Starting PayslipSender service...
2025-07-21 13:19:52,771 - email_service - INFO - SMTP connection test successful
2025-07-21 13:19:52,771 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 13:19:52,818 - __main__ - INFO - Database connection validated
2025-07-21 13:19:52,818 - __main__ - INFO - Monitoring directory: C:\Users\<USER>\Desktop\PayslipSender\payslips
2025-07-21 13:19:52,818 - __main__ - INFO - PayslipSender service is running
2025-07-21 13:19:52,818 - __main__ - INFO - New payslip file detected: Payslip_501_20250625.pdf
2025-07-21 13:19:52,818 - __main__ - INFO - Processing payslip for employee 501, date 20250625
2025-07-21 13:19:52,818 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 13:20:03,085 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel )
2025-07-21 13:20:03,085 - __main__ - INFO - Successfully sent payslip to Jose Daniel  (<EMAIL>)
2025-07-21 13:20:03,085 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_501_20250625_20250721_132003.pdf
2025-07-21 13:20:03,085 - __main__ - INFO - New payslip file detected: Payslip_502_20250625.pdf
2025-07-21 13:20:03,085 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-21 13:20:03,092 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 13:20:13,532 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-21 13:20:13,532 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-21 13:20:13,532 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_502_20250625_20250721_132013.pdf
2025-07-21 13:20:13,532 - __main__ - INFO - New payslip file detected: Payslip_503_20250625.pdf
2025-07-21 13:20:13,532 - __main__ - INFO - Processing payslip for employee 503, date 20250625
2025-07-21 13:20:13,548 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 13:20:21,378 - email_service - INFO - Email sent <NAME_EMAIL> (Test 2 )
2025-07-21 13:20:21,378 - __main__ - INFO - Successfully sent payslip to Test 2  (<EMAIL>)
2025-07-21 13:20:21,378 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_503_20250625_20250721_132021.pdf
2025-07-21 13:20:21,378 - __main__ - INFO - New payslip file detected: Payslip_505_20250625.pdf
2025-07-21 13:20:21,378 - __main__ - INFO - Processing payslip for employee 505, date 20250625
2025-07-21 13:20:21,378 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 13:20:30,104 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-21 13:20:30,118 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-21 13:20:30,121 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_505_20250625_20250721_132030.pdf
2025-07-21 13:20:30,121 - __main__ - INFO - New payslip file detected: Payslip_508_20250625.pdf
2025-07-21 13:20:30,121 - __main__ - INFO - Processing payslip for employee 508, date 20250625
2025-07-21 13:20:30,125 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 13:20:38,893 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-21 13:20:38,893 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-21 13:20:38,893 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_508_20250625_20250721_132038.pdf
2025-07-21 13:45:16,180 - email_service - INFO - Email service initialized for smtp.gmail.com:587
2025-07-21 13:45:16,180 - __main__ - INFO - PayslipSender initialized for production deployment
2025-07-21 13:45:16,180 - __main__ - INFO - Starting PayslipSender service...
2025-07-21 13:45:18,274 - email_service - INFO - SMTP connection test successful
2025-07-21 13:45:18,274 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 13:45:18,322 - __main__ - INFO - Database connection validated
2025-07-21 13:45:18,322 - __main__ - INFO - Monitoring directory: C:\Users\<USER>\Desktop\PayslipSender\payslips
2025-07-21 13:45:18,322 - __main__ - INFO - PayslipSender service is running
2025-07-21 13:45:48,334 - __main__ - INFO - New payslip file detected: Payslip_501_20250625.pdf
2025-07-21 13:45:48,334 - __main__ - INFO - Processing payslip for employee 501, date 20250625
2025-07-21 13:45:48,334 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 13:45:58,721 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel )
2025-07-21 13:45:58,721 - __main__ - INFO - Successfully sent payslip to Jose Daniel  (<EMAIL>)
2025-07-21 13:45:58,723 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_501_20250625_20250721_134558.pdf
2025-07-21 14:16:05,121 - email_service - INFO - Email service initialized for smtp.gmail.com:587
2025-07-21 14:16:05,121 - __main__ - INFO - PayslipSender initialized for production deployment
2025-07-21 14:16:05,121 - __main__ - INFO - Starting PayslipSender service...
2025-07-21 14:16:07,185 - email_service - INFO - SMTP connection test successful
2025-07-21 14:16:07,185 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 14:16:07,251 - __main__ - INFO - Database connection validated
2025-07-21 14:16:07,251 - __main__ - INFO - Monitoring directory: C:\Users\<USER>\Desktop\PayslipSender\payslips
2025-07-21 14:16:07,251 - __main__ - INFO - PayslipSender service is running
2025-07-21 14:17:00,290 - email_service - INFO - Email service initialized for smtp.gmail.com:587
2025-07-21 14:17:00,290 - __main__ - INFO - PayslipSender initialized for production deployment
2025-07-21 14:17:00,290 - __main__ - INFO - Starting PayslipSender service...
2025-07-21 14:18:21,802 - email_service - INFO - Email service initialized for smtp.gmail.com:587
2025-07-21 14:18:21,802 - __main__ - INFO - PayslipSender initialized for production deployment
2025-07-21 14:18:21,802 - __main__ - INFO - Starting PayslipSender service...
2025-07-21 14:18:27,690 - email_service - INFO - Email service initialized for smtp.gmail.com:587
2025-07-21 14:18:27,690 - __main__ - INFO - PayslipSender initialized for production deployment
2025-07-21 14:18:27,690 - __main__ - INFO - Starting PayslipSender service...
2025-07-21 14:18:29,719 - email_service - INFO - SMTP connection test successful
2025-07-21 14:18:29,719 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 14:18:29,797 - __main__ - INFO - Database connection validated
2025-07-21 14:18:29,797 - __main__ - INFO - Monitoring directory: C:\Users\<USER>\Desktop\PayslipSender\payslips
2025-07-21 14:18:29,797 - __main__ - INFO - PayslipSender service is running
2025-07-21 14:18:59,804 - __main__ - INFO - New payslip file detected: Payslip_501_20250625.pdf
2025-07-21 14:18:59,804 - __main__ - INFO - Processing payslip for employee 501, date 20250625
2025-07-21 14:18:59,811 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 14:19:04,853 - __main__ - INFO - Received signal 2, initiating graceful shutdown...
2025-07-21 14:19:08,918 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel )
2025-07-21 14:19:08,918 - __main__ - INFO - Successfully sent payslip to Jose Daniel  (<EMAIL>)
2025-07-21 14:19:08,922 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_501_20250625_20250721_141908.pdf
2025-07-21 14:19:58,937 - __main__ - INFO - New payslip file detected: Payslip_502_20250625.pdf
2025-07-21 14:19:58,937 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-21 14:19:58,947 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 14:20:07,313 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-21 14:20:07,313 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-21 14:20:07,331 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_502_20250625_20250721_142007.pdf
2025-07-21 14:20:07,331 - __main__ - INFO - New payslip file detected: Payslip_503_20250625.pdf
2025-07-21 14:20:07,331 - __main__ - INFO - Processing payslip for employee 503, date 20250625
2025-07-21 14:20:07,344 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 14:20:16,044 - email_service - INFO - Email sent <NAME_EMAIL> (Test 2 )
2025-07-21 14:20:16,053 - __main__ - INFO - Successfully sent payslip to Test 2  (<EMAIL>)
2025-07-21 14:20:16,069 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_503_20250625_20250721_142016.pdf
2025-07-21 14:20:16,069 - __main__ - INFO - New payslip file detected: Payslip_505_20250625.pdf
2025-07-21 14:20:16,085 - __main__ - INFO - Processing payslip for employee 505, date 20250625
2025-07-21 14:20:16,085 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 14:20:24,625 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-21 14:20:24,625 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-21 14:20:24,669 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_505_20250625_20250721_142024.pdf
2025-07-21 14:20:24,669 - __main__ - INFO - New payslip file detected: Payslip_508_20250625.pdf
2025-07-21 14:20:24,669 - __main__ - INFO - Processing payslip for employee 508, date 20250625
2025-07-21 14:20:24,677 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 14:20:33,457 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-21 14:20:33,457 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-21 14:20:33,473 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_508_20250625_20250721_142033.pdf
2025-07-21 14:31:57,070 - email_service - INFO - Email service initialized for smtp.gmail.com:587
2025-07-21 14:31:57,070 - __main__ - INFO - PayslipSender initialized for production deployment
2025-07-21 14:31:57,070 - __main__ - INFO - Starting PayslipSender service...
2025-07-21 14:31:58,633 - email_service - INFO - SMTP connection test successful
2025-07-21 14:31:58,633 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 14:31:58,666 - __main__ - INFO - Database connection validated
2025-07-21 14:31:58,666 - __main__ - INFO - Monitoring directory: C:\Users\<USER>\Desktop\PayslipSender\payslips
2025-07-21 14:31:58,666 - __main__ - INFO - PayslipSender service is running
2025-07-21 14:31:58,666 - __main__ - INFO - New payslip file detected: Payslip_501_20250625.pdf
2025-07-21 14:31:58,666 - __main__ - INFO - Processing payslip for employee 501, date 20250625
2025-07-21 14:31:58,671 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 14:32:09,020 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel )
2025-07-21 14:32:09,020 - __main__ - INFO - Successfully sent payslip to Jose Daniel  (<EMAIL>)
2025-07-21 14:32:09,023 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_501_20250625_20250721_143209.pdf
2025-07-21 14:32:09,023 - __main__ - INFO - New payslip file detected: Payslip_502_20250625.pdf
2025-07-21 14:32:09,023 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-21 14:32:09,030 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 14:32:17,931 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-21 14:32:17,933 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-21 14:32:17,937 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_502_20250625_20250721_143217.pdf
2025-07-21 14:32:17,937 - __main__ - INFO - New payslip file detected: Payslip_503_20250625.pdf
2025-07-21 14:32:17,937 - __main__ - INFO - Processing payslip for employee 503, date 20250625
2025-07-21 14:32:17,937 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 14:32:26,658 - email_service - INFO - Email sent <NAME_EMAIL> (Test 2 )
2025-07-21 14:32:26,658 - __main__ - INFO - Successfully sent payslip to Test 2  (<EMAIL>)
2025-07-21 14:32:26,660 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_503_20250625_20250721_143226.pdf
2025-07-21 14:32:26,660 - __main__ - INFO - New payslip file detected: Payslip_505_20250625.pdf
2025-07-21 14:32:26,660 - __main__ - INFO - Processing payslip for employee 505, date 20250625
2025-07-21 14:32:26,665 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 14:32:35,136 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-21 14:32:35,136 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-21 14:32:35,136 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_505_20250625_20250721_143235.pdf
2025-07-21 14:32:35,136 - __main__ - INFO - New payslip file detected: Payslip_508_20250625.pdf
2025-07-21 14:32:35,136 - __main__ - INFO - Processing payslip for employee 508, date 20250625
2025-07-21 14:32:35,152 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 14:32:43,284 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-21 14:32:43,284 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-21 14:32:43,286 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_508_20250625_20250721_143243.pdf
2025-07-21 14:39:33,324 - __main__ - INFO - New payslip file detected: Payslip_504_20250625.pdf
2025-07-21 14:39:33,324 - __main__ - INFO - Processing payslip for employee 504, date 20250625
2025-07-21 14:39:33,324 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 14:39:33,630 - __main__ - INFO - New payslip file detected: Payslip_504_20250625.pdf
2025-07-21 14:39:33,630 - __main__ - INFO - Processing payslip for employee 504, date 20250625
2025-07-21 14:39:33,630 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 14:39:42,458 - email_service - INFO - Email sent <NAME_EMAIL> (Iverson)
2025-07-21 14:39:42,458 - __main__ - INFO - Successfully sent payslip to Iverson (<EMAIL>)
2025-07-21 14:39:42,458 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_504_20250625_20250721_143942.pdf
2025-07-21 14:39:43,257 - email_service - INFO - Email sent <NAME_EMAIL> (Iverson)
2025-07-21 14:39:43,257 - __main__ - INFO - Successfully sent payslip to Iverson (<EMAIL>)
2025-07-21 14:39:43,257 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_504_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_504_20250625_20250721_143943.pdf'
