2025-07-21 15:41:34,817 - __main__ - INFO - Status: 0 files in queue, 10/10 active workers
2025-07-21 15:41:36,437 - __main__ - INFO - Status: 0 files in queue, 10/10 active workers
2025-07-21 15:41:37,611 - __main__ - INFO - Status: 0 files in queue, 10/10 active workers
2025-07-21 15:41:40,794 - __main__ - INFO - Status: 0 files in queue, 10/10 active workers
2025-07-21 15:41:40,851 - __main__ - INFO - Status: 0 files in queue, 10/10 active workers
2025-07-21 15:42:34,824 - __main__ - INFO - Status: 0 files in queue, 10/10 active workers
2025-07-21 15:42:36,443 - __main__ - INFO - Status: 0 files in queue, 10/10 active workers
2025-07-21 15:42:37,616 - __main__ - INFO - Status: 0 files in queue, 10/10 active workers
2025-07-21 15:42:40,802 - __main__ - INFO - Status: 0 files in queue, 10/10 active workers
2025-07-21 15:42:40,858 - __main__ - INFO - Status: 0 files in queue, 10/10 active workers
2025-07-21 15:43:28,890 - email_service - INFO - Email service initialized for smtp.gmail.com:587
2025-07-21 15:43:28,890 - __main__ - INFO - PayslipSender initialized for production deployment
2025-07-21 15:43:28,890 - __main__ - INFO - Working directory: C:\Users\<USER>\Desktop\PayslipSender\dist
2025-07-21 15:43:28,890 - __main__ - INFO - Watch directory: C:\Users\<USER>\Desktop\PayslipSender\payslips
2025-07-21 15:43:28,890 - __main__ - INFO - Archived directory: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips
2025-07-21 15:43:28,890 - __main__ - INFO - Starting PayslipSender service...
2025-07-21 15:43:30,987 - email_service - INFO - SMTP connection test successful
2025-07-21 15:43:30,991 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 15:43:31,112 - __main__ - INFO - Database connection validated
2025-07-21 15:43:31,112 - __main__ - INFO - Monitoring directory: C:\Users\<USER>\Desktop\PayslipSender\payslips
2025-07-21 15:43:31,112 - __main__ - INFO - PayslipSender service is running
2025-07-21 15:43:31,112 - __main__ - INFO - Found 5 new payslip files to process
2025-07-21 15:43:31,112 - __main__ - INFO - New payslip file detected: Payslip_501_20250625.pdf
2025-07-21 15:43:31,112 - __main__ - INFO - Processing payslip for employee 501, date 20250625
2025-07-21 15:43:31,123 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 15:43:34,830 - __main__ - INFO - Status: 0 files in queue, 10/10 active workers
2025-07-21 15:43:36,449 - __main__ - INFO - Status: 0 files in queue, 10/10 active workers
2025-07-21 15:43:37,623 - __main__ - INFO - Status: 0 files in queue, 10/10 active workers
2025-07-21 15:43:40,146 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel )
2025-07-21 15:43:40,159 - __main__ - INFO - Successfully sent payslip to Jose Daniel  (<EMAIL>)
2025-07-21 15:43:40,177 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_501_20250625_20250721_154340.pdf
2025-07-21 15:43:40,177 - __main__ - INFO - New payslip file detected: Payslip_502_20250625.pdf
2025-07-21 15:43:40,177 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-21 15:43:40,177 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 15:43:40,810 - __main__ - INFO - Status: 0 files in queue, 10/10 active workers
2025-07-21 15:43:40,865 - __main__ - INFO - Status: 0 files in queue, 10/10 active workers
2025-07-21 15:43:49,063 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-21 15:43:49,063 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-21 15:43:49,089 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_502_20250625_20250721_154349.pdf
2025-07-21 15:43:49,089 - __main__ - INFO - New payslip file detected: Payslip_503_20250625.pdf
2025-07-21 15:43:49,089 - __main__ - INFO - Processing payslip for employee 503, date 20250625
2025-07-21 15:43:49,089 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 15:43:58,147 - email_service - INFO - Email sent <NAME_EMAIL> (Test 2 )
2025-07-21 15:43:58,147 - __main__ - INFO - Successfully sent payslip to Test 2  (<EMAIL>)
2025-07-21 15:43:58,159 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_503_20250625_20250721_154358.pdf
2025-07-21 15:43:58,159 - __main__ - INFO - New payslip file detected: Payslip_505_20250625.pdf
2025-07-21 15:43:58,159 - __main__ - INFO - Processing payslip for employee 505, date 20250625
2025-07-21 15:43:58,164 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 15:44:06,973 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-21 15:44:06,973 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-21 15:44:06,982 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_505_20250625_20250721_154406.pdf
2025-07-21 15:44:06,982 - __main__ - INFO - New payslip file detected: Payslip_506_20250625.pdf
2025-07-21 15:44:06,982 - __main__ - INFO - Processing payslip for employee 506, date 20250625
2025-07-21 15:44:06,982 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 15:44:15,967 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-21 15:44:15,968 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-21 15:44:15,980 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_506_20250625_20250721_154415.pdf
2025-07-21 15:44:34,837 - __main__ - INFO - Status: 0 files in queue, 10/10 active workers
2025-07-21 15:44:36,454 - __main__ - INFO - Status: 0 files in queue, 10/10 active workers
2025-07-21 15:44:37,631 - __main__ - INFO - Status: 0 files in queue, 10/10 active workers
2025-07-21 15:44:40,817 - __main__ - INFO - Status: 0 files in queue, 10/10 active workers
2025-07-21 15:44:40,872 - __main__ - INFO - Status: 0 files in queue, 10/10 active workers
2025-07-21 15:45:34,844 - __main__ - INFO - Status: 0 files in queue, 10/10 active workers
2025-07-21 15:45:36,459 - __main__ - INFO - Status: 0 files in queue, 10/10 active workers
2025-07-21 15:45:37,636 - __main__ - INFO - Status: 0 files in queue, 10/10 active workers
2025-07-21 15:45:40,825 - __main__ - INFO - Status: 0 files in queue, 10/10 active workers
2025-07-21 15:45:40,878 - __main__ - INFO - Status: 0 files in queue, 10/10 active workers
2025-07-21 15:46:34,851 - __main__ - INFO - Status: 0 files in queue, 10/10 active workers
2025-07-21 15:46:36,463 - __main__ - INFO - Status: 0 files in queue, 10/10 active workers
2025-07-21 15:46:37,642 - __main__ - INFO - Status: 0 files in queue, 10/10 active workers
2025-07-21 15:46:40,832 - __main__ - INFO - Status: 0 files in queue, 10/10 active workers
2025-07-21 15:46:40,884 - __main__ - INFO - Status: 0 files in queue, 10/10 active workers
2025-07-21 15:47:33,439 - email_service - INFO - Email service initialized for smtp.gmail.com:587
2025-07-21 15:47:33,439 - __main__ - INFO - PayslipSender initialized for production deployment
2025-07-21 15:47:33,439 - __main__ - INFO - Working directory: C:\Users\<USER>\Desktop\PayslipSender\dist
2025-07-21 15:47:33,439 - __main__ - INFO - Watch directory: C:\Users\<USER>\Desktop\PayslipSender\payslips
2025-07-21 15:47:33,439 - __main__ - INFO - Archived directory: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips
2025-07-21 15:47:33,439 - __main__ - INFO - Starting PayslipSender service...
2025-07-21 15:47:34,858 - __main__ - INFO - Status: 0 files in queue, 10/10 active workers
2025-07-21 15:47:35,539 - email_service - INFO - SMTP connection test successful
2025-07-21 15:47:35,539 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 15:47:35,619 - __main__ - INFO - Database connection validated
2025-07-21 15:47:35,619 - __main__ - INFO - Monitoring directory: C:\Users\<USER>\Desktop\PayslipSender\payslips
2025-07-21 15:47:35,619 - __main__ - INFO - PayslipSender service is running
2025-07-21 15:47:35,619 - __main__ - INFO - Found 5 new payslip files to process
2025-07-21 15:47:35,619 - __main__ - INFO - New payslip file detected: Payslip_501_20250625.pdf
2025-07-21 15:47:35,619 - __main__ - INFO - Processing payslip for employee 501, date 20250625
2025-07-21 15:47:35,619 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 15:47:36,468 - __main__ - INFO - Status: 0 files in queue, 10/10 active workers
2025-07-21 15:47:37,650 - __main__ - INFO - Status: 0 files in queue, 10/10 active workers
2025-07-21 15:47:40,839 - __main__ - INFO - Status: 0 files in queue, 10/10 active workers
2025-07-21 15:47:40,891 - __main__ - INFO - Status: 0 files in queue, 10/10 active workers
2025-07-21 15:47:44,291 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel )
2025-07-21 15:47:44,291 - __main__ - INFO - Successfully sent payslip to Jose Daniel  (<EMAIL>)
2025-07-21 15:47:44,312 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_501_20250625_20250721_154744.pdf
2025-07-21 15:47:44,312 - __main__ - INFO - New payslip file detected: Payslip_502_20250625.pdf
2025-07-21 15:47:44,312 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-21 15:47:44,317 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 15:47:52,890 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-21 15:47:52,890 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-21 15:47:52,906 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_502_20250625_20250721_154752.pdf
2025-07-21 15:47:52,906 - __main__ - INFO - New payslip file detected: Payslip_503_20250625.pdf
2025-07-21 15:47:52,906 - __main__ - INFO - Processing payslip for employee 503, date 20250625
2025-07-21 15:47:52,906 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 15:48:01,718 - email_service - INFO - Email sent <NAME_EMAIL> (Test 2 )
2025-07-21 15:48:01,718 - __main__ - INFO - Successfully sent payslip to Test 2  (<EMAIL>)
2025-07-21 15:48:01,718 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_503_20250625_20250721_154801.pdf
2025-07-21 15:48:01,718 - __main__ - INFO - New payslip file detected: Payslip_505_20250625.pdf
2025-07-21 15:48:01,718 - __main__ - INFO - Processing payslip for employee 505, date 20250625
2025-07-21 15:48:01,733 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 15:48:10,623 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-21 15:48:10,623 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-21 15:48:10,639 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_505_20250625_20250721_154810.pdf
2025-07-21 15:48:10,639 - __main__ - INFO - New payslip file detected: Payslip_506_20250625.pdf
2025-07-21 15:48:10,639 - __main__ - INFO - Processing payslip for employee 506, date 20250625
2025-07-21 15:48:10,639 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-21 15:48:19,742 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-21 15:48:19,758 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-21 15:48:19,762 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_506_20250625_20250721_154819.pdf
2025-07-21 15:48:34,866 - __main__ - INFO - Status: 0 files in queue, 10/10 active workers
2025-07-21 15:48:36,491 - __main__ - INFO - Status: 0 files in queue, 10/10 active workers
2025-07-21 15:48:37,657 - __main__ - INFO - Status: 0 files in queue, 10/10 active workers
2025-07-21 15:48:40,847 - __main__ - INFO - Status: 0 files in queue, 10/10 active workers
2025-07-21 15:48:40,900 - __main__ - INFO - Status: 0 files in queue, 10/10 active workers
2025-07-21 15:49:34,874 - __main__ - INFO - Status: 0 files in queue, 10/10 active workers
2025-07-21 15:49:36,497 - __main__ - INFO - Status: 0 files in queue, 10/10 active workers
2025-07-21 15:49:37,665 - __main__ - INFO - Status: 0 files in queue, 10/10 active workers
2025-07-21 15:49:40,855 - __main__ - INFO - Status: 0 files in queue, 10/10 active workers
2025-07-21 15:49:40,906 - __main__ - INFO - Status: 0 files in queue, 10/10 active workers
2025-07-21 15:49:53,796 - __main__ - INFO - Received signal 2, initiating graceful shutdown...
2025-07-21 15:49:53,796 - __main__ - INFO - Received signal 2, initiating graceful shutdown...
2025-07-21 15:49:53,796 - __main__ - INFO - PayslipWorker-10 received shutdown signal
2025-07-21 15:49:53,796 - __main__ - INFO - PayslipWorker-9 received shutdown signal
2025-07-21 15:49:53,796 - __main__ - INFO - PayslipWorker-10 stopped
2025-07-21 15:49:53,796 - __main__ - INFO - PayslipWorker-5 received shutdown signal
2025-07-21 15:49:53,799 - __main__ - INFO - PayslipWorker-9 stopped
2025-07-21 15:49:53,796 - __main__ - INFO - PayslipWorker-7 received shutdown signal
2025-07-21 15:49:53,796 - __main__ - INFO - PayslipWorker-2 received shutdown signal
2025-07-21 15:49:53,796 - __main__ - INFO - PayslipWorker-8 received shutdown signal
2025-07-21 15:49:53,796 - __main__ - INFO - PayslipWorker-4 received shutdown signal
2025-07-21 15:49:53,796 - __main__ - INFO - PayslipWorker-6 received shutdown signal
2025-07-21 15:49:53,796 - __main__ - INFO - PayslipWorker-1 received shutdown signal
2025-07-21 15:49:53,796 - __main__ - INFO - PayslipWorker-2 received shutdown signal
2025-07-21 15:49:53,799 - __main__ - INFO - PayslipWorker-6 stopped
2025-07-21 15:49:53,799 - __main__ - INFO - PayslipWorker-1 received shutdown signal
2025-07-21 15:49:53,799 - __main__ - INFO - PayslipWorker-5 received shutdown signal
2025-07-21 15:49:53,799 - __main__ - INFO - PayslipWorker-1 stopped
2025-07-21 15:49:53,799 - __main__ - INFO - PayslipWorker-5 stopped
2025-07-21 15:49:53,799 - __main__ - INFO - PayslipWorker-3 received shutdown signal
2025-07-21 15:49:53,799 - __main__ - INFO - PayslipWorker-8 received shutdown signal
2025-07-21 15:49:53,799 - __main__ - INFO - PayslipWorker-8 stopped
2025-07-21 15:49:53,799 - __main__ - INFO - PayslipWorker-3 stopped
2025-07-21 15:49:53,799 - __main__ - INFO - PayslipWorker-2 stopped
2025-07-21 15:49:53,799 - __main__ - INFO - PayslipWorker-5 stopped
2025-07-21 15:49:53,799 - __main__ - INFO - PayslipWorker-4 stopped
2025-07-21 15:49:53,799 - __main__ - INFO - PayslipWorker-7 stopped
2025-07-21 15:49:53,799 - __main__ - INFO - PayslipWorker-8 stopped
2025-07-21 15:49:53,799 - __main__ - INFO - PayslipWorker-10 received shutdown signal
2025-07-21 15:49:53,799 - __main__ - INFO - PayslipWorker-7 received shutdown signal
2025-07-21 15:49:53,799 - __main__ - INFO - PayslipWorker-2 stopped
2025-07-21 15:49:53,799 - __main__ - INFO - PayslipWorker-4 received shutdown signal
2025-07-21 15:49:53,801 - __main__ - INFO - PayslipWorker-10 stopped
2025-07-21 15:49:53,801 - __main__ - INFO - PayslipWorker-7 stopped
2025-07-21 15:49:53,799 - __main__ - INFO - PayslipWorker-9 received shutdown signal
2025-07-21 15:49:53,801 - __main__ - INFO - PayslipWorker-3 stopped
2025-07-21 15:49:53,801 - __main__ - INFO - PayslipWorker-4 stopped
2025-07-21 15:49:53,801 - __main__ - INFO - PayslipWorker-9 stopped
2025-07-21 15:49:53,832 - __main__ - INFO - Received signal 2, initiating graceful shutdown...
2025-07-21 15:49:53,834 - __main__ - INFO - PayslipWorker-2 received shutdown signal
2025-07-21 15:49:53,834 - __main__ - INFO - PayslipWorker-6 received shutdown signal
2025-07-21 15:49:53,834 - __main__ - INFO - PayslipWorker-5 received shutdown signal
2025-07-21 15:49:53,834 - __main__ - INFO - PayslipWorker-9 received shutdown signal
2025-07-21 15:49:53,834 - __main__ - INFO - PayslipWorker-3 received shutdown signal
2025-07-21 15:49:53,834 - __main__ - INFO - PayslipWorker-4 received shutdown signal
2025-07-21 15:49:53,834 - __main__ - INFO - PayslipWorker-8 received shutdown signal
2025-07-21 15:49:53,834 - __main__ - INFO - PayslipWorker-8 stopped
2025-07-21 15:49:53,834 - __main__ - INFO - PayslipWorker-10 received shutdown signal
2025-07-21 15:49:53,834 - __main__ - INFO - PayslipWorker-1 received shutdown signal
2025-07-21 15:49:53,834 - __main__ - INFO - PayslipWorker-2 stopped
2025-07-21 15:49:53,834 - __main__ - INFO - PayslipWorker-6 stopped
2025-07-21 15:49:53,834 - __main__ - INFO - PayslipWorker-5 stopped
2025-07-21 15:49:53,834 - __main__ - INFO - PayslipWorker-9 stopped
2025-07-21 15:49:53,834 - __main__ - INFO - PayslipWorker-3 stopped
2025-07-21 15:49:53,834 - __main__ - INFO - PayslipWorker-4 stopped
2025-07-21 15:49:53,834 - __main__ - INFO - PayslipWorker-7 received shutdown signal
2025-07-21 15:49:53,836 - __main__ - INFO - PayslipWorker-10 stopped
2025-07-21 15:49:53,836 - __main__ - INFO - PayslipWorker-1 stopped
2025-07-21 15:49:53,836 - __main__ - INFO - PayslipWorker-7 stopped
2025-07-21 15:49:53,842 - __main__ - INFO - Received signal 2, initiating graceful shutdown...
2025-07-21 15:49:53,842 - __main__ - INFO - PayslipWorker-2 received shutdown signal
2025-07-21 15:49:53,842 - __main__ - INFO - PayslipWorker-6 received shutdown signal
2025-07-21 15:49:53,842 - __main__ - INFO - Received signal 2, initiating graceful shutdown...
2025-07-21 15:49:53,842 - __main__ - INFO - PayslipWorker-10 received shutdown signal
2025-07-21 15:49:53,844 - __main__ - INFO - PayslipWorker-2 received shutdown signal
2025-07-21 15:49:53,842 - __main__ - INFO - PayslipWorker-1 received shutdown signal
2025-07-21 15:49:53,842 - __main__ - INFO - PayslipWorker-4 received shutdown signal
2025-07-21 15:49:53,842 - __main__ - INFO - PayslipWorker-5 received shutdown signal
2025-07-21 15:49:53,842 - __main__ - INFO - PayslipWorker-3 received shutdown signal
2025-07-21 15:49:53,842 - __main__ - INFO - PayslipWorker-9 received shutdown signal
2025-07-21 15:49:53,844 - __main__ - INFO - PayslipWorker-6 received shutdown signal
2025-07-21 15:49:53,842 - __main__ - INFO - PayslipWorker-7 received shutdown signal
2025-07-21 15:49:53,844 - __main__ - INFO - PayslipWorker-6 stopped
2025-07-21 15:49:53,842 - __main__ - INFO - PayslipWorker-8 received shutdown signal
2025-07-21 15:49:53,844 - __main__ - INFO - PayslipWorker-7 received shutdown signal
2025-07-21 15:49:53,842 - __main__ - INFO - PayslipWorker-2 stopped
2025-07-21 15:49:53,844 - __main__ - INFO - PayslipWorker-5 received shutdown signal
2025-07-21 15:49:53,842 - __main__ - INFO - PayslipWorker-6 stopped
2025-07-21 15:49:53,844 - __main__ - INFO - PayslipWorker-9 received shutdown signal
2025-07-21 15:49:53,844 - __main__ - INFO - PayslipWorker-10 stopped
2025-07-21 15:49:53,844 - __main__ - INFO - PayslipWorker-4 received shutdown signal
2025-07-21 15:49:53,844 - __main__ - INFO - PayslipWorker-1 stopped
2025-07-21 15:49:53,844 - __main__ - INFO - PayslipWorker-3 received shutdown signal
2025-07-21 15:49:53,844 - __main__ - INFO - PayslipWorker-4 stopped
2025-07-21 15:49:53,844 - __main__ - INFO - PayslipWorker-1 received shutdown signal
2025-07-21 15:49:53,844 - __main__ - INFO - PayslipWorker-3 stopped
2025-07-21 15:49:53,844 - __main__ - INFO - PayslipWorker-10 received shutdown signal
2025-07-21 15:49:53,844 - __main__ - INFO - PayslipWorker-9 stopped
2025-07-21 15:49:53,844 - __main__ - INFO - PayslipWorker-2 stopped
2025-07-21 15:49:53,844 - __main__ - INFO - PayslipWorker-7 stopped
2025-07-21 15:49:53,844 - __main__ - INFO - PayslipWorker-8 received shutdown signal
2025-07-21 15:49:53,844 - __main__ - INFO - PayslipWorker-8 stopped
2025-07-21 15:49:53,846 - __main__ - INFO - PayslipWorker-8 stopped
2025-07-21 15:49:53,844 - __main__ - INFO - PayslipWorker-5 stopped
2025-07-21 15:49:53,844 - __main__ - INFO - PayslipWorker-9 stopped
2025-07-21 15:49:53,844 - __main__ - INFO - PayslipWorker-4 stopped
2025-07-21 15:49:53,844 - __main__ - INFO - PayslipWorker-3 stopped
2025-07-21 15:49:53,844 - __main__ - INFO - PayslipWorker-1 stopped
2025-07-21 15:49:53,846 - __main__ - INFO - PayslipWorker-10 stopped
2025-07-21 15:49:53,844 - __main__ - INFO - PayslipWorker-7 stopped
2025-07-21 15:49:54,876 - __main__ - INFO - Initiating graceful shutdown...
2025-07-21 15:49:54,876 - __main__ - INFO - Waiting for current file processing to complete...
2025-07-21 15:49:54,876 - __main__ - INFO - Stopping worker threads...
2025-07-21 15:49:54,876 - __main__ - INFO - PayslipSender service stopped
2025-07-21 15:49:55,321 - __main__ - INFO - Received signal 2, initiating graceful shutdown...
2025-07-21 15:49:56,499 - __main__ - INFO - Initiating graceful shutdown...
2025-07-21 15:49:56,499 - __main__ - INFO - Waiting for current file processing to complete...
2025-07-21 15:49:56,499 - __main__ - INFO - Stopping worker threads...
2025-07-21 15:49:56,499 - __main__ - INFO - PayslipSender service stopped
2025-07-22 08:04:22,047 - email_service - INFO - Email service initialized for smtp.gmail.com:587
2025-07-22 08:04:22,050 - email_service - INFO - Email service initialized for smtp.gmail.com:587
2025-07-22 08:04:22,050 - email_service - INFO - Email service initialized for smtp.gmail.com:587
2025-07-22 08:04:22,052 - __main__ - INFO - PayslipSender initialized for production deployment
2025-07-22 08:04:22,052 - __main__ - INFO - Working directory: C:\Users\<USER>\Desktop\PayslipSender
2025-07-22 08:04:22,052 - __main__ - INFO - Watch directory: C:\Users\<USER>\Desktop\PayslipSender\payslips
2025-07-22 08:04:22,052 - __main__ - INFO - Archived directory: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips
2025-07-22 08:04:22,052 - __main__ - INFO - Starting PayslipSender service with parallel processing...
2025-07-22 08:04:22,052 - __main__ - INFO - PayslipSender initialized for production deployment
2025-07-22 08:04:22,052 - __main__ - INFO - PayslipSender initialized for production deployment
er
2025-07-22 08:04:22,054 - __main__ - INFO - Working directory: C:\Users\<USER>\Desktop\PayslipSender
2025-07-22 08:04:22,052 - __main__ - INFO - PayslipSender initialized for production deployment
2025-07-22 08:04:22,054 - __main__ - INFO - PayslipSender initialized for production deployment
2025-07-22 08:04:22,054 - __main__ - INFO - Watch directory: C:\Users\<USER>\Desktop\PayslipSender\payslips
2025-07-22 08:04:22,054 - __main__ - INFO - Watch directory: C:\Users\<USER>\Desktop\PayslipSender\pay2025-072025-07-22 08:04:22,054 - __main__ - INFO - Archived directory: C:\Users\<USER>\Desktop\PayslipSender\2025-07-22 08:04:22,054 - __main__ - INFO - Working directory: C:\Users\<USER>\Desktop\PayslipSender
2025-07-22 08:04:22,054 - __main__ - INFO - Watch directory: C:\Users\<USER>\Desktop\PayslipSender\payslips
2025-07-22 08:04:22,054 - __main__ - INFO - Archived directory: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips
2025-07-22 08:04:22,054 - __main__ - INFO - Watch directory: C:\Users\<USER>\Desktop\PayslipSender\payslips
2025-07-22 08:04:22,054 - __main__ - INFO - Watch directory: C:\Users\<USER>\Desktop\PayslipSender\paysli20252025-07-22 08:04:22,054 - __main__ - INFO - Archived directory: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips
2025-07-22 08:04:22,054 - __main__ - INFO - Archived directory: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips
2025-07-22 08:04:22,054 - __main__ - INFO - Archived directory: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips
2025-07-22 08:04:22,054 - __main__ - INFO - Starting PayslipSender service with parallel processing...
2025-07-22 08:04:22,054 - __main__ - INFO - Starting PayslipSender service with parallel processing...
2025-07-22 08:04:22,054 - __main__ - INFO - Starting PayslipSender service with parallel processing...
2025-07-22 08:04:22,150 - email_service - INFO - Email service initialized for smtp.gmail.com:587
2025-07-22 08:04:22,152 - __main__ - INFO - PayslipSender initialized for production deployment
2025-07-22 08:04:22,152 - __main__ - INFO - Working directory: C:\Users\<USER>\Desktop\PayslipSender
2025-07-22 08:04:22,152 - __main__ - INFO - Watch directory: C:\Users\<USER>\Desktop\PayslipSender\payslips
2025-07-22 08:04:22,152 - __main__ - INFO - Archived directory: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips
2025-07-22 08:04:22,152 - __main__ - INFO - Starting PayslipSender service with parallel processing...
2025-07-22 08:04:23,766 - email_service - INFO - SMTP connection test successful
2025-07-22 08:04:23,766 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 08:04:23,922 - __main__ - INFO - Database connection validated
2025-07-22 08:04:23,922 - __main__ - INFO - Monitoring directory: C:\Users\<USER>\Desktop\PayslipSender\payslips
2025-07-22 08:04:23,922 - __main__ - INFO - Using 5 worker threads for parallel processing
2025-07-22 08:04:23,922 - __main__ - INFO - PayslipSender service is running
2025-07-22 08:04:23,922 - __main__ - INFO - Starting 5 worker threads for file processing
2025-07-22 08:04:23,922 - __main__ - INFO - PayslipWorker-1 started and ready for processing
2025-07-22 08:04:23,922 - __main__ - INFO - PayslipWorker-2 started and ready for processing
2025-07-22 08:04:23,926 - __main__ - INFO - PayslipWorker-3 started and ready for processing
2025-07-22 08:04:23,926 - __main__ - INFO - PayslipWorker-4 started and ready for processing
2025-07-22 08:04:23,926 - __main__ - INFO - PayslipWorker-5 started and ready for processing
2025-07-22 08:04:23,926 - __main__ - INFO - Found 5 new payslip files to process
2025-07-22 08:04:23,926 - __main__ - INFO - Queuing file for processing: Payslip_501_20250625.pdf
2025-07-22 08:04:23,926 - __main__ - INFO - Queuing file for processing: Payslip_502_20250625.pdf
2025-07-22 08:04:23,926 - __main__ - INFO - Queuing file for processing: Payslip_503_20250625.pdf
2025-07-22 08:04:23,926 - __main__ - INFO - Queuing file for processing: Payslip_505_20250625.pdf
2025-07-22 08:04:23,926 - __main__ - INFO - Queuing file for processing: Payslip_506_20250625.pdf
2025-07-22 08:04:23,926 - __main__ - INFO - PayslipWorker-1 processing: Payslip_501_20250625.pdf
2025-07-22 08:04:23,926 - __main__ - INFO - PayslipWorker-4 processing: Payslip_502_20250625.pdf
2025-07-22 08:04:23,926 - __main__ - INFO - PayslipWorker-2 processing: Payslip_503_20250625.pdf
2025-07-22 08:04:23,926 - __main__ - INFO - PayslipWorker-5 processing: Payslip_505_20250625.pdf
2025-07-22 08:04:23,926 - __main__ - INFO - PayslipWorker-3 processing: Payslip_506_20250625.pdf
2025-07-22 08:04:23,926 - __main__ - INFO - Processing payslip for employee 506, date 20250625
2025-07-22 08:04:23,926 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-22 08:04:23,926 - __main__ - INFO - Processing payslip for employee 503, date 20250625
2025-07-22 08:04:23,926 - __main__ - INFO - Processing payslip for employee 505, date 20250625
2025-07-22 08:04:23,926 - __main__ - INFO - Processing payslip for employee 501, date 20250625
2025-07-22 08:04:23,926 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 08:04:23,926 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 08:04:23,936 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 08:04:23,938 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 08:04:23,938 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 08:04:23,956 - email_service - INFO - SMTP connection test successful
2025-07-22 08:04:23,966 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 08:04:23,986 - email_service - INFO - SMTP connection test successful
2025-07-22 08:04:23,990 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 08:04:23,990 - email_service - INFO - SMTP connection test successful
2025-07-22 08:04:23,990 - __main__ - INFO - Database connection validated
2025-07-22 08:04:23,990 - __main__ - INFO - Monitoring directory: C:\Users\<USER>\Desktop\PayslipSender\payslips
2025-07-22 08:04:23,990 - __main__ - INFO - Using 5 worker threads for parallel processing
2025-07-22 08:04:23,990 - __main__ - INFO - PayslipSender service is running
2025-07-22 08:04:23,990 - __main__ - INFO - Starting 5 worker threads for file processing
2025-07-22 08:04:23,990 - __main__ - INFO - PayslipWorker-1 started and ready for processing
2025-07-22 08:04:23,990 - __main__ - INFO - PayslipWorker-2 started and ready for processing
2025-07-22 08:04:23,996 - __main__ - INFO - PayslipWorker-3 started and ready for processing
2025-07-22 08:04:23,996 - __main__ - INFO - PayslipWorker-4 started and ready for processing
2025-07-22 08:04:23,996 - __main__ - INFO - PayslipWorker-5 started and ready for processing
2025-07-22 08:04:23,996 - __main__ - INFO - Found 5 new payslip files to process
2025-07-22 08:04:23,996 - __main__ - INFO - Queuing file for processing: Payslip_501_20250625.pdf
2025-07-22 08:04:23,996 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 08:04:23,996 - __main__ - INFO - Queuing file for processing: Payslip_502_20250625.pdf
2025-07-22 08:04:23,996 - __main__ - INFO - PayslipWorker-1 processing: Payslip_501_20250625.pdf
2025-07-22 08:04:23,996 - __main__ - INFO - Queuing file for processing: Payslip_503_20250625.pdf
2025-07-22 08:04:23,996 - __main__ - INFO - PayslipWorker-2 processing: Payslip_502_20250625.pdf
2025-07-22 08:04:23,996 - __main__ - INFO - Processing payslip for employee 501, date 20250625
2025-07-22 08:04:23,996 - __main__ - INFO - Queuing file for processing: Payslip_505_20250625.pdf
2025-07-22 08:04:23,996 - __main__ - INFO - PayslipWorker-3 processing: Payslip_503_20250625.pdf
2025-07-22 08:04:23,996 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-22 08:04:24,002 - __main__ - INFO - Queuing file for processing: Payslip_506_20250625.pdf
2025-07-22 08:04:24,002 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 08:04:24,002 - __main__ - INFO - PayslipWorker-4 processing: Payslip_505_20250625.pdf
2025-07-22 08:04:24,002 - __main__ - INFO - Processing payslip for employee 503, date 20250625
2025-07-22 08:04:24,006 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 08:04:24,006 - __main__ - INFO - PayslipWorker-5 processing: Payslip_506_20250625.pdf
2025-07-22 08:04:24,006 - __main__ - INFO - Processing payslip for employee 505, date 20250625
2025-07-22 08:04:24,006 - email_service - INFO - SMTP connection test successful
2025-07-22 08:04:24,006 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 08:04:24,006 - __main__ - INFO - Processing payslip for employee 506, date 20250625
2025-07-22 08:04:24,017 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 08:04:24,017 - __main__ - INFO - Database connection validated
2025-07-22 08:04:24,017 - __main__ - INFO - Monitoring directory: C:\Users\<USER>\Desktop\PayslipSender\payslips
2025-07-22 08:04:24,017 - __main__ - INFO - Using 5 worker threads for parallel processing
2025-07-22 08:04:24,017 - __main__ - INFO - PayslipSender service is running
2025-07-22 08:04:24,017 - __main__ - INFO - Starting 5 worker threads for file processing
2025-07-22 08:04:24,018 - __main__ - INFO - PayslipWorker-1 started and ready for processing
2025-07-22 08:04:24,018 - __main__ - INFO - PayslipWorker-2 started and ready for processing
2025-07-22 08:04:24,018 - __main__ - INFO - PayslipWorker-3 started and ready for processing
2025-07-22 08:04:24,018 - __main__ - INFO - PayslipWorker-4 started and ready for processing
2025-07-22 08:04:24,018 - __main__ - INFO - PayslipWorker-5 started and ready for processing
2025-07-22 08:04:24,018 - __main__ - INFO - Found 5 new payslip files to process
2025-07-22 08:04:24,020 - __main__ - INFO - Queuing file for processing: Payslip_501_20250625.pdf
2025-07-22 08:04:24,020 - __main__ - INFO - Queuing file for processing: Payslip_502_20250625.pdf
2025-07-22 08:04:24,006 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 08:04:24,020 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 08:04:24,020 - __main__ - INFO - PayslipWorker-1 processing: Payslip_501_20250625.pdf
2025-07-22 08:04:24,020 - __main__ - INFO - Queuing file for processing: Payslip_503_20250625.pdf
2025-07-22 08:04:24,020 - __main__ - INFO - Processing payslip for employee 501, date 20250625
2025-07-22 08:04:24,020 - __main__ - INFO - PayslipWorker-2 processing: Payslip_502_20250625.pdf
2025-07-22 08:04:24,020 - __main__ - INFO - Queuing file for processing: Payslip_505_20250625.pdf
2025-07-22 08:04:24,020 - __main__ - INFO - PayslipWorker-3 processing: Payslip_503_20250625.pdf
2025-07-22 08:04:24,024 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 08:04:24,024 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-22 08:04:24,026 - __main__ - INFO - Database connection validated
2025-07-22 08:04:24,028 - __main__ - INFO - Monitoring directory: C:\Users\<USER>\Desktop\PayslipSender\payslips
2025-07-22 08:04:24,028 - __main__ - INFO - Using 5 worker threads for parallel processing
2025-07-22 08:04:24,028 - __main__ - INFO - PayslipSender service is running
2025-07-22 08:04:24,028 - __main__ - INFO - Starting 5 worker threads for file processing
2025-07-22 08:04:24,028 - __main__ - INFO - PayslipWorker-1 started and ready for processing
2025-07-22 08:04:24,028 - __main__ - INFO - PayslipWorker-2 started and ready for processing
2025-07-22 08:04:24,030 - __main__ - INFO - PayslipWorker-3 started and ready for processing
2025-07-22 08:04:24,030 - __main__ - INFO - PayslipWorker-4 started and ready for processing
2025-07-22 08:04:24,030 - __main__ - INFO - PayslipWorker-5 started and ready for processing
2025-07-22 08:04:24,030 - __main__ - INFO - Found 5 new payslip files to process
2025-07-22 08:04:24,030 - __main__ - INFO - Queuing file for processing: Payslip_501_20250625.pdf
2025-07-22 08:04:24,032 - __main__ - INFO - Queuing file for processing: Payslip_502_20250625.pdf
2025-07-22 08:04:24,026 - __main__ - INFO - PayslipWorker-4 processing: Payslip_505_20250625.pdf
2025-07-22 08:04:24,032 - __main__ - INFO - PayslipWorker-1 processing: Payslip_501_20250625.pdf
2025-07-22 08:04:24,026 - __main__ - INFO - Processing payslip for employee 503, date 20250625
2025-07-22 08:04:24,032 - __main__ - INFO - Queuing file for processing: Payslip_503_20250625.pdf
2025-07-22 08:04:24,032 - __main__ - INFO - PayslipWorker-2 processing: Payslip_502_20250625.pdf
2025-07-22 08:04:24,032 - __main__ - INFO - Processing payslip for employee 501, date 20250625
2025-07-22 08:04:24,030 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 08:04:24,032 - __main__ - INFO - Processing payslip for employee 505, date 20250625
2025-07-22 08:04:24,032 - __main__ - INFO - Queuing file for processing: Payslip_505_20250625.pdf
2025-07-22 08:04:24,032 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-22 08:04:24,032 - __main__ - INFO - PayslipWorker-5 processing: Payslip_506_20250625.pdf
2025-07-22 08:04:24,039 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 08:04:24,037 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 08:04:24,039 - __main__ - INFO - Queuing file for processing: Payslip_506_20250625.pdf
2025-07-22 08:04:24,043 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 08:04:24,043 - __main__ - INFO - Processing payslip for employee 506, date 20250625
2025-07-22 08:04:24,039 - __main__ - INFO - PayslipWorker-3 processing: Payslip_503_20250625.pdf
2025-07-22 08:04:24,043 - __main__ - INFO - Processing payslip for employee 503, date 20250625
2025-07-22 08:04:24,047 - __main__ - INFO - Database connection validated
2025-07-22 08:04:24,047 - __main__ - INFO - Monitoring directory: C:\Users\<USER>\Desktop\PayslipSender\payslips
2025-07-22 08:04:24,047 - __main__ - INFO - Using 5 worker threads for parallel processing
2025-07-22 08:04:24,049 - __main__ - INFO - PayslipSender service is running
2025-07-22 08:04:24,049 - __main__ - INFO - Starting 5 worker threads for file processing
2025-07-22 08:04:24,049 - __main__ - INFO - PayslipWorker-1 started and ready for processing
2025-07-22 08:04:24,049 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 08:04:24,049 - __main__ - INFO - PayslipWorker-2 started and ready for processing
2025-07-22 08:04:24,049 - __main__ - INFO - PayslipWorker-3 started and ready for processing
2025-07-22 08:04:24,050 - __main__ - INFO - PayslipWorker-4 started and ready for processing
2025-07-22 08:04:24,043 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 08:04:24,043 - __main__ - INFO - PayslipWorker-5 processing: Payslip_506_20250625.pdf
2025-07-22 08:04:24,039 - __main__ - INFO - PayslipWorker-4 processing: Payslip_505_20250625.pdf
2025-07-22 08:04:24,050 - __main__ - INFO - PayslipWorker-5 started and ready for processing
2025-07-22 08:04:24,050 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 08:04:24,050 - __main__ - INFO - Processing payslip for employee 506, date 20250625
2025-07-22 08:04:24,050 - __main__ - INFO - Found 5 new payslip files to process
2025-07-22 08:04:24,052 - __main__ - INFO - Queuing file for processing: Payslip_501_20250625.pdf
2025-07-22 08:04:24,052 - __main__ - INFO - Queuing file for processing: Payslip_502_20250625.pdf
2025-07-22 08:04:24,052 - __main__ - INFO - PayslipWorker-1 processing: Payslip_501_20250625.pdf
2025-07-22 08:04:24,052 - __main__ - INFO - Queuing file for processing: Payslip_503_20250625.pdf
2025-07-22 08:04:24,052 - __main__ - INFO - PayslipWorker-2 processing: Payslip_502_20250625.pdf
2025-07-22 08:04:24,052 - __main__ - INFO - Processing payslip for employee 501, date 20250625
2025-07-22 08:04:24,050 - __main__ - INFO - Processing payslip for employee 505, date 20250625
2025-07-22 08:04:24,052 - __main__ - INFO - Queuing file for processing: Payslip_505_20250625.pdf
2025-07-22 08:04:24,052 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-22 08:04:24,061 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 08:04:24,067 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 08:04:24,052 - __main__ - INFO - PayslipWorker-3 processing: Payslip_503_20250625.pdf
2025-07-22 08:04:24,078 - __main__ - INFO - Processing payslip for employee 503, date 20250625
2025-07-22 08:04:24,063 - __main__ - INFO - Queuing file for processing: Payslip_506_20250625.pdf
2025-07-22 08:04:24,075 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 08:04:24,075 - __main__ - INFO - PayslipWorker-4 processing: Payslip_505_20250625.pdf
2025-07-22 08:04:24,063 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 08:04:24,084 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 08:04:24,088 - __main__ - INFO - PayslipWorker-5 processing: Payslip_506_20250625.pdf
2025-07-22 08:04:24,088 - __main__ - INFO - Processing payslip for employee 505, date 20250625
2025-07-22 08:04:24,088 - __main__ - INFO - Processing payslip for employee 506, date 20250625
2025-07-22 08:04:24,104 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 08:04:24,111 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 08:04:24,206 - email_service - INFO - SMTP connection test successful
2025-07-22 08:04:24,213 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 08:04:24,241 - __main__ - INFO - Database connection validated
2025-07-22 08:04:24,241 - email_service - INFO - SMTP connection test successful
2025-07-22 08:04:24,241 - __main__ - INFO - Monitoring directory: C:\Users\<USER>\Desktop\PayslipSender\payslips
2025-07-22 08:04:24,241 - __main__ - INFO - Using 5 worker threads for parallel processing
2025-07-22 08:04:24,241 - __main__ - INFO - PayslipSender service is running
2025-07-22 08:04:24,241 - __main__ - INFO - Starting 5 worker threads for file processing
2025-07-22 08:04:24,241 - __main__ - INFO - PayslipWorker-1 started and ready for processing
2025-07-22 08:04:24,241 - __main__ - INFO - PayslipWorker-2 started and ready for processing
2025-07-22 08:04:24,241 - __main__ - INFO - PayslipWorker-3 started and ready for processing
2025-07-22 08:04:24,243 - __main__ - INFO - PayslipWorker-4 started and ready for processing
2025-07-22 08:04:24,243 - __main__ - INFO - PayslipWorker-5 started and ready for processing
2025-07-22 08:04:24,243 - __main__ - INFO - Found 5 new payslip files to process
2025-07-22 08:04:24,243 - __main__ - INFO - Queuing file for processing: Payslip_501_20250625.pdf
2025-07-22 08:04:24,243 - __main__ - INFO - Queuing file for processing: Payslip_502_20250625.pdf
2025-07-22 08:04:24,243 - __main__ - INFO - PayslipWorker-1 processing: Payslip_501_20250625.pdf
2025-07-22 08:04:24,243 - __main__ - INFO - Queuing file for processing: Payslip_503_20250625.pdf
2025-07-22 08:04:24,243 - __main__ - INFO - PayslipWorker-2 processing: Payslip_502_20250625.pdf
2025-07-22 08:04:24,243 - __main__ - INFO - Processing payslip for employee 501, date 20250625
2025-07-22 08:04:24,245 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 08:04:24,243 - __main__ - INFO - Queuing file for processing: Payslip_505_20250625.pdf
2025-07-22 08:04:24,243 - __main__ - INFO - PayslipWorker-3 processing: Payslip_503_20250625.pdf
2025-07-22 08:04:24,243 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-22 08:04:24,246 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 08:04:24,246 - __main__ - INFO - Queuing file for processing: Payslip_506_20250625.pdf
2025-07-22 08:04:24,246 - __main__ - INFO - PayslipWorker-4 processing: Payslip_505_20250625.pdf
2025-07-22 08:04:24,246 - __main__ - INFO - Processing payslip for employee 503, date 20250625
2025-07-22 08:04:24,246 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 08:04:24,246 - __main__ - INFO - PayslipWorker-5 processing: Payslip_506_20250625.pdf
2025-07-22 08:04:24,246 - __main__ - INFO - Processing payslip for employee 505, date 20250625
2025-07-22 08:04:24,246 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 08:04:24,246 - __main__ - INFO - Processing payslip for employee 506, date 20250625
2025-07-22 08:04:24,256 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 08:04:24,260 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 08:04:24,272 - __main__ - INFO - Database connection validated
2025-07-22 08:04:24,272 - __main__ - INFO - Monitoring directory: C:\Users\<USER>\Desktop\PayslipSender\payslips
2025-07-22 08:04:24,272 - __main__ - INFO - Using 5 worker threads for parallel processing
2025-07-22 08:04:24,272 - __main__ - INFO - PayslipSender service is running
2025-07-22 08:04:24,274 - __main__ - INFO - Starting 5 worker threads for file processing
2025-07-22 08:04:24,274 - __main__ - INFO - PayslipWorker-1 started and ready for processing
2025-07-22 08:04:24,274 - __main__ - INFO - PayslipWorker-2 started and ready for processing
2025-07-22 08:04:24,274 - __main__ - INFO - PayslipWorker-3 started and ready for processing
2025-07-22 08:04:24,274 - __main__ - INFO - PayslipWorker-4 started and ready for processing
2025-07-22 08:04:24,274 - __main__ - INFO - PayslipWorker-5 started and ready for processing
2025-07-22 08:04:24,276 - __main__ - INFO - Found 5 new payslip files to process
2025-07-22 08:04:24,276 - __main__ - INFO - Queuing file for processing: Payslip_501_20250625.pdf
2025-07-22 08:04:24,276 - __main__ - INFO - Queuing file for processing: Payslip_502_20250625.pdf
2025-07-22 08:04:24,276 - __main__ - INFO - PayslipWorker-1 processing: Payslip_501_20250625.pdf
2025-07-22 08:04:24,276 - __main__ - INFO - Queuing file for processing: Payslip_503_20250625.pdf
2025-07-22 08:04:24,276 - __main__ - INFO - PayslipWorker-2 processing: Payslip_502_20250625.pdf
2025-07-22 08:04:24,276 - __main__ - INFO - Processing payslip for employee 501, date 20250625
2025-07-22 08:04:24,276 - __main__ - INFO - Queuing file for processing: Payslip_505_20250625.pdf
2025-07-22 08:04:24,276 - __main__ - INFO - PayslipWorker-3 processing: Payslip_503_20250625.pdf
2025-07-22 08:04:24,276 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-22 08:04:24,279 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 08:04:24,279 - __main__ - INFO - Queuing file for processing: Payslip_506_20250625.pdf
2025-07-22 08:04:24,279 - __main__ - INFO - PayslipWorker-4 processing: Payslip_505_20250625.pdf
2025-07-22 08:04:24,279 - __main__ - INFO - Processing payslip for employee 503, date 20250625
2025-07-22 08:04:24,285 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 08:04:24,286 - __main__ - INFO - PayslipWorker-5 processing: Payslip_506_20250625.pdf
2025-07-22 08:04:24,286 - __main__ - INFO - Processing payslip for employee 505, date 20250625
2025-07-22 08:04:24,288 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 08:04:24,288 - __main__ - INFO - Processing payslip for employee 506, date 20250625
2025-07-22 08:04:24,293 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 08:04:24,299 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 08:04:24,336 - email_service - INFO - SMTP connection test successful
2025-07-22 08:04:24,336 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 08:04:24,356 - __main__ - INFO - Database connection validated
2025-07-22 08:04:24,356 - __main__ - INFO - Monitoring directory: C:\Users\<USER>\Desktop\PayslipSender\payslips
2025-07-22 08:04:24,356 - __main__ - INFO - Using 5 worker threads for parallel processing
2025-07-22 08:04:24,356 - __main__ - INFO - PayslipSender service is running
2025-07-22 08:04:24,356 - __main__ - INFO - Starting 5 worker threads for file processing
2025-07-22 08:04:24,356 - __main__ - INFO - PayslipWorker-1 started and ready for processing
2025-07-22 08:04:24,356 - __main__ - INFO - PayslipWorker-2 started and ready for processing
2025-07-22 08:04:24,356 - __main__ - INFO - PayslipWorker-3 started and ready for processing
2025-07-22 08:04:24,356 - __main__ - INFO - PayslipWorker-4 started and ready for processing
2025-07-22 08:04:24,356 - __main__ - INFO - PayslipWorker-5 started and ready for processing
2025-07-22 08:04:24,356 - __main__ - INFO - Found 5 new payslip files to process
2025-07-22 08:04:24,356 - __main__ - INFO - Queuing file for processing: Payslip_501_20250625.pdf
2025-07-22 08:04:24,356 - __main__ - INFO - Queuing file for processing: Payslip_502_20250625.pdf
2025-07-22 08:04:24,356 - __main__ - INFO - PayslipWorker-1 processing: Payslip_501_20250625.pdf
2025-07-22 08:04:24,356 - __main__ - INFO - Queuing file for processing: Payslip_503_20250625.pdf
2025-07-22 08:04:24,356 - __main__ - INFO - PayslipWorker-2 processing: Payslip_502_20250625.pdf
2025-07-22 08:04:24,356 - __main__ - INFO - Processing payslip for employee 501, date 20250625
2025-07-22 08:04:24,356 - __main__ - INFO - Queuing file for processing: Payslip_505_20250625.pdf
2025-07-22 08:04:24,356 - __main__ - INFO - PayslipWorker-3 processing: Payslip_503_20250625.pdf
2025-07-22 08:04:24,356 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-22 08:04:24,367 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 08:04:24,367 - __main__ - INFO - Queuing file for processing: Payslip_506_20250625.pdf
2025-07-22 08:04:24,367 - __main__ - INFO - PayslipWorker-4 processing: Payslip_505_20250625.pdf
2025-07-22 08:04:24,367 - __main__ - INFO - Processing payslip for employee 503, date 20250625
2025-07-22 08:04:24,371 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 08:04:24,371 - __main__ - INFO - PayslipWorker-5 processing: Payslip_506_20250625.pdf
2025-07-22 08:04:24,371 - __main__ - INFO - Processing payslip for employee 505, date 20250625
2025-07-22 08:04:24,371 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 08:04:24,371 - __main__ - INFO - Processing payslip for employee 506, date 20250625
2025-07-22 08:04:24,377 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 08:04:24,381 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 08:04:34,067 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-22 08:04:34,067 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-22 08:04:34,067 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_505_20250625_20250722_080434.pdf
2025-07-22 08:04:34,067 - __main__ - INFO - PayslipWorker-4 completed: Payslip_505_20250625.pdf
2025-07-22 08:04:35,030 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-22 08:04:35,030 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-22 08:04:35,032 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_502_20250625_20250722_080435.pdf
2025-07-22 08:04:35,032 - __main__ - INFO - PayslipWorker-2 completed: Payslip_502_20250625.pdf
2025-07-22 08:04:35,369 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-22 08:04:35,369 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-22 08:04:35,369 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_502_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_502_20250625_20250722_080435.pdf'
2025-07-22 08:04:35,369 - __main__ - INFO - PayslipWorker-2 completed: Payslip_502_20250625.pdf
2025-07-22 08:04:35,616 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel )
2025-07-22 08:04:35,616 - __main__ - INFO - Successfully sent payslip to Jose Daniel  (<EMAIL>)
2025-07-22 08:04:35,616 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_501_20250625_20250722_080435.pdf
2025-07-22 08:04:35,616 - __main__ - INFO - PayslipWorker-1 completed: Payslip_501_20250625.pdf
2025-07-22 08:04:36,203 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-22 08:04:36,203 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-22 08:04:36,203 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_505_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_505_20250625_20250722_080436.pdf'
2025-07-22 08:04:36,203 - __main__ - INFO - PayslipWorker-4 completed: Payslip_505_20250625.pdf
2025-07-22 08:04:36,916 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-22 08:04:36,916 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-22 08:04:36,916 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_502_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_502_20250625_20250722_080436.pdf'
2025-07-22 08:04:36,916 - __main__ - INFO - PayslipWorker-2 completed: Payslip_502_20250625.pdf
2025-07-22 08:04:40,196 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel )
2025-07-22 08:04:40,196 - __main__ - INFO - Successfully sent payslip to Jose Daniel  (<EMAIL>)
2025-07-22 08:04:40,196 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_501_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_501_20250625_20250722_080440.pdf'
2025-07-22 08:04:40,196 - __main__ - INFO - PayslipWorker-1 completed: Payslip_501_20250625.pdf
2025-07-22 08:04:41,556 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-22 08:04:41,556 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-22 08:04:41,556 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_505_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_505_20250625_20250722_080441.pdf'
2025-07-22 08:04:41,556 - __main__ - INFO - PayslipWorker-4 completed: Payslip_505_20250625.pdf
2025-07-22 08:04:41,888 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-22 08:04:41,888 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-22 08:04:41,888 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_505_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_505_20250625_20250722_080441.pdf'
2025-07-22 08:04:41,888 - __main__ - INFO - PayslipWorker-4 completed: Payslip_505_20250625.pdf
2025-07-22 08:04:41,999 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel )
2025-07-22 08:04:41,999 - __main__ - INFO - Successfully sent payslip to Jose Daniel  (<EMAIL>)
2025-07-22 08:04:41,999 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_501_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_501_20250625_20250722_080441.pdf'
2025-07-22 08:04:41,999 - __main__ - INFO - PayslipWorker-1 completed: Payslip_501_20250625.pdf
2025-07-22 08:04:42,238 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-22 08:04:42,238 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-22 08:04:42,238 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_505_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_505_20250625_20250722_080442.pdf'
2025-07-22 08:04:42,238 - __main__ - INFO - PayslipWorker-4 completed: Payslip_505_20250625.pdf
2025-07-22 08:04:42,348 - email_service - INFO - Email sent <NAME_EMAIL> (Test 2 )
2025-07-22 08:04:42,348 - __main__ - INFO - Successfully sent payslip to Test 2  (<EMAIL>)
2025-07-22 08:04:42,364 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_503_20250625_20250722_080442.pdf
2025-07-22 08:04:42,364 - __main__ - INFO - PayslipWorker-3 completed: Payslip_503_20250625.pdf
2025-07-22 08:04:42,364 - email_service - INFO - Email sent <NAME_EMAIL> (Test 2 )
2025-07-22 08:04:42,364 - __main__ - INFO - Successfully sent payslip to Test 2  (<EMAIL>)
2025-07-22 08:04:42,364 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_503_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_503_20250625_20250722_080442.pdf'
2025-07-22 08:04:42,364 - __main__ - INFO - PayslipWorker-2 completed: Payslip_503_20250625.pdf
2025-07-22 08:04:42,395 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-22 08:04:42,395 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-22 08:04:42,395 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_502_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_502_20250625_20250722_080442.pdf'
2025-07-22 08:04:42,395 - __main__ - INFO - PayslipWorker-4 completed: Payslip_502_20250625.pdf
2025-07-22 08:04:42,411 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-22 08:04:42,411 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-22 08:04:42,411 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_506_20250625_20250722_080442.pdf
2025-07-22 08:04:42,411 - __main__ - INFO - PayslipWorker-5 completed: Payslip_506_20250625.pdf
2025-07-22 08:04:42,427 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel )
2025-07-22 08:04:42,427 - __main__ - INFO - Successfully sent payslip to Jose Daniel  (<EMAIL>)
2025-07-22 08:04:42,427 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_501_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_501_20250625_20250722_080442.pdf'
2025-07-22 08:04:42,427 - __main__ - INFO - PayslipWorker-1 completed: Payslip_501_20250625.pdf
2025-07-22 08:04:42,506 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel )
2025-07-22 08:04:42,506 - __main__ - INFO - Successfully sent payslip to Jose Daniel  (<EMAIL>)
2025-07-22 08:04:42,506 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_501_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_501_20250625_20250722_080442.pdf'
2025-07-22 08:04:42,506 - __main__ - INFO - PayslipWorker-1 completed: Payslip_501_20250625.pdf
2025-07-22 08:04:42,570 - email_service - INFO - Email sent <NAME_EMAIL> (Test 2 )
2025-07-22 08:04:42,570 - __main__ - INFO - Successfully sent payslip to Test 2  (<EMAIL>)
2025-07-22 08:04:42,585 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_503_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_503_20250625_20250722_080442.pdf'
2025-07-22 08:04:42,585 - __main__ - INFO - PayslipWorker-3 completed: Payslip_503_20250625.pdf
2025-07-22 08:04:42,744 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-22 08:04:42,744 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-22 08:04:42,744 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_506_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_506_20250625_20250722_080442.pdf'
2025-07-22 08:04:42,744 - __main__ - INFO - PayslipWorker-5 completed: Payslip_506_20250625.pdf
2025-07-22 08:04:42,918 - email_service - INFO - Email sent <NAME_EMAIL> (Test 2 )
2025-07-22 08:04:42,918 - __main__ - INFO - Successfully sent payslip to Test 2  (<EMAIL>)
2025-07-22 08:04:42,918 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_503_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_503_20250625_20250722_080442.pdf'
2025-07-22 08:04:42,918 - __main__ - INFO - PayslipWorker-3 completed: Payslip_503_20250625.pdf
2025-07-22 08:04:42,981 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-22 08:04:42,981 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-22 08:04:42,981 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_506_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_506_20250625_20250722_080442.pdf'
2025-07-22 08:04:42,981 - __main__ - INFO - PayslipWorker-5 completed: Payslip_506_20250625.pdf
2025-07-22 08:04:42,986 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-22 08:04:42,997 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-22 08:04:42,997 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_505_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_505_20250625_20250722_080442.pdf'
2025-07-22 08:04:42,997 - __main__ - INFO - PayslipWorker-4 completed: Payslip_505_20250625.pdf
2025-07-22 08:04:43,077 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel )
2025-07-22 08:04:43,077 - __main__ - INFO - Successfully sent payslip to Jose Daniel  (<EMAIL>)
2025-07-22 08:04:43,077 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_501_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_501_20250625_20250722_080443.pdf'
2025-07-22 08:04:43,077 - __main__ - INFO - PayslipWorker-1 completed: Payslip_501_20250625.pdf
2025-07-22 08:04:43,171 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-22 08:04:43,171 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-22 08:04:43,171 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_506_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_506_20250625_20250722_080443.pdf'
2025-07-22 08:04:43,171 - __main__ - INFO - PayslipWorker-5 completed: Payslip_506_20250625.pdf
2025-07-22 08:04:43,171 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-22 08:04:43,171 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-22 08:04:43,171 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_506_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_506_20250625_20250722_080443.pdf'
2025-07-22 08:04:43,171 - __main__ - INFO - PayslipWorker-5 completed: Payslip_506_20250625.pdf
2025-07-22 08:04:43,501 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-22 08:04:43,501 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-22 08:04:43,501 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_505_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_505_20250625_20250722_080443.pdf'
2025-07-22 08:04:43,501 - __main__ - INFO - PayslipWorker-5 completed: Payslip_505_20250625.pdf
2025-07-22 08:04:43,817 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-22 08:04:43,817 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-22 08:04:43,817 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_502_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_502_20250625_20250722_080443.pdf'
2025-07-22 08:04:43,817 - __main__ - INFO - PayslipWorker-2 completed: Payslip_502_20250625.pdf
2025-07-22 08:04:43,865 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-22 08:04:43,880 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-22 08:04:43,880 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_502_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_502_20250625_20250722_080443.pdf'
2025-07-22 08:04:43,880 - __main__ - INFO - PayslipWorker-2 completed: Payslip_502_20250625.pdf
2025-07-22 08:04:43,947 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-22 08:04:43,947 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-22 08:04:43,947 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_506_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_506_20250625_20250722_080443.pdf'
2025-07-22 08:04:43,947 - __main__ - INFO - PayslipWorker-3 completed: Payslip_506_20250625.pdf
2025-07-22 08:04:44,070 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-22 08:04:44,070 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-22 08:04:44,070 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_506_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_506_20250625_20250722_080444.pdf'
2025-07-22 08:04:44,070 - __main__ - INFO - PayslipWorker-5 completed: Payslip_506_20250625.pdf
2025-07-22 08:04:44,212 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-22 08:04:44,212 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-22 08:04:44,212 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_502_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_502_20250625_20250722_080444.pdf'
2025-07-22 08:04:44,212 - __main__ - INFO - PayslipWorker-2 completed: Payslip_502_20250625.pdf
2025-07-22 08:04:44,576 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel )
2025-07-22 08:04:44,576 - __main__ - INFO - Successfully sent payslip to Jose Daniel  (<EMAIL>)
2025-07-22 08:04:44,576 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_501_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_501_20250625_20250722_080444.pdf'
2025-07-22 08:04:44,576 - __main__ - INFO - PayslipWorker-1 completed: Payslip_501_20250625.pdf
2025-07-22 08:04:45,288 - email_service - INFO - Email sent <NAME_EMAIL> (Test 2 )
2025-07-22 08:04:45,288 - __main__ - INFO - Successfully sent payslip to Test 2  (<EMAIL>)
2025-07-22 08:04:45,288 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_503_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_503_20250625_20250722_080445.pdf'
2025-07-22 08:04:45,288 - __main__ - INFO - PayslipWorker-3 completed: Payslip_503_20250625.pdf
2025-07-22 08:04:46,092 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-22 08:04:46,092 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-22 08:04:46,092 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_505_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_505_20250625_20250722_080446.pdf'
2025-07-22 08:04:46,092 - __main__ - INFO - PayslipWorker-4 completed: Payslip_505_20250625.pdf
2025-07-22 08:04:47,412 - email_service - INFO - Email sent <NAME_EMAIL> (Test 2 )
2025-07-22 08:04:47,412 - __main__ - INFO - Successfully sent payslip to Test 2  (<EMAIL>)
2025-07-22 08:04:47,412 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_503_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_503_20250625_20250722_080447.pdf'
2025-07-22 08:04:47,412 - __main__ - INFO - PayslipWorker-3 completed: Payslip_503_20250625.pdf
2025-07-22 08:04:48,690 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel )
2025-07-22 08:04:48,690 - __main__ - INFO - Successfully sent payslip to Jose Daniel  (<EMAIL>)
2025-07-22 08:04:48,690 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_501_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_501_20250625_20250722_080448.pdf'
2025-07-22 08:04:48,690 - __main__ - INFO - PayslipWorker-1 completed: Payslip_501_20250625.pdf
2025-07-22 08:04:49,619 - email_service - INFO - Email sent <NAME_EMAIL> (Test 2 )
2025-07-22 08:04:49,619 - __main__ - INFO - Successfully sent payslip to Test 2  (<EMAIL>)
2025-07-22 08:04:49,619 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_503_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_503_20250625_20250722_080449.pdf'
2025-07-22 08:04:49,619 - __main__ - INFO - PayslipWorker-3 completed: Payslip_503_20250625.pdf
2025-07-22 08:04:50,232 - email_service - INFO - Email sent <NAME_EMAIL> (Test 2 )
2025-07-22 08:04:50,232 - __main__ - INFO - Successfully sent payslip to Test 2  (<EMAIL>)
2025-07-22 08:04:50,232 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_503_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_503_20250625_20250722_080450.pdf'
2025-07-22 08:04:50,232 - __main__ - INFO - PayslipWorker-3 completed: Payslip_503_20250625.pdf
2025-07-22 08:04:51,412 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-22 08:04:51,412 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-22 08:04:51,412 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_506_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_506_20250625_20250722_080451.pdf'
2025-07-22 08:04:51,412 - __main__ - INFO - PayslipWorker-5 completed: Payslip_506_20250625.pdf
2025-07-22 08:04:51,831 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-22 08:04:51,831 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-22 08:04:51,831 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_502_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_502_20250625_20250722_080451.pdf'
2025-07-22 08:04:51,831 - __main__ - INFO - PayslipWorker-2 completed: Payslip_502_20250625.pdf
2025-07-22 08:05:24,012 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:05:24,045 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:05:24,101 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:05:24,103 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:05:24,123 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:05:24,293 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:05:24,330 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:05:24,561 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:06:24,066 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:06:24,104 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:06:24,163 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:06:24,180 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:06:24,350 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:06:24,388 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:06:24,621 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:07:24,129 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:07:24,166 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:07:24,234 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:07:24,242 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:07:24,413 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:07:24,448 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:07:24,680 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:08:24,198 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:08:24,232 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:08:24,311 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:08:24,311 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:08:24,311 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:08:24,477 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:08:24,515 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:08:24,749 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:09:24,248 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:09:24,283 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:09:24,384 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:09:24,385 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:09:24,385 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:09:24,532 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:09:24,570 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:09:24,808 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:10:24,300 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:10:24,337 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:10:24,466 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:10:24,470 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:10:24,471 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:10:24,584 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:10:24,627 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:10:24,849 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:11:24,326 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:11:24,360 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:11:24,493 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:11:24,501 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:11:24,611 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:11:24,655 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:11:24,877 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:12:24,378 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:12:24,409 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:12:24,531 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:12:24,542 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:12:24,649 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:12:24,699 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:12:24,915 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:13:24,419 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:13:24,448 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:13:24,569 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:13:24,581 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:13:24,682 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:13:24,734 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:13:24,950 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:14:24,458 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:14:24,489 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:14:24,609 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:14:24,622 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:14:24,720 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:14:24,772 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:14:24,987 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:15:24,511 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:15:24,536 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:15:24,661 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:15:24,674 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:15:24,674 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:15:24,770 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:15:24,820 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:15:25,037 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:16:24,569 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:16:24,595 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:16:24,721 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:16:24,737 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:16:24,837 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:16:24,880 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:16:25,101 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:17:24,634 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:17:24,662 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:17:24,788 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:17:24,809 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:17:24,896 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:17:24,938 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:17:25,162 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:18:24,699 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:18:24,732 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:18:24,854 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:18:24,881 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:18:24,965 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:18:25,004 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:18:25,238 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:19:24,765 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:19:24,799 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:19:24,911 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:19:24,953 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:19:25,033 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:19:25,080 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:19:25,307 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:20:24,816 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:20:24,849 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:20:24,963 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:20:25,009 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:20:25,084 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:20:25,133 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:20:25,360 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:21:24,880 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:21:24,916 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:21:25,026 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:21:25,073 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:21:25,145 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:21:25,197 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:21:25,423 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:22:24,940 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:22:24,983 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:22:25,092 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:22:25,138 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:22:25,138 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:22:25,207 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:22:25,253 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:22:25,486 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:23:25,002 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:23:25,048 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:23:25,153 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:23:25,198 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:23:25,265 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:23:25,315 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:23:25,541 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:24:25,058 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:24:25,102 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:24:25,211 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:24:25,260 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:24:25,321 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:24:25,369 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:24:25,600 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:25:25,120 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:25:25,145 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:25:25,257 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:25:25,314 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:25:25,314 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:25:25,362 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:25:25,412 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:25:25,640 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:26:25,189 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:26:25,213 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:26:25,322 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:26:25,386 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:26:25,431 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:26:25,481 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:26:25,703 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:27:25,249 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:27:25,267 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:27:25,379 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:27:25,450 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:27:25,492 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:27:25,544 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:27:25,764 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:28:25,313 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:28:25,329 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:28:25,448 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:28:25,519 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:28:25,554 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:28:25,611 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:28:25,829 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:29:25,370 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:29:25,389 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:29:25,510 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:29:25,583 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:29:25,610 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:29:25,669 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:29:25,885 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:30:25,437 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:30:25,454 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:30:25,576 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:30:25,649 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:30:25,672 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:30:25,729 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:30:25,933 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:31:25,494 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:31:25,509 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:31:25,640 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:31:25,714 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:31:25,714 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:31:25,729 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:31:25,787 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:31:25,982 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:32:25,572 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:32:25,580 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:32:25,705 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:32:25,789 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:32:25,804 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:32:25,855 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:32:26,051 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:33:25,640 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:33:25,650 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:33:25,772 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:33:25,868 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:33:25,869 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:33:25,918 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:33:26,119 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:34:25,701 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:34:25,705 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:34:25,837 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:34:25,941 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:34:25,941 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:34:25,941 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:34:25,988 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:34:26,187 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:35:25,771 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:35:25,896 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:35:26,019 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:35:26,019 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:35:26,049 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:35:26,256 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:36:25,855 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:36:25,855 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:36:25,963 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:36:26,107 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:36:26,107 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:36:26,118 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:36:26,321 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:37:25,923 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:37:25,923 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:37:26,021 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:37:26,174 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:37:26,174 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:37:26,174 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:37:26,176 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:37:26,377 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:38:25,984 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:38:26,079 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:38:26,230 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:38:26,246 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:38:26,246 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:38:26,433 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:39:26,052 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:39:26,143 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:39:26,301 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:39:26,314 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:39:26,320 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:39:26,320 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:39:26,497 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:40:26,125 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:40:26,125 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:40:26,207 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:40:26,367 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:40:26,380 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:40:26,384 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:40:26,561 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:41:26,187 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:41:26,188 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:41:26,271 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:41:26,423 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:41:26,436 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:41:26,441 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:41:26,441 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:41:26,621 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:42:26,258 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:42:26,339 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:42:26,483 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:42:26,499 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:42:26,503 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:42:26,503 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:42:26,683 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:43:26,325 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:43:26,325 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:43:26,406 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:43:26,546 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:43:26,562 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:43:26,566 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:43:26,752 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:44:26,396 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:44:26,396 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:44:26,457 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:44:26,611 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:44:26,627 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:44:26,633 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:44:26,634 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:44:26,816 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:45:26,460 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:45:26,460 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:45:26,517 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:45:26,678 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:45:26,694 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:45:26,699 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:45:26,884 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:46:26,527 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:46:26,577 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:46:26,740 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:46:26,755 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:46:26,760 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:46:26,945 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:47:26,594 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:47:26,643 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:47:26,804 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:47:26,821 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:47:26,823 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:47:27,005 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:48:26,661 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:48:26,662 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:48:26,701 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:48:26,866 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:48:26,885 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:48:26,889 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:48:26,889 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:48:27,070 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:49:26,728 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:49:26,761 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:49:26,933 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:49:26,947 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:49:26,960 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:49:27,138 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:50:26,791 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:50:26,794 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:50:26,820 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:50:26,992 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:50:27,008 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:50:27,028 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:50:27,202 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:51:26,859 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:51:26,865 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:51:26,887 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:51:27,065 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:51:27,080 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:51:27,106 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:51:27,268 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:52:26,919 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:52:26,927 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:52:26,948 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:52:27,121 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:52:27,134 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:52:27,168 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:52:27,327 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:53:26,989 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:53:26,996 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:53:27,022 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:53:27,192 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:53:27,212 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:53:27,250 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:53:27,394 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:54:27,047 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:54:27,056 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:54:27,083 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:54:27,256 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:54:27,276 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:54:27,321 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:54:27,455 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:55:27,121 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:55:27,121 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:55:27,154 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:55:27,323 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:55:27,341 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:55:27,391 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:55:27,522 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:56:27,198 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:56:27,198 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:56:27,222 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:56:27,394 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:56:27,410 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:56:27,464 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:56:27,464 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:56:27,588 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:57:27,274 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:57:27,277 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:57:27,295 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:57:27,466 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:57:27,486 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:57:27,541 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:57:27,660 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:58:27,343 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:58:27,352 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:58:27,365 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:58:27,537 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:58:27,556 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:58:27,617 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:58:27,729 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:59:27,411 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:59:27,416 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:59:27,430 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:59:27,611 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:59:27,623 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:59:27,692 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:59:27,692 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 08:59:27,796 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:00:27,477 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:00:27,486 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:00:27,503 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:00:27,680 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:00:27,686 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:00:27,765 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:00:27,867 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:01:27,535 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:01:27,546 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:01:27,563 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:01:27,737 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:01:27,743 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:01:27,827 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:01:27,926 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:02:27,604 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:02:27,616 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:02:27,628 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:02:27,804 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:02:27,813 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:02:27,900 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:02:27,900 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:02:27,986 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:03:27,662 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:03:27,670 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:03:27,684 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:03:27,861 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:03:27,866 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:03:27,956 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:03:27,956 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:03:28,043 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:04:27,706 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:04:27,713 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:04:27,727 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:04:27,901 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:04:27,904 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:04:28,004 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:04:28,004 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:04:28,088 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:05:27,771 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:05:27,776 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:05:27,790 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:05:27,966 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:05:28,077 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:05:28,154 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:06:27,839 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:06:27,841 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:06:27,858 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:06:28,038 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:06:28,155 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:06:28,226 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:07:27,890 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:07:27,891 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:07:27,907 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:07:28,096 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:07:28,223 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:07:28,285 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:08:27,942 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:08:27,951 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:08:28,159 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:08:28,159 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:08:28,279 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:08:28,279 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:08:28,335 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:09:27,997 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:09:28,000 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:09:28,213 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:09:28,213 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:09:28,332 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:09:28,386 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:10:00,965 - __main__ - INFO - Received signal 2, initiating graceful shutdown...
2025-07-22 09:10:00,965 - __main__ - INFO - PayslipWorker-4 received shutdown signal
2025-07-22 09:10:00,965 - __main__ - INFO - PayslipWorker-2 received shutdown signal
2025-07-22 09:10:00,965 - __main__ - INFO - PayslipWorker-3 received shutdown signal
2025-07-22 09:10:00,965 - __main__ - INFO - PayslipWorker-1 received shutdown signal
2025-07-22 09:10:00,965 - __main__ - INFO - PayslipWorker-5 received shutdown signal
2025-07-22 09:10:00,965 - __main__ - INFO - PayslipWorker-5 stopped
2025-07-22 09:10:00,965 - __main__ - INFO - PayslipWorker-2 stopped
2025-07-22 09:10:00,965 - __main__ - INFO - PayslipWorker-3 stopped
2025-07-22 09:10:00,965 - __main__ - INFO - PayslipWorker-1 stopped
2025-07-22 09:10:00,965 - __main__ - INFO - PayslipWorker-4 stopped
2025-07-22 09:10:01,417 - __main__ - INFO - Initiating graceful shutdown...
2025-07-22 09:10:01,417 - __main__ - INFO - Waiting for current file processing to complete...
2025-07-22 09:10:01,417 - __main__ - INFO - Stopping worker threads...
2025-07-22 09:10:01,417 - __main__ - INFO - PayslipSender service stopped
2025-07-22 09:10:10,347 - email_service - INFO - Email service initialized for smtp.gmail.com:587
2025-07-22 09:10:10,347 - __main__ - INFO - PayslipSender initialized for production deployment
2025-07-22 09:10:10,347 - __main__ - INFO - Working directory: C:\Users\<USER>\Desktop\PayslipSender
2025-07-22 09:10:10,347 - __main__ - INFO - Watch directory: C:\Users\<USER>\Desktop\PayslipSender\payslips
2025-07-22 09:10:10,347 - __main__ - INFO - Archived directory: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips
2025-07-22 09:10:10,347 - __main__ - INFO - Starting PayslipSender service with parallel processing...
2025-07-22 09:10:11,845 - email_service - INFO - SMTP connection test successful
2025-07-22 09:10:11,845 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:11,893 - __main__ - INFO - Database connection validated
2025-07-22 09:10:11,893 - __main__ - INFO - Monitoring directory: C:\Users\<USER>\Desktop\PayslipSender\payslips
2025-07-22 09:10:11,893 - __main__ - INFO - Using 5 worker threads for parallel processing
2025-07-22 09:10:11,893 - __main__ - INFO - PayslipSender service is running
2025-07-22 09:10:11,893 - __main__ - INFO - Starting 5 worker threads for file processing
2025-07-22 09:10:11,907 - __main__ - INFO - PayslipWorker-1 started and ready for processing
2025-07-22 09:10:11,907 - __main__ - INFO - PayslipWorker-2 started and ready for processing
2025-07-22 09:10:11,907 - __main__ - INFO - PayslipWorker-3 started and ready for processing
2025-07-22 09:10:11,907 - __main__ - INFO - PayslipWorker-4 started and ready for processing
2025-07-22 09:10:11,907 - __main__ - INFO - PayslipWorker-5 started and ready for processing
2025-07-22 09:10:15,260 - __main__ - INFO - Found 5 new payslip files to process
2025-07-22 09:10:15,260 - __main__ - INFO - Queuing file for processing: Payslip_501_20250625.pdf
2025-07-22 09:10:15,260 - __main__ - INFO - Found 5 new payslip files to process
2025-07-22 09:10:15,260 - __main__ - INFO - Queuing file for processing: Payslip_502_20250625.pdf
2025-07-22 09:10:15,260 - __main__ - INFO - Queuing file for processing: Payslip_501_20250625.pdf
2025-07-22 09:10:15,260 - __main__ - INFO - Queuing file for processing: Payslip_503_20250625.pdf
2025-07-22 09:10:15,260 - __main__ - INFO - Queuing file for processing: Payslip_502_20250625.pdf
2025-07-22 09:10:15,260 - __main__ - INFO - Queuing file for processing: Payslip_503_20250625.pdf
2025-07-22 09:10:15,260 - __main__ - INFO - Queuing file for processing: Payslip_505_20250625.pdf
2025-07-22 09:10:15,260 - __main__ - INFO - Queuing file for processing: Payslip_506_20250625.pdf
2025-07-22 09:10:15,260 - __main__ - INFO - PayslipWorker-3 processing: Payslip_501_20250625.pdf
2025-07-22 09:10:15,260 - __main__ - INFO - PayslipWorker-2 processing: Payslip_502_20250625.pdf
2025-07-22 09:10:15,260 - __main__ - INFO - Processing payslip for employee 502, date 20250625

2025-07-22 09:10:15,260 - __main__ - INFO - Processing payslip for employee 501, date 20250625
2025-07-22 09:10:15,260 - __main__ - INFO - PayslipWorker-4 processing: Payslip_503_20250625.pdf
2025-07-22 09:10:15,260 - __main__ - INFO - Processing payslip for employee 501, date 20250625
2025-07-22 09:10:15,260 - __main__ - INFO - PayslipWorker-3 processing: Payslip_503_20250625.pdf
2025-07-22 09:10:15,260 - __main__ - INFO - PayslipWorker-5 processing: Payslip_502_20250625.pdf
2025-07-22 09:10:15,266 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-22 09:10:15,260 - __main__ - INFO - Queuing file for processing: Payslip_505_20250625.pdf
2025-07-22 09:10:15,262 - __main__ - INFO - PayslipWorker-1 processing: Payslip_506_20250625.pdf
2025-07-22 09:10:15,266 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:15,268 - __main__ - INFO - Processing payslip for employee 506, date 20250625
2025-07-22 09:10:15,266 - __main__ - INFO - Processing payslip for employee 503, date 20250625
2025-07-22 09:10:15,266 - __main__ - INFO - Processing payslip for employee 503, date 20250625
2025-07-22 09:10:15,268 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:15,268 - __main__ - INFO - Queuing file for processing: Payslip_506_20250625.pdf
2025-07-22 09:10:15,268 - __main__ - INFO - PayslipWorker-1 processing: Payslip_505_20250625.pdf
2025-07-22 09:10:15,275 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:15,275 - __main__ - INFO - PayslipWorker-5 processing: Payslip_506_20250625.pdf
2025-07-22 09:10:15,275 - __main__ - INFO - Processing payslip for employee 505, date 20250625
2025-07-22 09:10:15,260 - __main__ - INFO - PayslipWorker-4 processing: Payslip_505_20250625.pdf
2025-07-22 09:10:15,268 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:15,266 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:15,275 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:15,280 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:15,275 - __main__ - INFO - Processing payslip for employee 506, date 20250625
2025-07-22 09:10:15,282 - __main__ - INFO - Processing payslip for employee 505, date 20250625
2025-07-22 09:10:15,290 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:15,282 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:15,290 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:15,377 - __main__ - INFO - Found 5 new payslip files to process
2025-07-22 09:10:15,377 - __main__ - INFO - Queuing file for processing: Payslip_501_20250625.pdf
2025-07-22 09:10:15,377 - __main__ - INFO - Queuing file for processing: Payslip_501_20250625.pdf
2025-07-22 09:10:15,377 - __main__ - INFO - Queuing file for processing: Payslip_502_20250625.pdf
2025-07-22 09:10:15,377 - __main__ - INFO - Queuing file for processing: Payslip_502_20250625.pdf
2025-07-22 09:10:15,377 - __main__ - INFO - PayslipWorker-2 processing: Payslip_501_20250625.pdf
2025-07-22 09:10:15,377 - __main__ - INFO - Processing payslip for employee 501, date 20250625
2025-07-22 09:10:15,377 - __main__ - INFO - PayslipWorker-2 processing: Payslip_501_20250625.pdf
2025-07-22 09:10:15,377 - __main__ - INFO - Queuing file for processing: Payslip_503_20250625.pdf
2025-07-22 09:10:15,377 - __main__ - INFO - PayslipWorker-5 processing: Payslip_502_20250625.pdf
2025-07-22 09:10:15,377 - __main__ - INFO - Processing payslip for employee 501, date 20250625
2025-07-22 09:10:15,377 - __main__ - INFO - PayslipWorker-4 processing: Payslip_502_20250625.pdf
2025-07-22 09:10:15,377 - __main__ - INFO - Queuing file for processing: Payslip_503_20250625.pdf
2025-07-22 09:10:15,379 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:15,377 - __main__ - INFO - Queuing file for processing: Payslip_505_20250625.pdf
2025-07-22 09:10:15,379 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-22 09:10:15,377 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-22 09:10:15,379 - __main__ - INFO - Queuing file for processing: Payslip_505_20250625.pdf
2025-07-22 09:10:15,379 - __main__ - INFO - PayslipWorker-1 processing: Payslip_503_20250625.pdf
2025-07-22 09:10:15,377 - __main__ - INFO - PayslipWorker-3 processing: Payslip_503_20250625.pdf
2025-07-22 09:10:15,379 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:15,383 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:15,381 - __main__ - INFO - Queuing file for processing: Payslip_506_20250625.pdf
2025-07-22 09:10:15,383 - __main__ - INFO - PayslipWorker-5 processing: Payslip_505_20250625.pdf
2025-07-22 09:10:15,383 - __main__ - INFO - Processing payslip for employee 503, date 20250625
2025-07-22 09:10:15,383 - __main__ - INFO - Processing payslip for employee 503, date 20250625
2025-07-22 09:10:15,383 - __main__ - INFO - PayslipWorker-3 processing: Payslip_506_20250625.pdf
2025-07-22 09:10:15,383 - __main__ - INFO - PayslipWorker-1 processing: Payslip_506_20250625.pdf
2025-07-22 09:10:15,383 - __main__ - INFO - Processing payslip for employee 505, date 20250625
2025-07-22 09:10:15,383 - __main__ - INFO - Processing payslip for employee 505, date 20250625
2025-07-22 09:10:15,383 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:15,383 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:15,383 - __main__ - INFO - Processing payslip for employee 506, date 20250625
2025-07-22 09:10:15,383 - __main__ - INFO - Processing payslip for employee 506, date 20250625
2025-07-22 09:10:15,393 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:15,393 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:15,393 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:15,393 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:15,912 - __main__ - INFO - Found 5 new payslip files to process
2025-07-22 09:10:15,912 - __main__ - INFO - Queuing file for processing: Payslip_501_20250625.pdf
2025-07-22 09:10:15,912 - __main__ - INFO - PayslipWorker-2 processing: Payslip_501_20250625.pdf
2025-07-22 09:10:15,912 - __main__ - INFO - Queuing file for processing: Payslip_502_20250625.pdf
2025-07-22 09:10:15,912 - __main__ - INFO - Processing payslip for employee 501, date 20250625
2025-07-22 09:10:15,916 - __main__ - INFO - PayslipWorker-1 processing: Payslip_502_20250625.pdf
2025-07-22 09:10:15,916 - __main__ - INFO - Queuing file for processing: Payslip_503_20250625.pdf
2025-07-22 09:10:15,916 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:15,916 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-22 09:10:15,916 - __main__ - INFO - PayslipWorker-3 processing: Payslip_503_20250625.pdf
2025-07-22 09:10:15,916 - __main__ - INFO - Queuing file for processing: Payslip_505_20250625.pdf
2025-07-22 09:10:15,932 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:15,932 - __main__ - INFO - Processing payslip for employee 503, date 20250625
2025-07-22 09:10:15,932 - __main__ - INFO - PayslipWorker-4 processing: Payslip_505_20250625.pdf
2025-07-22 09:10:15,941 - __main__ - INFO - Processing payslip for employee 505, date 20250625
2025-07-22 09:10:15,941 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:15,932 - __main__ - INFO - Queuing file for processing: Payslip_506_20250625.pdf
2025-07-22 09:10:15,950 - __main__ - INFO - PayslipWorker-5 processing: Payslip_506_20250625.pdf
2025-07-22 09:10:15,950 - __main__ - INFO - Processing payslip for employee 506, date 20250625
2025-07-22 09:10:15,950 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:15,950 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:16,044 - __main__ - INFO - Found 5 new payslip files to process
2025-07-22 09:10:16,044 - __main__ - INFO - Queuing file for processing: Payslip_501_20250625.pdf
2025-07-22 09:10:16,045 - __main__ - INFO - Queuing file for processing: Payslip_502_20250625.pdf
2025-07-22 09:10:16,045 - __main__ - INFO - Queuing file for processing: Payslip_503_20250625.pdf
2025-07-22 09:10:16,045 - __main__ - INFO - Queuing file for processing: Payslip_505_20250625.pdf
2025-07-22 09:10:16,045 - __main__ - INFO - Queuing file for processing: Payslip_503_20250625.pdf
2025-07-22 09:10:16,045 - __main__ - INFO - Queuing file for processing: Payslip_505_20250625.pdf
2025-07-22 09:10:16,045 - __main__ - INFO - Found 5 new payslip files to process
2025-07-22 09:10:16,045 - __main__ - INFO - Queuing file for processing: Payslip_501_20250625.pdf
2025-07-22 09:10:16,045 - __main__ - INFO - Queuing file for processing: Payslip_502_20250625.pdf
2025-07-22 09:10:16,045 - __main__ - INFO - PayslipWorker-2 processing: Payslip_501_20250625.pdf
2025-07-22 09:10:16,045 - __main__ - INFO - PayslipWorker-4 processing: Payslip_501_20250625.pdf
2025-07-22 09:10:16,045 - __main__ - INFO - PayslipWorker-4 processing: Payslip_501_20250625.pdf
2025-07-22 09:10:16,045 - __main__ - INFO - Processing payslip for employee 501, date 20250625
2025-07-22 09:10:16,045 - __main__ - INFO - Queuing file for processing: Payslip_503_20250625.pdf
2025-07-22 09:10:16,045 - __main__ - INFO - PayslipWorker-1 processing: Payslip_502_20250625.pdf
2025-07-22 09:10:16,045 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-22 09:10:16,045 - __main__ - INFO - PayslipWorker-3 processing: Payslip_503_20250625.pdf
2025-07-22 09:10:16,045 - __main__ - INFO - PayslipWorker-1 processing: Payslip_505_20250625.pdf
2025-07-22 09:10:16,045 - __main__ - INFO - PayslipWorker-4 processing: Payslip_506_20250625.pdf
2025-07-22 09:10:16,045 - __main__ - INFO - PayslipWorker-1 processing: Payslip_502_20250625.pdf
2025-07-22 09:10:16,045 - __main__ - INFO - Queuing file for processing: Payslip_505_20250625.pdf
2025-07-22 09:10:16,045 - __main__ - INFO - PayslipWorker-2 processing: Payslip_503_20250625.pdf
2025-07-22 09:10:16,045 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-22 09:10:16,045 - __main__ - INFO - Processing payslip for employee 503, date 20250625
2025-07-22 09:10:16,045 - __main__ - INFO - Processing payslip for employee 501, date 20250625
2025-07-22 09:10:16,045 - __main__ - INFO - Processing payslip for employee 503, date 20250625
2025-07-22 09:10:16,045 - __main__ - INFO - Queuing file for processing: Payslip_506_20250625.pdf
2025-07-22 09:10:16,045 - __main__ - INFO - PayslipWorker-5 processing: Payslip_503_20250625.pdf
2025-07-22 09:10:16,045 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:16,045 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:16,045 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-22 09:10:16,045 - __main__ - INFO - Queuing file for processing: Payslip_506_20250625.pdf
2025-07-22 09:10:16,045 - __main__ - INFO - PayslipWorker-2 processing: Payslip_505_20250625.pdf
2025-07-22 09:10:16,045 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:16,045 - __main__ - INFO - Processing payslip for employee 503, date 20250625
2025-07-22 09:10:16,045 - __main__ - INFO - Processing payslip for employee 505, date 20250625
2025-07-22 09:10:16,045 - __main__ - INFO - PayslipWorker-3 processing: Payslip_505_20250625.pdf
2025-07-22 09:10:16,045 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:16,045 - __main__ - INFO - PayslipWorker-5 processing: Payslip_506_20250625.pdf
2025-07-22 09:10:16,058 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:16,045 - __main__ - INFO - PayslipWorker-3 processing: Payslip_506_20250625.pdf
2025-07-22 09:10:16,059 - __main__ - INFO - Processing payslip for employee 505, date 20250625
2025-07-22 09:10:16,045 - __main__ - INFO - Processing payslip for employee 505, date 20250625
2025-07-22 09:10:16,045 - __main__ - INFO - Processing payslip for employee 506, date 20250625
2025-07-22 09:10:16,059 - __main__ - INFO - Processing payslip for employee 506, date 20250625
2025-07-22 09:10:16,059 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:16,059 - __main__ - INFO - Processing payslip for employee 506, date 20250625
2025-07-22 09:10:16,045 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:16,045 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:16,058 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:16,059 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:16,059 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:16,059 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:16,059 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:16,059 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:24,892 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-22 09:10:24,892 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-22 09:10:24,900 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_502_20250625_20250722_091024.pdf
2025-07-22 09:10:24,901 - __main__ - INFO - PayslipWorker-5 completed: Payslip_502_20250625.pdf
2025-07-22 09:10:28,064 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 4 processed files
2025-07-22 09:10:28,077 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 4 processed files
2025-07-22 09:10:28,113 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 4 processed files
2025-07-22 09:10:28,313 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 4 processed files
2025-07-22 09:10:28,315 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 4 processed files
2025-07-22 09:10:28,397 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 4 processed files
2025-07-22 09:10:28,456 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 4 processed files
2025-07-22 09:10:29,168 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel )
2025-07-22 09:10:29,168 - __main__ - INFO - Successfully sent payslip to Jose Daniel  (<EMAIL>)
2025-07-22 09:10:29,168 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_501_20250625_20250722_091029.pdf
2025-07-22 09:10:29,168 - __main__ - INFO - PayslipWorker-2 completed: Payslip_501_20250625.pdf
2025-07-22 09:10:30,027 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-22 09:10:30,027 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-22 09:10:30,027 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_502_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_502_20250625_20250722_091030.pdf'
2025-07-22 09:10:30,027 - __main__ - INFO - PayslipWorker-1 completed: Payslip_502_20250625.pdf
2025-07-22 09:10:30,299 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-22 09:10:30,299 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-22 09:10:30,299 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_502_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_502_20250625_20250722_091030.pdf'
2025-07-22 09:10:30,299 - __main__ - INFO - PayslipWorker-2 completed: Payslip_502_20250625.pdf
2025-07-22 09:10:30,709 - email_service - INFO - Email sent <NAME_EMAIL> (Test 2 )
2025-07-22 09:10:30,709 - __main__ - INFO - Successfully sent payslip to Test 2  (<EMAIL>)
2025-07-22 09:10:30,709 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_503_20250625_20250722_091030.pdf
2025-07-22 09:10:30,709 - __main__ - INFO - PayslipWorker-3 completed: Payslip_503_20250625.pdf
2025-07-22 09:10:31,399 - email_service - INFO - Email sent <NAME_EMAIL> (Test 2 )
2025-07-22 09:10:31,399 - __main__ - INFO - Successfully sent payslip to Test 2  (<EMAIL>)
2025-07-22 09:10:31,399 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_503_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_503_20250625_20250722_091031.pdf'
2025-07-22 09:10:31,399 - __main__ - INFO - PayslipWorker-3 completed: Payslip_503_20250625.pdf
2025-07-22 09:10:31,584 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-22 09:10:31,584 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-22 09:10:31,584 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_506_20250625_20250722_091031.pdf
2025-07-22 09:10:31,584 - __main__ - INFO - PayslipWorker-1 completed: Payslip_506_20250625.pdf
2025-07-22 09:10:32,795 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-22 09:10:32,795 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-22 09:10:32,795 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_506_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_506_20250625_20250722_091032.pdf'
2025-07-22 09:10:32,795 - __main__ - INFO - PayslipWorker-5 completed: Payslip_506_20250625.pdf
2025-07-22 09:10:33,130 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel )
2025-07-22 09:10:33,130 - __main__ - INFO - Successfully sent payslip to Jose Daniel  (<EMAIL>)
2025-07-22 09:10:33,130 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_501_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_501_20250625_20250722_091033.pdf'
2025-07-22 09:10:33,130 - __main__ - INFO - PayslipWorker-2 completed: Payslip_501_20250625.pdf
2025-07-22 09:10:33,198 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-22 09:10:33,198 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-22 09:10:33,198 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_505_20250625_20250722_091033.pdf
2025-07-22 09:10:33,198 - __main__ - INFO - PayslipWorker-4 completed: Payslip_505_20250625.pdf
2025-07-22 09:10:33,299 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel )
2025-07-22 09:10:33,299 - __main__ - INFO - Successfully sent payslip to Jose Daniel  (<EMAIL>)
2025-07-22 09:10:33,299 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_501_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_501_20250625_20250722_091033.pdf'
2025-07-22 09:10:33,299 - __main__ - INFO - PayslipWorker-2 completed: Payslip_501_20250625.pdf
2025-07-22 09:10:33,577 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-22 09:10:33,577 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-22 09:10:33,577 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_506_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_506_20250625_20250722_091033.pdf'
2025-07-22 09:10:33,577 - __main__ - INFO - PayslipWorker-5 completed: Payslip_506_20250625.pdf
2025-07-22 09:10:33,852 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-22 09:10:33,852 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-22 09:10:33,852 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_502_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_502_20250625_20250722_091033.pdf'
2025-07-22 09:10:33,852 - __main__ - INFO - PayslipWorker-1 completed: Payslip_502_20250625.pdf
2025-07-22 09:10:33,994 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-22 09:10:33,994 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-22 09:10:33,994 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_502_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_502_20250625_20250722_091033.pdf'
2025-07-22 09:10:33,994 - __main__ - INFO - PayslipWorker-5 completed: Payslip_502_20250625.pdf
2025-07-22 09:10:34,010 - email_service - INFO - Email sent <NAME_EMAIL> (Test 2 )
2025-07-22 09:10:34,010 - __main__ - INFO - Successfully sent payslip to Test 2  (<EMAIL>)
2025-07-22 09:10:34,010 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_503_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_503_20250625_20250722_091034.pdf'
2025-07-22 09:10:34,010 - __main__ - INFO - PayslipWorker-1 completed: Payslip_503_20250625.pdf
2025-07-22 09:10:34,043 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-22 09:10:34,043 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-22 09:10:34,043 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_506_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_506_20250625_20250722_091034.pdf'
2025-07-22 09:10:34,043 - __main__ - INFO - PayslipWorker-5 completed: Payslip_506_20250625.pdf
2025-07-22 09:10:34,092 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel )
2025-07-22 09:10:34,092 - __main__ - INFO - Successfully sent payslip to Jose Daniel  (<EMAIL>)
2025-07-22 09:10:34,092 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_501_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_501_20250625_20250722_091034.pdf'
2025-07-22 09:10:34,092 - __main__ - INFO - PayslipWorker-2 completed: Payslip_501_20250625.pdf
2025-07-22 09:10:34,092 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-22 09:10:34,092 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-22 09:10:34,092 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_506_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_506_20250625_20250722_091034.pdf'
2025-07-22 09:10:34,092 - __main__ - INFO - PayslipWorker-4 completed: Payslip_506_20250625.pdf
2025-07-22 09:10:34,170 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-22 09:10:34,170 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-22 09:10:34,170 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_506_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_506_20250625_20250722_091034.pdf'
2025-07-22 09:10:34,170 - __main__ - INFO - PayslipWorker-3 completed: Payslip_506_20250625.pdf
2025-07-22 09:10:34,186 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-22 09:10:34,191 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-22 09:10:34,192 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_502_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_502_20250625_20250722_091034.pdf'
2025-07-22 09:10:34,193 - __main__ - INFO - PayslipWorker-5 completed: Payslip_502_20250625.pdf
2025-07-22 09:10:34,220 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-22 09:10:34,220 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-22 09:10:34,220 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_506_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_506_20250625_20250722_091034.pdf'
2025-07-22 09:10:34,220 - __main__ - INFO - PayslipWorker-3 completed: Payslip_506_20250625.pdf
2025-07-22 09:10:34,381 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-22 09:10:34,381 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-22 09:10:34,381 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_505_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_505_20250625_20250722_091034.pdf'
2025-07-22 09:10:34,381 - __main__ - INFO - PayslipWorker-3 completed: Payslip_505_20250625.pdf
2025-07-22 09:10:34,490 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel )
2025-07-22 09:10:34,490 - __main__ - INFO - Successfully sent payslip to Jose Daniel  (<EMAIL>)
2025-07-22 09:10:34,492 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_501_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_501_20250625_20250722_091034.pdf'
2025-07-22 09:10:34,492 - __main__ - INFO - PayslipWorker-3 completed: Payslip_501_20250625.pdf
2025-07-22 09:10:34,492 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-22 09:10:34,492 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-22 09:10:34,492 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_505_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_505_20250625_20250722_091034.pdf'
2025-07-22 09:10:34,492 - __main__ - INFO - PayslipWorker-4 completed: Payslip_505_20250625.pdf
2025-07-22 09:10:34,700 - email_service - INFO - Email sent <NAME_EMAIL> (Test 2 )
2025-07-22 09:10:34,700 - __main__ - INFO - Successfully sent payslip to Test 2  (<EMAIL>)
2025-07-22 09:10:34,700 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_503_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_503_20250625_20250722_091034.pdf'
2025-07-22 09:10:34,700 - __main__ - INFO - PayslipWorker-2 completed: Payslip_503_20250625.pdf
2025-07-22 09:10:34,843 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel )
2025-07-22 09:10:34,843 - __main__ - INFO - Successfully sent payslip to Jose Daniel  (<EMAIL>)
2025-07-22 09:10:34,843 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_501_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_501_20250625_20250722_091034.pdf'
2025-07-22 09:10:34,843 - __main__ - INFO - PayslipWorker-4 completed: Payslip_501_20250625.pdf
2025-07-22 09:10:34,941 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-22 09:10:34,941 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-22 09:10:34,941 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_505_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_505_20250625_20250722_091034.pdf'
2025-07-22 09:10:34,941 - __main__ - INFO - PayslipWorker-4 completed: Payslip_505_20250625.pdf
2025-07-22 09:10:35,033 - email_service - INFO - Email sent <NAME_EMAIL> (Test 2 )
2025-07-22 09:10:35,033 - __main__ - INFO - Successfully sent payslip to Test 2  (<EMAIL>)
2025-07-22 09:10:35,033 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_503_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_503_20250625_20250722_091035.pdf'
2025-07-22 09:10:35,033 - __main__ - INFO - PayslipWorker-5 completed: Payslip_503_20250625.pdf
2025-07-22 09:10:35,033 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-22 09:10:35,033 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-22 09:10:35,033 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_502_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_502_20250625_20250722_091035.pdf'
2025-07-22 09:10:35,033 - __main__ - INFO - PayslipWorker-4 completed: Payslip_502_20250625.pdf
2025-07-22 09:10:35,049 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel )
2025-07-22 09:10:35,049 - __main__ - INFO - Successfully sent payslip to Jose Daniel  (<EMAIL>)
2025-07-22 09:10:35,049 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_501_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_501_20250625_20250722_091035.pdf'
2025-07-22 09:10:35,049 - __main__ - INFO - PayslipWorker-4 completed: Payslip_501_20250625.pdf
2025-07-22 09:10:35,097 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-22 09:10:35,097 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-22 09:10:35,097 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_505_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_505_20250625_20250722_091035.pdf'
2025-07-22 09:10:35,097 - __main__ - INFO - PayslipWorker-1 completed: Payslip_505_20250625.pdf
2025-07-22 09:10:35,097 - email_service - INFO - Email sent <NAME_EMAIL> (Test 2 )
2025-07-22 09:10:35,097 - __main__ - INFO - Successfully sent payslip to Test 2  (<EMAIL>)
2025-07-22 09:10:35,097 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_503_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_503_20250625_20250722_091035.pdf'
2025-07-22 09:10:35,097 - __main__ - INFO - PayslipWorker-3 completed: Payslip_503_20250625.pdf
2025-07-22 09:10:35,800 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel )
2025-07-22 09:10:35,800 - __main__ - INFO - Successfully sent payslip to Jose Daniel  (<EMAIL>)
2025-07-22 09:10:35,800 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_501_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_501_20250625_20250722_091035.pdf'
2025-07-22 09:10:35,800 - __main__ - INFO - PayslipWorker-2 completed: Payslip_501_20250625.pdf
2025-07-22 09:10:36,085 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-22 09:10:36,085 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-22 09:10:36,085 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_506_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_506_20250625_20250722_091036.pdf'
2025-07-22 09:10:36,085 - __main__ - INFO - PayslipWorker-1 completed: Payslip_506_20250625.pdf
2025-07-22 09:10:36,644 - email_service - INFO - Email sent <NAME_EMAIL> (Test 2 )
2025-07-22 09:10:36,644 - __main__ - INFO - Successfully sent payslip to Test 2  (<EMAIL>)
2025-07-22 09:10:36,644 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_503_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_503_20250625_20250722_091036.pdf'
2025-07-22 09:10:36,644 - __main__ - INFO - PayslipWorker-4 completed: Payslip_503_20250625.pdf
2025-07-22 09:10:37,614 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-22 09:10:37,614 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-22 09:10:37,614 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_505_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_505_20250625_20250722_091037.pdf'
2025-07-22 09:10:37,614 - __main__ - INFO - PayslipWorker-2 completed: Payslip_505_20250625.pdf
2025-07-22 09:10:37,974 - __main__ - INFO - Found 5 new payslip files to process
2025-07-22 09:10:37,974 - __main__ - INFO - Queuing file for processing: Payslip_501_20250625.pdf
2025-07-22 09:10:37,974 - __main__ - INFO - PayslipWorker-5 processing: Payslip_501_20250625.pdf
2025-07-22 09:10:37,974 - __main__ - INFO - Processing payslip for employee 501, date 20250625
2025-07-22 09:10:37,974 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:37,974 - __main__ - INFO - Queuing file for processing: Payslip_502_20250625.pdf
2025-07-22 09:10:37,974 - __main__ - INFO - Queuing file for processing: Payslip_503_20250625.pdf
2025-07-22 09:10:37,974 - __main__ - INFO - PayslipWorker-3 processing: Payslip_502_20250625.pdf
2025-07-22 09:10:37,974 - __main__ - INFO - PayslipWorker-4 processing: Payslip_503_20250625.pdf
2025-07-22 09:10:37,974 - __main__ - INFO - Processing payslip for employee 503, date 20250625
2025-07-22 09:10:37,974 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-22 09:10:37,974 - __main__ - INFO - Queuing file for processing: Payslip_505_20250625.pdf
2025-07-22 09:10:37,974 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:37,974 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:37,974 - __main__ - INFO - PayslipWorker-2 processing: Payslip_505_20250625.pdf
2025-07-22 09:10:37,974 - __main__ - INFO - Processing payslip for employee 505, date 20250625
2025-07-22 09:10:37,985 - __main__ - INFO - Queuing file for processing: Payslip_506_20250625.pdf
2025-07-22 09:10:37,985 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:38,075 - __main__ - INFO - Found 5 new payslip files to process
2025-07-22 09:10:38,075 - __main__ - INFO - Queuing file for processing: Payslip_501_20250625.pdf
2025-07-22 09:10:38,075 - __main__ - INFO - Queuing file for processing: Payslip_502_20250625.pdf
2025-07-22 09:10:38,075 - __main__ - INFO - PayslipWorker-3 processing: Payslip_501_20250625.pdf
2025-07-22 09:10:38,075 - __main__ - INFO - Processing payslip for employee 501, date 20250625
2025-07-22 09:10:38,075 - __main__ - INFO - PayslipWorker-4 processing: Payslip_502_20250625.pdf
2025-07-22 09:10:38,075 - __main__ - INFO - Queuing file for processing: Payslip_503_20250625.pdf
2025-07-22 09:10:38,075 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:38,075 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-22 09:10:38,075 - __main__ - INFO - Queuing file for processing: Payslip_505_20250625.pdf
2025-07-22 09:10:38,075 - __main__ - INFO - PayslipWorker-5 processing: Payslip_503_20250625.pdf
2025-07-22 09:10:38,080 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:38,080 - __main__ - INFO - Queuing file for processing: Payslip_506_20250625.pdf
2025-07-22 09:10:38,080 - __main__ - INFO - PayslipWorker-2 processing: Payslip_505_20250625.pdf
2025-07-22 09:10:38,080 - __main__ - INFO - Processing payslip for employee 503, date 20250625
2025-07-22 09:10:38,086 - __main__ - INFO - Found 5 new payslip files to process
2025-07-22 09:10:38,086 - __main__ - INFO - Queuing file for processing: Payslip_501_20250625.pdf
2025-07-22 09:10:38,086 - __main__ - INFO - Queuing file for processing: Payslip_502_20250625.pdf
2025-07-22 09:10:38,086 - __main__ - INFO - PayslipWorker-3 processing: Payslip_501_20250625.pdf
2025-07-22 09:10:38,080 - __main__ - INFO - PayslipWorker-1 processing: Payslip_506_20250625.pdf
2025-07-22 09:10:38,086 - __main__ - INFO - Queuing file for processing: Payslip_503_20250625.pdf
2025-07-22 09:10:38,086 - __main__ - INFO - PayslipWorker-2 processing: Payslip_502_20250625.pdf
2025-07-22 09:10:38,086 - __main__ - INFO - Processing payslip for employee 501, date 20250625
2025-07-22 09:10:38,086 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
pdf
2025-07-22 09:10:38,086 - __main__ - INFO - Processing payslip for employee 506, date 20250625

2025-07-22 09:10:38,086 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-22 09:10:38,091 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:38,091 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:38,091 - __main__ - INFO - Queuing file for processing: Payslip_506_20250625.pdf
2025-07-22 09:10:38,091 - __main__ - INFO - PayslipWorker-5 processing: Payslip_505_20250625.pdf
2025-07-22 09:10:38,091 - __main__ - INFO - Processing payslip for employee 503, date 20250625
2025-07-22 09:10:38,096 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:38,096 - __main__ - INFO - PayslipWorker-1 processing: Payslip_506_20250625.pdf
2025-07-22 09:10:38,096 - __main__ - INFO - Processing payslip for employee 505, date 20250625
2025-07-22 09:10:38,096 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:38,096 - __main__ - INFO - Processing payslip for employee 506, date 20250625
2025-07-22 09:10:38,096 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:38,096 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:38,122 - __main__ - INFO - Found 5 new payslip files to process
2025-07-22 09:10:38,122 - __main__ - INFO - Queuing file for processing: Payslip_501_20250625.pdf
2025-07-22 09:10:38,122 - __main__ - INFO - Queuing file for processing: Payslip_502_20250625.pdf
2025-07-22 09:10:38,122 - __main__ - INFO - Queuing file for processing: Payslip_503_20250625.pdf
2025-07-22 09:10:38,122 - __main__ - INFO - PayslipWorker-1 processing: Payslip_501_20250625.pdf
2025-07-22 09:10:38,122 - __main__ - INFO - PayslipWorker-5 processing: Payslip_502_20250625.pdf
2025-07-22 09:10:38,122 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-22 09:10:38,122 - __main__ - INFO - Processing payslip for employee 501, date 20250625
2025-07-22 09:10:38,122 - __main__ - INFO - PayslipWorker-4 processing: Payslip_503_20250625.pdf
2025-07-22 09:10:38,122 - __main__ - INFO - Queuing file for processing: Payslip_505_20250625.pdf
2025-07-22 09:10:38,122 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:38,127 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:38,127 - __main__ - INFO - Processing payslip for employee 503, date 20250625
2025-07-22 09:10:38,127 - __main__ - INFO - Queuing file for processing: Payslip_506_20250625.pdf
2025-07-22 09:10:38,127 - __main__ - INFO - PayslipWorker-2 processing: Payslip_505_20250625.pdf
2025-07-22 09:10:38,127 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:38,127 - __main__ - INFO - Processing payslip for employee 505, date 20250625
2025-07-22 09:10:38,127 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:38,320 - __main__ - INFO - Found 5 new payslip files to process
2025-07-22 09:10:38,320 - __main__ - INFO - Queuing file for processing: Payslip_501_20250625.pdf
2025-07-22 09:10:38,320 - __main__ - INFO - Queuing file for processing: Payslip_502_20250625.pdf
2025-07-22 09:10:38,320 - __main__ - INFO - PayslipWorker-2 processing: Payslip_501_20250625.pdf
2025-07-22 09:10:38,320 - __main__ - INFO - Processing payslip for employee 501, date 20250625
2025-07-22 09:10:38,322 - __main__ - INFO - Found 5 new payslip files to process
2025-07-22 09:10:38,322 - __main__ - INFO - Queuing file for processing: Payslip_501_20250625.pdf
2025-07-22 09:10:38,322 - __main__ - INFO - Queuing file for processing: Payslip_502_20250625.pdf
2025-07-22 09:10:38,322 - __main__ - INFO - PayslipWorker-5 processing: Payslip_501_20250625.pdf
2025-07-22 09:10:38,322 - __main__ - INFO - Processing payslip for employee 501, date 20250625
2025-07-22 09:10:38,320 - __main__ - INFO - PayslipWorker-3 processing: Payslip_502_20250625.pdf
2025-07-22 09:10:38,322 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-22 09:10:38,322 - __main__ - INFO - PayslipWorker-4 processing: Payslip_502_20250625.pdf
2025-07-22 09:10:38,322 - __main__ - INFO - Queuing file for processing: Payslip_503_20250625.pdf
2025-07-22 09:10:38,322 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:38,322 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-22 09:10:38,322 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:38,320 - __main__ - INFO - Queuing file for processing: Payslip_503_20250625.pdf
2025-07-22 09:10:38,322 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:38,322 - __main__ - INFO - Queuing file for processing: Payslip_505_20250625.pdf
2025-07-22 09:10:38,322 - __main__ - INFO - PayslipWorker-4 processing: Payslip_503_20250625.pdf
2025-07-22 09:10:38,322 - __main__ - INFO - Queuing file for processing: Payslip_506_20250625.pdf
2025-07-22 09:10:38,322 - __main__ - INFO - PayslipWorker-5 processing: Payslip_505_20250625.pdf
2025-07-22 09:10:38,322 - __main__ - INFO - Processing payslip for employee 503, date 20250625
2025-07-22 09:10:38,322 - __main__ - INFO - Queuing file for processing: Payslip_505_20250625.pdf
2025-07-22 09:10:38,322 - __main__ - INFO - PayslipWorker-3 processing: Payslip_503_20250625.pdf
2025-07-22 09:10:38,322 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:38,322 - __main__ - INFO - Queuing file for processing: Payslip_506_20250625.pdf
2025-07-22 09:10:38,322 - __main__ - INFO - PayslipWorker-1 processing: Payslip_505_20250625.pdf
2025-07-22 09:10:38,322 - __main__ - INFO - Processing payslip for employee 503, date 20250625
2025-07-22 09:10:38,322 - __main__ - INFO - Processing payslip for employee 505, date 20250625
2025-07-22 09:10:38,322 - __main__ - INFO - PayslipWorker-2 processing: Payslip_506_20250625.pdf
2025-07-22 09:10:38,322 - __main__ - INFO - Processing payslip for employee 505, date 20250625
2025-07-22 09:10:38,322 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:38,334 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:38,334 - __main__ - INFO - Processing payslip for employee 506, date 20250625
2025-07-22 09:10:38,334 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:38,334 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:38,341 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:38,341 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-22 09:10:38,341 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-22 09:10:38,341 - __main__ - ERROR - Error moving file to archived directory: [WinError 32] The process cannot access the file because it is being used by another process: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_502_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_502_20250625_20250722_091038.pdf'
2025-07-22 09:10:38,341 - __main__ - INFO - PayslipWorker-1 completed: Payslip_502_20250625.pdf
2025-07-22 09:10:38,341 - __main__ - INFO - PayslipWorker-1 processing: Payslip_506_20250625.pdf
2025-07-22 09:10:38,341 - __main__ - INFO - Processing payslip for employee 506, date 20250625
2025-07-22 09:10:38,351 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:38,405 - __main__ - INFO - Found 5 new payslip files to process
2025-07-22 09:10:38,405 - __main__ - INFO - Queuing file for processing: Payslip_501_20250625.pdf
2025-07-22 09:10:38,405 - __main__ - INFO - Queuing file for processing: Payslip_502_20250625.pdf
2025-07-22 09:10:38,405 - __main__ - INFO - PayslipWorker-4 processing: Payslip_501_20250625.pdf
2025-07-22 09:10:38,405 - __main__ - INFO - Processing payslip for employee 501, date 20250625
2025-07-22 09:10:38,405 - __main__ - INFO - Queuing file for processing: Payslip_503_20250625.pdf
2025-07-22 09:10:38,409 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:38,409 - __main__ - INFO - PayslipWorker-2 processing: Payslip_502_20250625.pdf
2025-07-22 09:10:38,409 - __main__ - INFO - Queuing file for processing: Payslip_505_20250625.pdf
2025-07-22 09:10:38,409 - __main__ - INFO - PayslipWorker-1 processing: Payslip_503_20250625.pdf
2025-07-22 09:10:38,409 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-22 09:10:38,409 - __main__ - INFO - Queuing file for processing: Payslip_506_20250625.pdf
2025-07-22 09:10:38,409 - __main__ - INFO - PayslipWorker-3 processing: Payslip_505_20250625.pdf
2025-07-22 09:10:38,409 - __main__ - INFO - Processing payslip for employee 503, date 20250625
2025-07-22 09:10:38,413 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:38,413 - __main__ - INFO - Processing payslip for employee 505, date 20250625
2025-07-22 09:10:38,413 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:38,413 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:38,464 - __main__ - INFO - Found 5 new payslip files to process
2025-07-22 09:10:38,464 - __main__ - INFO - Queuing file for processing: Payslip_501_20250625.pdf
2025-07-22 09:10:38,464 - __main__ - INFO - Queuing file for processing: Payslip_502_20250625.pdf
2025-07-22 09:10:38,464 - __main__ - INFO - Queuing file for processing: Payslip_503_20250625.pdf
2025-07-22 09:10:38,464 - __main__ - INFO - Queuing file for processing: Payslip_505_20250625.pdf
2025-07-22 09:10:38,464 - __main__ - INFO - Queuing file for processing: Payslip_506_20250625.pdf
2025-07-22 09:10:38,464 - __main__ - INFO - PayslipWorker-4 processing: Payslip_501_20250625.pdf
2025-07-22 09:10:38,464 - __main__ - INFO - Processing payslip for employee 501, date 20250625
2025-07-22 09:10:38,464 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:38,464 - __main__ - INFO - PayslipWorker-5 processing: Payslip_502_20250625.pdf
2025-07-22 09:10:38,464 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-22 09:10:38,464 - __main__ - INFO - PayslipWorker-3 processing: Payslip_505_20250625.pdf
2025-07-22 09:10:38,464 - __main__ - INFO - PayslipWorker-1 processing: Payslip_506_20250625.pdf
2025-07-22 09:10:38,464 - __main__ - INFO - PayslipWorker-2 processing: Payslip_503_20250625.pdf
2025-07-22 09:10:38,464 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:38,464 - __main__ - INFO - Processing payslip for employee 505, date 20250625
2025-07-22 09:10:38,464 - __main__ - INFO - Processing payslip for employee 506, date 20250625
2025-07-22 09:10:38,464 - __main__ - INFO - Processing payslip for employee 503, date 20250625
2025-07-22 09:10:38,464 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:38,477 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:38,477 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:40,059 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-22 09:10:40,059 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-22 09:10:40,059 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_505_20250625_20250722_091040.pdf
2025-07-22 09:10:40,059 - __main__ - INFO - PayslipWorker-5 completed: Payslip_505_20250625.pdf
2025-07-22 09:10:40,059 - __main__ - INFO - PayslipWorker-5 processing: Payslip_506_20250625.pdf
2025-07-22 09:10:40,059 - __main__ - INFO - Processing payslip for employee 506, date 20250625
2025-07-22 09:10:40,059 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:40,415 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-22 09:10:40,415 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-22 09:10:40,415 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_505_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_505_20250625_20250722_091040.pdf'
2025-07-22 09:10:40,415 - __main__ - INFO - PayslipWorker-1 completed: Payslip_505_20250625.pdf
2025-07-22 09:10:40,415 - __main__ - INFO - PayslipWorker-1 processing: Payslip_506_20250625.pdf
2025-07-22 09:10:40,415 - __main__ - INFO - Processing payslip for employee 506, date 20250625
2025-07-22 09:10:40,415 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:40,637 - email_service - INFO - Email sent <NAME_EMAIL> (Test 2 )
2025-07-22 09:10:40,637 - __main__ - INFO - Successfully sent payslip to Test 2  (<EMAIL>)
2025-07-22 09:10:40,641 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_503_20250625_20250722_091040.pdf
2025-07-22 09:10:40,641 - __main__ - INFO - PayslipWorker-3 completed: Payslip_503_20250625.pdf
2025-07-22 09:10:40,641 - __main__ - INFO - PayslipWorker-3 processing: Payslip_506_20250625.pdf
2025-07-22 09:10:40,641 - __main__ - INFO - Processing payslip for employee 506, date 20250625
2025-07-22 09:10:40,641 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 09:10:46,205 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-22 09:10:46,205 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-22 09:10:46,205 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_505_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_505_20250625_20250722_091046.pdf'
2025-07-22 09:10:46,205 - __main__ - INFO - PayslipWorker-2 completed: Payslip_505_20250625.pdf
2025-07-22 09:10:47,015 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-22 09:10:47,015 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-22 09:10:47,018 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_502_20250625_20250722_091047.pdf
2025-07-22 09:10:47,018 - __main__ - INFO - PayslipWorker-5 completed: Payslip_502_20250625.pdf
2025-07-22 09:10:47,843 - email_service - INFO - Email sent <NAME_EMAIL> (Test 2 )
2025-07-22 09:10:47,843 - __main__ - INFO - Successfully sent payslip to Test 2  (<EMAIL>)
2025-07-22 09:10:47,843 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_503_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_503_20250625_20250722_091047.pdf'
2025-07-22 09:10:47,843 - __main__ - INFO - PayslipWorker-5 completed: Payslip_503_20250625.pdf
2025-07-22 09:10:48,654 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel )
2025-07-22 09:10:48,654 - __main__ - INFO - Successfully sent payslip to Jose Daniel  (<EMAIL>)
2025-07-22 09:10:48,654 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_501_20250625_20250722_091048.pdf
2025-07-22 09:10:48,654 - __main__ - INFO - PayslipWorker-5 completed: Payslip_501_20250625.pdf
2025-07-22 09:10:52,543 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel )
2025-07-22 09:10:52,543 - __main__ - INFO - Successfully sent payslip to Jose Daniel  (<EMAIL>)
2025-07-22 09:10:52,543 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_501_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_501_20250625_20250722_091052.pdf'
2025-07-22 09:10:52,543 - __main__ - INFO - PayslipWorker-3 completed: Payslip_501_20250625.pdf
2025-07-22 09:10:53,083 - email_service - INFO - Email sent <NAME_EMAIL> (Test 2 )
2025-07-22 09:10:53,083 - __main__ - INFO - Successfully sent payslip to Test 2  (<EMAIL>)
2025-07-22 09:10:53,083 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_503_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_503_20250625_20250722_091053.pdf'
2025-07-22 09:10:53,083 - __main__ - INFO - PayslipWorker-2 completed: Payslip_503_20250625.pdf
2025-07-22 09:10:54,076 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-22 09:10:54,076 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-22 09:10:54,076 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_506_20250625_20250722_091054.pdf
2025-07-22 09:10:54,076 - __main__ - INFO - PayslipWorker-1 completed: Payslip_506_20250625.pdf
2025-07-22 09:10:54,683 - email_service - INFO - Email sent <NAME_EMAIL> (Test 2 )
2025-07-22 09:10:54,683 - __main__ - INFO - Successfully sent payslip to Test 2  (<EMAIL>)
2025-07-22 09:10:54,683 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_503_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_503_20250625_20250722_091054.pdf'
2025-07-22 09:10:54,683 - __main__ - INFO - PayslipWorker-4 completed: Payslip_503_20250625.pdf
2025-07-22 09:10:55,346 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-22 09:10:55,346 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-22 09:10:55,346 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_506_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_506_20250625_20250722_091055.pdf'
2025-07-22 09:10:55,346 - __main__ - INFO - PayslipWorker-1 completed: Payslip_506_20250625.pdf
2025-07-22 09:10:56,097 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-22 09:10:56,097 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-22 09:10:56,097 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_506_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_506_20250625_20250722_091056.pdf'
2025-07-22 09:10:56,097 - __main__ - INFO - PayslipWorker-1 completed: Payslip_506_20250625.pdf
2025-07-22 09:10:56,316 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel )
2025-07-22 09:10:56,316 - __main__ - INFO - Successfully sent payslip to Jose Daniel  (<EMAIL>)
2025-07-22 09:10:56,316 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_501_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_501_20250625_20250722_091056.pdf'
2025-07-22 09:10:56,316 - __main__ - INFO - PayslipWorker-4 completed: Payslip_501_20250625.pdf
2025-07-22 09:10:56,368 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel )
2025-07-22 09:10:56,368 - __main__ - INFO - Successfully sent payslip to Jose Daniel  (<EMAIL>)
2025-07-22 09:10:56,368 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_501_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_501_20250625_20250722_091056.pdf'
2025-07-22 09:10:56,368 - __main__ - INFO - PayslipWorker-3 completed: Payslip_501_20250625.pdf
2025-07-22 09:10:56,443 - email_service - INFO - Email sent <NAME_EMAIL> (Test 2 )
2025-07-22 09:10:56,443 - __main__ - INFO - Successfully sent payslip to Test 2  (<EMAIL>)
2025-07-22 09:10:56,443 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_503_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_503_20250625_20250722_091056.pdf'
2025-07-22 09:10:56,443 - __main__ - INFO - PayslipWorker-4 completed: Payslip_503_20250625.pdf
2025-07-22 09:10:56,569 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel )
2025-07-22 09:10:56,569 - __main__ - INFO - Successfully sent payslip to Jose Daniel  (<EMAIL>)
2025-07-22 09:10:56,569 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_501_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_501_20250625_20250722_091056.pdf'
2025-07-22 09:10:56,569 - __main__ - INFO - PayslipWorker-1 completed: Payslip_501_20250625.pdf
2025-07-22 09:10:56,585 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel )
2025-07-22 09:10:56,585 - __main__ - INFO - Successfully sent payslip to Jose Daniel  (<EMAIL>)
2025-07-22 09:10:56,585 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_501_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_501_20250625_20250722_091056.pdf'
2025-07-22 09:10:56,585 - __main__ - INFO - PayslipWorker-2 completed: Payslip_501_20250625.pdf
2025-07-22 09:10:56,740 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-22 09:10:56,741 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-22 09:10:56,742 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_505_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_505_20250625_20250722_091056.pdf'
2025-07-22 09:10:56,742 - __main__ - INFO - PayslipWorker-2 completed: Payslip_505_20250625.pdf
2025-07-22 09:10:56,929 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-22 09:10:56,939 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-22 09:10:56,939 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_502_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_502_20250625_20250722_091056.pdf'
2025-07-22 09:10:56,939 - __main__ - INFO - PayslipWorker-2 completed: Payslip_502_20250625.pdf
2025-07-22 09:10:56,959 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel )
2025-07-22 09:10:56,959 - __main__ - INFO - Successfully sent payslip to Jose Daniel  (<EMAIL>)
2025-07-22 09:10:56,959 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_501_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_501_20250625_20250722_091056.pdf'
2025-07-22 09:10:56,959 - __main__ - INFO - PayslipWorker-5 completed: Payslip_501_20250625.pdf
2025-07-22 09:10:57,010 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-22 09:10:57,010 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-22 09:10:57,010 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_502_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_502_20250625_20250722_091057.pdf'
2025-07-22 09:10:57,010 - __main__ - INFO - PayslipWorker-3 completed: Payslip_502_20250625.pdf
2025-07-22 09:10:57,061 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-22 09:10:57,061 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-22 09:10:57,061 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_506_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_506_20250625_20250722_091057.pdf'
2025-07-22 09:10:57,061 - __main__ - INFO - PayslipWorker-3 completed: Payslip_506_20250625.pdf
2025-07-22 09:10:57,105 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-22 09:10:57,105 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-22 09:10:57,105 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_505_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_505_20250625_20250722_091057.pdf'
2025-07-22 09:10:57,105 - __main__ - INFO - PayslipWorker-1 completed: Payslip_505_20250625.pdf
2025-07-22 09:10:57,120 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-22 09:10:57,121 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-22 09:10:57,121 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_502_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_502_20250625_20250722_091057.pdf'
2025-07-22 09:10:57,121 - __main__ - INFO - PayslipWorker-4 completed: Payslip_502_20250625.pdf
2025-07-22 09:10:57,156 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-22 09:10:57,172 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-22 09:10:57,174 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_506_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_506_20250625_20250722_091057.pdf'
2025-07-22 09:10:57,174 - __main__ - INFO - PayslipWorker-1 completed: Payslip_506_20250625.pdf
2025-07-22 09:10:57,307 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-22 09:10:57,307 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-22 09:10:57,307 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_506_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_506_20250625_20250722_091057.pdf'
2025-07-22 09:10:57,307 - __main__ - INFO - PayslipWorker-1 completed: Payslip_506_20250625.pdf
2025-07-22 09:10:57,329 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-22 09:10:57,329 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-22 09:10:57,329 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_505_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_505_20250625_20250722_091057.pdf'
2025-07-22 09:10:57,329 - __main__ - INFO - PayslipWorker-3 completed: Payslip_505_20250625.pdf
2025-07-22 09:10:57,457 - email_service - INFO - Email sent <NAME_EMAIL> (Test 2 )
2025-07-22 09:10:57,457 - __main__ - INFO - Successfully sent payslip to Test 2  (<EMAIL>)
2025-07-22 09:10:57,457 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_503_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_503_20250625_20250722_091057.pdf'
2025-07-22 09:10:57,457 - __main__ - INFO - PayslipWorker-4 completed: Payslip_503_20250625.pdf
2025-07-22 09:10:57,509 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-22 09:10:57,509 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-22 09:10:57,509 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_505_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_505_20250625_20250722_091057.pdf'
2025-07-22 09:10:57,509 - __main__ - INFO - PayslipWorker-2 completed: Payslip_505_20250625.pdf
2025-07-22 09:10:57,529 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-22 09:10:57,529 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-22 09:10:57,529 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_505_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_505_20250625_20250722_091057.pdf'
2025-07-22 09:10:57,529 - __main__ - INFO - PayslipWorker-5 completed: Payslip_505_20250625.pdf
2025-07-22 09:10:57,758 - email_service - INFO - Email sent <NAME_EMAIL> (Test 2 )
2025-07-22 09:10:57,759 - __main__ - INFO - Successfully sent payslip to Test 2  (<EMAIL>)
2025-07-22 09:10:57,759 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_503_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_503_20250625_20250722_091057.pdf'
2025-07-22 09:10:57,759 - __main__ - INFO - PayslipWorker-3 completed: Payslip_503_20250625.pdf
2025-07-22 09:10:58,016 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-22 09:10:58,017 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-22 09:10:58,017 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_502_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_502_20250625_20250722_091058.pdf'
2025-07-22 09:10:58,017 - __main__ - INFO - PayslipWorker-2 completed: Payslip_502_20250625.pdf
2025-07-22 09:10:58,029 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-22 09:10:58,029 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-22 09:10:58,030 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_506_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_506_20250625_20250722_091058.pdf'
2025-07-22 09:10:58,030 - __main__ - INFO - PayslipWorker-5 completed: Payslip_506_20250625.pdf
2025-07-22 09:10:58,367 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-22 09:10:58,367 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-22 09:10:58,368 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_505_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_505_20250625_20250722_091058.pdf'
2025-07-22 09:10:58,368 - __main__ - INFO - PayslipWorker-5 completed: Payslip_505_20250625.pdf
2025-07-22 09:10:58,751 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-22 09:10:58,752 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-22 09:10:58,752 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_502_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_502_20250625_20250722_091058.pdf'
2025-07-22 09:10:58,752 - __main__ - INFO - PayslipWorker-5 completed: Payslip_502_20250625.pdf
2025-07-22 09:10:59,125 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel )
2025-07-22 09:10:59,126 - __main__ - INFO - Successfully sent payslip to Jose Daniel  (<EMAIL>)
2025-07-22 09:10:59,126 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_501_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_501_20250625_20250722_091059.pdf'
2025-07-22 09:10:59,126 - __main__ - INFO - PayslipWorker-4 completed: Payslip_501_20250625.pdf
2025-07-22 09:10:59,965 - email_service - INFO - Email sent <NAME_EMAIL> (Test 2 )
2025-07-22 09:10:59,965 - __main__ - INFO - Successfully sent payslip to Test 2  (<EMAIL>)
2025-07-22 09:10:59,966 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_503_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_503_20250625_20250722_091059.pdf'
2025-07-22 09:10:59,966 - __main__ - INFO - PayslipWorker-4 completed: Payslip_503_20250625.pdf
2025-07-22 09:11:00,756 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-22 09:11:00,756 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-22 09:11:00,756 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_502_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_502_20250625_20250722_091100.pdf'
2025-07-22 09:11:00,756 - __main__ - INFO - PayslipWorker-3 completed: Payslip_502_20250625.pdf
2025-07-22 09:11:01,609 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-22 09:11:01,609 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-22 09:11:01,609 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_505_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_505_20250625_20250722_091101.pdf'
2025-07-22 09:11:01,609 - __main__ - INFO - PayslipWorker-3 completed: Payslip_505_20250625.pdf
2025-07-22 09:11:02,006 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-22 09:11:02,006 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-22 09:11:02,006 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_506_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_506_20250625_20250722_091102.pdf'
2025-07-22 09:11:02,006 - __main__ - INFO - PayslipWorker-2 completed: Payslip_506_20250625.pdf
2025-07-22 09:11:03,067 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-22 09:11:03,067 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-22 09:11:03,067 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_502_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_502_20250625_20250722_091103.pdf'
2025-07-22 09:11:03,067 - __main__ - INFO - PayslipWorker-4 completed: Payslip_502_20250625.pdf
2025-07-22 09:11:04,217 - email_service - INFO - Email sent <NAME_EMAIL> (Test 2 )
2025-07-22 09:11:04,218 - __main__ - INFO - Successfully sent payslip to Test 2  (<EMAIL>)
2025-07-22 09:11:04,219 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_503_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_503_20250625_20250722_091104.pdf'
2025-07-22 09:11:04,219 - __main__ - INFO - PayslipWorker-1 completed: Payslip_503_20250625.pdf
2025-07-22 09:11:12,019 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:11:28,132 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:11:28,153 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:11:28,175 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:11:28,375 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:11:28,404 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:11:28,467 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:11:28,534 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:12:12,098 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:12:28,190 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:12:28,220 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:12:28,247 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:12:28,446 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:12:28,474 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:12:28,540 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:12:28,608 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:13:12,161 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:13:28,256 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:13:28,298 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:13:28,321 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:13:28,517 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:13:28,541 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:13:28,610 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:13:28,675 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:14:12,230 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:14:28,322 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:14:28,372 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:14:28,394 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:14:28,593 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:14:28,615 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:14:28,673 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:14:28,749 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:15:12,304 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:15:28,381 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:15:28,436 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:15:28,455 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:15:28,657 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:15:28,674 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:15:28,726 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:15:28,813 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:16:12,371 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:16:28,454 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:16:28,510 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:16:28,526 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:16:28,735 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:16:28,746 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:16:28,802 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:16:28,888 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:17:12,446 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:17:28,524 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:17:28,577 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:17:28,592 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:17:28,803 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:17:28,810 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:17:28,868 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:17:28,956 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:18:12,502 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:18:28,591 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:18:28,624 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:18:28,640 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:18:28,860 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:18:28,864 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:18:28,923 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:18:29,011 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:19:12,555 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:19:28,650 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:19:28,684 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:19:28,695 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:19:28,934 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:19:28,986 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:19:29,067 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:20:12,632 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:20:28,722 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:20:28,756 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:20:28,762 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:20:29,005 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:20:29,005 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:20:29,068 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:20:29,137 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:21:12,715 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:21:28,795 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:21:28,835 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:21:28,839 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:21:29,084 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:21:29,146 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:21:29,213 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:22:12,791 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:22:28,862 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:22:28,911 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:22:28,912 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:22:29,162 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:22:29,226 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:22:29,286 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:23:12,861 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:23:28,941 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:23:28,985 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:23:29,241 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:23:29,241 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:23:29,302 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:23:29,364 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:24:12,943 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:24:29,014 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:24:29,059 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:24:29,323 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:24:29,323 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:24:29,375 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:24:29,441 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:25:13,036 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:25:29,090 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:25:29,136 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:25:29,136 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:25:29,398 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:25:29,398 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:25:29,449 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:25:29,506 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:26:13,087 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:26:29,142 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:26:29,197 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:26:29,464 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:26:29,509 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:26:29,563 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:27:13,165 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:27:29,219 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:27:29,285 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:27:29,546 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:27:29,587 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:27:29,636 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:28:13,251 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:28:29,292 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:28:29,366 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:28:29,629 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:28:29,667 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:28:29,708 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:29:13,316 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:29:29,347 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:29:29,425 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:29:29,692 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:29:29,720 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:29:29,757 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:30:13,362 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:30:29,400 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:30:29,487 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:30:29,759 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:30:29,759 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:30:29,767 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:30:29,810 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:31:13,425 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:31:29,460 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:31:29,557 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:31:29,558 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:31:29,826 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:31:29,826 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:31:29,826 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:31:29,866 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:32:13,488 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:32:29,511 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:32:29,619 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:32:29,897 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:32:29,897 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:32:29,897 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:32:29,922 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:33:13,545 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:33:29,575 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:33:29,690 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:33:29,974 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:33:29,974 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:33:29,974 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:33:29,985 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:34:13,619 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:34:29,636 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:34:29,784 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:34:29,787 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:34:30,036 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:34:30,037 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:34:30,038 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:34:30,038 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:35:13,683 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:35:29,699 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:35:29,852 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:35:29,855 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:35:30,101 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:35:30,104 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:36:13,749 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:36:29,774 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:36:29,927 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:36:29,927 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:36:30,178 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:36:30,180 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:36:30,181 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:37:13,823 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:37:29,843 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:37:29,998 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:37:30,263 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:37:30,264 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:37:30,264 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:38:13,887 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:38:29,897 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:38:30,053 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:38:30,315 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:38:30,330 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:38:30,330 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:39:13,951 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:39:29,969 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:39:30,129 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:39:30,386 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:39:30,416 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:39:30,416 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:39:30,416 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:40:14,028 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:40:30,053 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:40:30,219 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:40:30,458 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:40:30,508 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:40:30,508 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:40:30,508 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:41:14,096 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:41:30,110 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:41:30,286 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:41:30,522 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:41:30,575 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:41:30,581 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:42:14,154 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:42:30,168 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:42:30,356 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:42:30,356 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:42:30,582 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:42:30,633 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:42:30,643 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:42:30,643 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:43:14,221 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:43:30,239 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:43:30,424 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:43:30,640 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:43:30,694 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:43:30,712 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:44:14,283 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:44:30,296 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:44:30,492 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:44:30,492 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:44:30,701 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:44:30,755 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:44:30,778 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:44:30,779 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:45:14,345 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:45:30,365 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:45:30,563 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:45:30,761 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:45:30,810 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:45:30,842 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:45:30,843 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:46:14,412 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:46:30,430 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:46:30,625 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:46:30,625 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:46:30,814 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:46:30,864 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:46:30,906 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:47:14,480 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:47:30,498 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:47:30,697 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:47:30,698 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:47:30,876 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:47:30,924 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:47:30,973 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:48:14,538 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:48:30,557 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:48:30,767 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:48:30,940 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:48:30,994 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:48:31,045 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:49:14,601 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:49:30,614 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:49:30,823 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:49:30,823 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:49:30,990 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:49:31,040 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:49:31,101 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:50:14,647 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:50:30,666 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:50:30,882 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:50:31,037 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:50:31,088 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:50:31,154 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:51:14,715 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:51:30,730 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:51:30,951 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:51:30,952 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:51:31,096 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:51:31,147 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:51:31,226 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:51:31,226 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:52:14,780 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:52:30,800 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:52:31,018 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:52:31,018 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:52:31,159 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:52:31,207 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:52:31,295 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:52:31,297 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:53:14,847 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:53:30,869 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:53:31,083 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:53:31,083 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:53:31,226 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:53:31,271 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:53:31,369 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:54:14,923 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:54:30,944 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:54:31,152 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:54:31,294 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:54:31,332 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:54:31,435 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:54:31,435 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:55:14,989 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:55:31,009 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:55:31,219 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:55:31,220 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:55:31,354 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:55:31,405 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:55:31,503 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:55:31,503 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:56:15,060 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:56:31,083 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:56:31,289 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:56:31,290 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:56:31,416 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:56:31,467 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:56:31,554 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:56:31,554 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:57:15,132 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:57:31,153 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:57:31,360 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:57:31,360 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:57:31,479 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:57:31,526 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:57:31,619 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:57:31,621 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:58:15,200 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:58:31,221 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:58:31,430 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:58:31,533 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:58:31,585 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:58:31,698 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:59:15,270 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:59:31,283 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:59:31,491 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:59:31,491 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:59:31,584 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:59:31,642 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 09:59:31,762 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:00:15,327 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:00:31,347 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:00:31,543 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:00:31,644 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:00:31,701 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:00:31,825 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:01:15,397 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:01:31,410 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:01:31,601 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:01:31,701 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:01:31,761 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:01:31,886 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:02:15,454 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:02:31,466 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:02:31,669 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:02:31,764 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:02:31,820 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:02:31,946 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:02:31,946 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:03:15,516 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:03:31,526 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:03:31,739 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:03:31,739 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:03:31,824 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:03:31,885 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:03:32,014 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:03:32,014 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:04:15,573 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:04:31,581 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:04:31,804 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:04:31,804 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:04:31,885 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:04:31,947 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:04:32,078 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:04:32,078 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:05:15,631 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:05:31,647 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:05:31,872 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:05:31,944 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:05:32,015 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:05:32,141 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:06:15,690 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:06:31,707 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:06:31,942 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:06:32,004 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:06:32,074 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:06:32,211 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:06:32,211 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:07:15,760 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:07:31,779 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:07:32,012 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:07:32,067 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:07:32,140 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:07:32,279 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:08:15,833 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:08:31,852 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:08:32,082 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:08:32,083 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:08:32,134 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:08:32,205 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:08:32,351 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:08:32,351 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:09:15,893 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:09:31,911 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:09:32,133 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:09:32,134 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:09:32,175 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:09:32,256 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:09:32,412 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:09:32,412 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:10:15,965 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:10:31,983 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:10:32,206 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:10:32,244 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:10:32,325 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:10:32,477 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:11:16,035 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:11:32,055 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:11:32,273 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:11:32,273 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:11:32,308 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:11:32,389 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:11:32,539 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:12:16,104 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:12:32,122 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:12:32,340 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:12:32,373 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:12:32,453 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:12:32,594 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:12:32,594 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:13:16,171 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:13:32,191 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:13:32,405 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:13:32,429 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:13:32,515 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:13:32,656 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:13:32,657 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:14:16,243 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:14:32,259 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:14:32,476 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:14:32,497 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:14:32,564 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:14:32,722 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:15:16,302 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:15:32,319 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:15:32,541 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:15:32,541 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:15:32,550 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:15:32,623 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:15:32,789 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:15:32,789 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:16:16,368 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:16:32,385 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:16:32,600 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:16:32,613 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:16:32,687 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:16:32,858 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:17:16,432 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:17:32,448 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:17:32,671 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:17:32,682 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:17:32,746 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:17:32,927 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:17:32,927 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:18:16,506 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:18:32,523 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:18:32,749 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:18:32,749 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:18:32,753 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:18:32,820 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:18:33,001 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:18:33,001 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:19:16,563 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:19:32,579 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:19:32,821 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:19:32,821 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:19:32,824 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:19:32,886 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:19:33,068 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:19:33,068 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:20:16,631 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:20:32,649 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:20:32,907 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:20:32,908 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:20:32,908 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:20:32,953 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:20:33,138 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:20:33,138 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:21:16,702 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:21:32,714 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:21:32,983 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:21:32,984 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:21:33,013 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:21:33,204 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:22:16,763 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:22:32,782 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:22:33,045 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:22:33,049 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:22:33,050 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:22:33,074 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:22:33,272 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:23:16,834 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:23:32,847 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:23:33,103 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:23:33,118 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:23:33,118 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:23:33,136 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:23:33,338 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:23:33,338 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:24:16,893 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:24:17,890 - __main__ - INFO - Found 1 new payslip files to process
2025-07-22 10:24:17,890 - __main__ - INFO - Queuing file for processing: Payslip_511_20250625.pdf
2025-07-22 10:24:17,890 - __main__ - INFO - PayslipWorker-4 processing: Payslip_511_20250625.pdf
2025-07-22 10:24:17,890 - __main__ - INFO - Processing payslip for employee 511, date 20250625
2025-07-22 10:24:17,894 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 10:24:17,894 - __main__ - INFO - Found 1 new payslip files to process
2025-07-22 10:24:17,894 - __main__ - INFO - Queuing file for processing: Payslip_511_20250625.pdf
2025-07-22 10:24:17,894 - __main__ - INFO - PayslipWorker-2 processing: Payslip_511_20250625.pdf
2025-07-22 10:24:17,894 - __main__ - INFO - Processing payslip for employee 511, date 20250625
2025-07-22 10:24:17,898 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 10:24:18,144 - __main__ - INFO - Found 1 new payslip files to process
2025-07-22 10:24:18,144 - __main__ - INFO - Queuing file for processing: Payslip_511_20250625.pdf
2025-07-22 10:24:18,144 - __main__ - INFO - PayslipWorker-2 processing: Payslip_511_20250625.pdf
2025-07-22 10:24:18,144 - __main__ - INFO - Processing payslip for employee 511, date 20250625
2025-07-22 10:24:18,148 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 10:24:18,164 - __main__ - INFO - Found 1 new payslip files to process
2025-07-22 10:24:18,164 - __main__ - INFO - Found 1 new payslip files to process
2025-07-22 10:24:18,164 - __main__ - INFO - Queuing file for processing: Payslip_511_20250625.pdf
2025-07-22 10:24:18,164 - __main__ - INFO - Queuing file for processing: Payslip_511_20250625.pdf
2025-07-22 10:24:18,164 - __main__ - INFO - PayslipWorker-3 processing: Payslip_511_20250625.pdf
2025-07-22 10:24:18,164 - __main__ - INFO - Processing payslip for employee 511, date 20250625

2025-07-22 10:24:18,165 - __main__ - INFO - Processing payslip for employee 511, date 20250625
2025-07-22 10:24:18,168 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 10:24:18,168 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 10:24:18,175 - __main__ - INFO - Found 1 new payslip files to process
2025-07-22 10:24:18,175 - __main__ - INFO - Queuing file for processing: Payslip_511_20250625.pdf
2025-07-22 10:24:18,175 - __main__ - INFO - PayslipWorker-2 processing: Payslip_511_20250625.pdf
2025-07-22 10:24:18,175 - __main__ - INFO - Processing payslip for employee 511, date 20250625
2025-07-22 10:24:18,180 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 10:24:18,380 - __main__ - INFO - Found 1 new payslip files to process
2025-07-22 10:24:18,381 - __main__ - INFO - Queuing file for processing: Payslip_511_20250625.pdf
2025-07-22 10:24:18,381 - __main__ - INFO - PayslipWorker-4 processing: Payslip_511_20250625.pdf
2025-07-22 10:24:18,381 - __main__ - INFO - Processing payslip for employee 511, date 20250625
2025-07-22 10:24:18,381 - __main__ - INFO - Found 1 new payslip files to process
2025-07-22 10:24:18,381 - __main__ - INFO - Queuing file for processing: Payslip_511_20250625.pdf
2025-07-22 10:24:18,381 - __main__ - INFO - PayslipWorker-3 processing: Payslip_511_20250625.pdf
2025-07-22 10:24:18,381 - __main__ - INFO - Processing payslip for employee 511, date 20250625
2025-07-22 10:24:18,385 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 10:24:18,385 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 10:24:27,212 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-22 10:24:27,212 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-22 10:24:27,214 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_511_20250625.pdf
2025-07-22 10:24:27,214 - __main__ - INFO - PayslipWorker-2 completed: Payslip_511_20250625.pdf
2025-07-22 10:24:27,332 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-22 10:24:27,332 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-22 10:24:27,334 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_511_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_511_20250625_20250722_102427.pdf'
2025-07-22 10:24:27,334 - __main__ - INFO - PayslipWorker-2 completed: Payslip_511_20250625.pdf
2025-07-22 10:24:27,833 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-22 10:24:27,834 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-22 10:24:27,835 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_511_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_511_20250625_20250722_102427.pdf'
2025-07-22 10:24:27,835 - __main__ - INFO - PayslipWorker-2 completed: Payslip_511_20250625.pdf
2025-07-22 10:24:28,530 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-22 10:24:28,530 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-22 10:24:28,530 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_511_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_511_20250625_20250722_102428.pdf'
2025-07-22 10:24:28,530 - __main__ - INFO - PayslipWorker-3 completed: Payslip_511_20250625.pdf
2025-07-22 10:24:29,482 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-22 10:24:29,483 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-22 10:24:29,484 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_511_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_511_20250625_20250722_102429.pdf'
2025-07-22 10:24:29,485 - __main__ - INFO - PayslipWorker-4 completed: Payslip_511_20250625.pdf
2025-07-22 10:24:30,695 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-22 10:24:30,695 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-22 10:24:30,695 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_511_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_511_20250625_20250722_102430.pdf'
2025-07-22 10:24:30,695 - __main__ - INFO - PayslipWorker-4 completed: Payslip_511_20250625.pdf
2025-07-22 10:24:31,209 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-22 10:24:31,210 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-22 10:24:31,211 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_511_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_511_20250625_20250722_102431.pdf'
2025-07-22 10:24:31,211 - __main__ - INFO - PayslipWorker-4 completed: Payslip_511_20250625.pdf
2025-07-22 10:24:32,122 - email_service - INFO - Email sent <NAME_EMAIL> (Test3)
2025-07-22 10:24:32,123 - __main__ - INFO - Successfully sent payslip to Test3 (<EMAIL>)
2025-07-22 10:24:32,124 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_511_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_511_20250625_20250722_102432.pdf'
2025-07-22 10:24:32,124 - __main__ - INFO - PayslipWorker-3 completed: Payslip_511_20250625.pdf
2025-07-22 10:24:32,904 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:24:33,158 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:24:33,180 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:24:33,189 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:24:33,395 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:25:16,954 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:25:32,958 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:25:33,230 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:25:33,246 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:25:33,246 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:25:33,246 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:25:33,468 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:25:33,471 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:25:44,966 - __main__ - INFO - Found 1 new payslip files to process
2025-07-22 10:25:44,966 - __main__ - INFO - Queuing file for processing: Payslip_513_20250625.pdf
2025-07-22 10:25:44,966 - __main__ - INFO - PayslipWorker-2 processing: Payslip_513_20250625.pdf
2025-07-22 10:25:44,966 - __main__ - INFO - Processing payslip for employee 513, date 20250625
2025-07-22 10:25:44,969 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 10:25:44,977 - __main__ - INFO - Found 1 new payslip files to process
2025-07-22 10:25:44,977 - __main__ - INFO - Queuing file for processing: Payslip_513_20250625.pdf
2025-07-22 10:25:44,977 - __main__ - INFO - PayslipWorker-4 processing: Payslip_513_20250625.pdf
2025-07-22 10:25:44,977 - __main__ - INFO - Processing payslip for employee 513, date 20250625
2025-07-22 10:25:44,980 - __main__ - ERROR - Employee ID 513 not found in database
2025-07-22 10:25:44,980 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 10:25:44,983 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 10:25:44,992 - __main__ - WARNING - Logged failed send for employee 513: Employee ID 513 not found in database
2025-07-22 10:25:44,992 - __main__ - ERROR - Employee ID 513 not found in database
0250625.pdf
2025-07-22 10:25:44,996 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 10:25:45,000 - __main__ - WARNING - Logged failed send for employee 513: Employee ID 513 not found in database
2025-07-22 10:25:45,000 - __main__ - INFO - PayslipWorker-4 completed: Payslip_513_20250625.pdf
2025-07-22 10:25:45,239 - __main__ - INFO - Found 1 new payslip files to process
2025-07-22 10:25:45,239 - __main__ - INFO - Queuing file for processing: Payslip_513_20250625.pdf
2025-07-22 10:25:45,239 - __main__ - INFO - PayslipWorker-4 processing: Payslip_513_20250625.pdf
2025-07-22 10:25:45,239 - __main__ - INFO - Processing payslip for employee 513, date 20250625
2025-07-22 10:25:45,243 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 10:25:45,253 - __main__ - ERROR - Employee ID 513 not found in database
2025-07-22 10:25:45,255 - __main__ - INFO - Found 1 new payslip files to process
2025-07-22 10:25:45,255 - __main__ - INFO - Queuing file for processing: Payslip_513_20250625.pdf
2025-07-22 10:25:45,255 - __main__ - INFO - PayslipWorker-4 processing: Payslip_513_20250625.pdf
2025-07-22 10:25:45,255 - __main__ - INFO - Processing payslip for employee 513, date 20250625
2025-07-22 10:25:45,256 - __main__ - INFO - Found 1 new payslip files to process
2025-07-22 10:25:45,256 - __main__ - INFO - Queuing file for processing: Payslip_513_20250625.pdf
2025-07-22 10:25:45,257 - __main__ - INFO - PayslipWorker-2 processing: Payslip_513_20250625.pdf
2025-07-22 10:25:45,257 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 10:25:45,257 - __main__ - INFO - Processing payslip for employee 513, date 20250625
2025-07-22 10:25:45,257 - __main__ - INFO - Found 1 new payslip files to process
2025-07-22 10:25:45,257 - __main__ - INFO - Queuing file for processing: Payslip_513_20250625.pdf
2025-07-22 10:25:45,257 - __main__ - INFO - PayslipWorker-2 processing: Payslip_513_20250625.pdf
2025-07-22 10:25:45,257 - __main__ - INFO - Processing payslip for employee 513, date 20250625
2025-07-22 10:25:45,260 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 10:25:45,260 - __main__ - WARNING - Logged failed send for employee 513: Employee ID 513 not found in database
2025-07-22 10:25:45,261 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 10:25:45,261 - __main__ - INFO - PayslipWorker-4 completed: Payslip_513_20250625.pdf
2025-07-22 10:25:45,261 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 10:25:45,270 - __main__ - ERROR - Employee ID 513 not found in database
2025-07-22 10:25:45,271 - __main__ - ERROR - Employee ID 513 not found in database
2025-07-22 10:25:45,271 - __main__ - ERROR - Employee ID 513 not found in database
2025-07-22 10:25:45,275 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 10:25:45,275 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 10:25:45,276 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 10:25:45,280 - __main__ - WARNING - Logged failed send for employee 513: Employee ID 513 not found in database
2025-07-22 10:25:45,280 - __main__ - WARNING - Logged failed send for employee 513: Employee ID 513 not found in database
2025-07-22 10:25:45,280 - __main__ - WARNING - Logged failed send for employee 513: Employee ID 513 not found in database
2025-07-22 10:25:45,280 - __main__ - INFO - PayslipWorker-4 completed: Payslip_513_20250625.pdf
2025-07-22 10:25:45,280 - __main__ - INFO - PayslipWorker-2 completed: Payslip_513_20250625.pdf
2025-07-22 10:25:45,480 - __main__ - INFO - Found 1 new payslip files to process
2025-07-22 10:25:45,480 - __main__ - INFO - Queuing file for processing: Payslip_513_20250625.pdf
2025-07-22 10:25:45,480 - __main__ - INFO - PayslipWorker-4 processing: Payslip_513_20250625.pdf
2025-07-22 10:25:45,481 - __main__ - INFO - Processing payslip for employee 513, date 20250625
2025-07-22 10:25:45,482 - __main__ - INFO - Found 1 new payslip files to process
2025-07-22 10:25:45,482 - __main__ - INFO - Queuing file for processing: Payslip_513_20250625.pdf
2025-07-22 10:25:45,482 - __main__ - INFO - PayslipWorker-1 processing: Payslip_513_20250625.pdf
2025-07-22 10:25:45,482 - __main__ - INFO - Processing payslip for employee 513, date 20250625
2025-07-22 10:25:45,485 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 10:25:45,487 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 10:25:45,497 - __main__ - ERROR - Employee ID 513 not found in database
2025-07-22 10:25:45,498 - __main__ - ERROR - Employee ID 513 not found in database
2025-07-22 10:25:45,503 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 10:25:45,503 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 10:25:45,510 - __main__ - WARNING - Logged failed send for employee 513: Employee ID 513 not found in database
2025-07-22 10:25:45,510 - __main__ - WARNING - Logged failed send for employee 513: Employee ID 513 not found in database
2025-07-22 10:25:45,510 - __main__ - INFO - PayslipWorker-1 completed: Payslip_513_20250625.pdf
2025-07-22 10:25:45,510 - __main__ - INFO - PayslipWorker-4 completed: Payslip_513_20250625.pdf
2025-07-22 10:26:17,013 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 10:26:33,017 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 10:26:33,285 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 10:26:33,309 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 10:26:33,309 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 10:26:33,522 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 10:27:17,070 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 10:27:33,064 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 10:27:33,356 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 10:27:33,372 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 10:27:33,376 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 10:27:33,376 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 10:27:33,584 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 10:28:17,113 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 10:28:33,112 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 10:28:33,404 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 10:28:33,418 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 10:28:33,427 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 10:28:33,428 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 10:28:33,636 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 10:28:33,636 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 10:29:17,165 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 10:29:33,157 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 10:29:33,447 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 10:29:33,456 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 10:29:33,472 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 10:29:33,684 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 10:30:17,209 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 10:30:33,202 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 10:30:33,493 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 10:30:33,501 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 10:30:33,526 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 10:30:33,735 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 10:31:17,270 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 10:31:33,259 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 10:31:33,541 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 10:31:33,547 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 10:31:33,578 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 10:31:33,788 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 10:32:17,341 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 10:32:33,329 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 10:32:33,593 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 10:32:33,604 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 10:32:33,651 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 10:32:33,651 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 10:32:33,865 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 10:32:33,865 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 10:33:17,417 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 10:33:33,403 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 10:33:33,668 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 10:33:33,672 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 10:33:33,725 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 10:33:33,725 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 10:33:33,936 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 10:33:33,936 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 10:34:17,484 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 10:34:33,469 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 10:34:33,731 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 10:34:33,734 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 10:34:33,785 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 10:34:33,999 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 10:35:17,543 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 10:35:19,641 - __main__ - INFO - Received signal 2, initiating graceful shutdown...
2025-07-22 10:35:19,642 - __main__ - INFO - PayslipWorker-2 received shutdown signal
2025-07-22 10:35:19,642 - __main__ - INFO - PayslipWorker-5 received shutdown signal
2025-07-22 10:35:19,642 - __main__ - INFO - PayslipWorker-5 stopped
2025-07-22 10:35:19,642 - __main__ - INFO - PayslipWorker-3 received shutdown signal
2025-07-22 10:35:19,642 - __main__ - INFO - PayslipWorker-4 received shutdown signal
2025-07-22 10:35:19,643 - __main__ - INFO - PayslipWorker-4 stopped
2025-07-22 10:35:19,642 - __main__ - INFO - PayslipWorker-1 received shutdown signal
2025-07-22 10:35:19,642 - __main__ - INFO - PayslipWorker-3 stopped
2025-07-22 10:35:19,642 - __main__ - INFO - PayslipWorker-2 stopped
2025-07-22 10:35:19,643 - __main__ - INFO - PayslipWorker-1 stopped
2025-07-22 10:35:20,546 - __main__ - INFO - Initiating graceful shutdown...
2025-07-22 10:35:20,546 - __main__ - INFO - Waiting for current file processing to complete...
2025-07-22 10:35:20,546 - __main__ - INFO - Stopping worker threads...
2025-07-22 10:35:20,546 - __main__ - INFO - PayslipSender service stopped
2025-07-22 10:35:33,527 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:35:33,780 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:35:33,784 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:35:33,845 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:35:34,054 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:35:34,055 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 0 processed files
2025-07-22 10:35:36,573 - email_service - INFO - Email service initialized for smtp.gmail.com:587
2025-07-22 10:35:36,574 - __main__ - INFO - PayslipSender initialized for production deployment
2025-07-22 10:35:36,574 - __main__ - INFO - Working directory: C:\Users\<USER>\Desktop\PayslipSender
2025-07-22 10:35:36,574 - __main__ - INFO - Watch directory: C:\Users\<USER>\Desktop\PayslipSender\payslips
2025-07-22 10:35:36,574 - __main__ - INFO - Archived directory: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips
2025-07-22 10:35:36,575 - __main__ - INFO - Starting PayslipSender service with parallel processing...
2025-07-22 10:35:38,627 - email_service - INFO - SMTP connection test successful
2025-07-22 10:35:38,639 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 10:35:38,711 - __main__ - INFO - Database connection validated
2025-07-22 10:35:38,711 - __main__ - INFO - Monitoring directory: C:\Users\<USER>\Desktop\PayslipSender\payslips
2025-07-22 10:35:38,711 - __main__ - INFO - Using 5 worker threads for parallel processing
2025-07-22 10:35:38,711 - __main__ - INFO - PayslipSender service is running
2025-07-22 10:35:38,711 - __main__ - INFO - Starting 5 worker threads for file processing
2025-07-22 10:35:38,713 - __main__ - INFO - PayslipWorker-1 started and ready for processing
2025-07-22 10:35:38,713 - __main__ - INFO - PayslipWorker-2 started and ready for processing
2025-07-22 10:35:38,713 - __main__ - INFO - PayslipWorker-3 started and ready for processing
2025-07-22 10:35:38,713 - __main__ - INFO - PayslipWorker-4 started and ready for processing
2025-07-22 10:35:38,713 - __main__ - INFO - PayslipWorker-5 started and ready for processing
2025-07-22 10:35:44,719 - __main__ - INFO - Found 2 new payslip files to process
2025-07-22 10:35:44,719 - __main__ - WARNING - Invalid filename format: Payslip_513_20250625 - Copy.pdf
2025-07-22 10:35:44,719 - __main__ - INFO - Queuing file for processing: Payslip_513_20250625.pdf
2025-07-22 10:35:44,719 - __main__ - INFO - PayslipWorker-2 processing: Payslip_513_20250625.pdf
2025-07-22 10:35:44,719 - __main__ - INFO - Processing payslip for employee 513, date 20250625
2025-07-22 10:35:44,723 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 10:35:44,724 - __main__ - ERROR - Employee ID 513 not found in database
2025-07-22 10:35:44,725 - __main__ - INFO - Attempting to log failed send for employee 513
2025-07-22 10:35:44,728 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 10:35:44,736 - __main__ - INFO - ✅ Successfully logged failed send to database (Record ID: 181) - Employee: 513, Reason: Employee ID 513 not found in database
2025-07-22 10:35:44,736 - __main__ - INFO - PayslipWorker-2 completed: Payslip_513_20250625.pdf
2025-07-22 10:35:44,789 - __main__ - INFO - Found 2 new payslip files to process
2025-07-22 10:35:44,789 - __main__ - WARNING - Invalid filename format: Payslip_513_20250625 - Copy.pdf
2025-07-22 10:35:44,789 - __main__ - INFO - Queuing file for processing: Payslip_513_20250625.pdf
2025-07-22 10:35:44,789 - __main__ - INFO - PayslipWorker-1 processing: Payslip_513_20250625.pdf
2025-07-22 10:35:44,789 - __main__ - INFO - Processing payslip for employee 513, date 20250625
2025-07-22 10:35:44,792 - __main__ - INFO - Found 2 new payslip files to process
2025-07-22 10:35:44,792 - __main__ - WARNING - Invalid filename format: Payslip_513_20250625 - Copy.pdf
2025-07-22 10:35:44,792 - __main__ - INFO - Queuing file for processing: Payslip_513_20250625.pdf
2025-07-22 10:35:44,792 - __main__ - INFO - PayslipWorker-5 processing: Payslip_513_20250625.pdf
2025-07-22 10:35:44,792 - __main__ - INFO - Processing payslip for employee 513, date 20250625
2025-07-22 10:35:44,793 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 10:35:44,795 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 10:35:44,804 - __main__ - ERROR - Employee ID 513 not found in database
2025-07-22 10:35:44,806 - __main__ - ERROR - Employee ID 513 not found in database
2025-07-22 10:35:44,808 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 10:35:44,809 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 10:35:44,813 - __main__ - WARNING - Logged failed send for employee 513: Employee ID 513 not found in database
2025-07-22 10:35:44,813 - __main__ - WARNING - Logged failed send for employee 513: Employee ID 513 not found in database
2025-07-22 10:35:44,813 - __main__ - INFO - PayslipWorker-1 completed: Payslip_513_20250625.pdf
2025-07-22 10:35:44,855 - __main__ - INFO - Found 2 new payslip files to process
2025-07-22 10:35:44,855 - __main__ - WARNING - Invalid filename format: Payslip_513_20250625 - Copy.pdf
2025-07-22 10:35:44,855 - __main__ - INFO - Queuing file for processing: Payslip_513_20250625.pdf
2025-07-22 10:35:44,855 - __main__ - INFO - PayslipWorker-1 processing: Payslip_513_20250625.pdf
2025-07-22 10:35:44,855 - __main__ - INFO - Processing payslip for employee 513, date 20250625
2025-07-22 10:35:44,855 - __main__ - INFO - Found 2 new payslip files to process
2025-07-22 10:35:44,855 - __main__ - WARNING - Invalid filename format: Payslip_513_20250625 - Copy.pdf
2025-07-22 10:35:44,855 - __main__ - INFO - Queuing file for processing: Payslip_513_20250625.pdf
2025-07-22 10:35:44,855 - __main__ - INFO - PayslipWorker-1 processing: Payslip_513_20250625.pdf
2025-07-22 10:35:44,855 - __main__ - INFO - Processing payslip for employee 513, date 20250625
2025-07-22 10:35:44,858 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 10:35:44,859 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 10:35:44,870 - __main__ - ERROR - Employee ID 513 not found in database
2025-07-22 10:35:44,873 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 10:35:44,873 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 10:35:44,877 - __main__ - WARNING - Logged failed send for employee 513: Employee ID 513 not found in database
2025-07-22 10:35:44,878 - __main__ - INFO - PayslipWorker-1 completed: Payslip_513_20250625.pdf
2025-07-22 10:35:44,878 - __main__ - INFO - PayslipWorker-1 completed: Payslip_513_20250625.pdf
2025-07-22 10:35:45,065 - __main__ - INFO - Found 2 new payslip files to process
2025-07-22 10:35:45,065 - __main__ - INFO - Found 2 new payslip files to process
2025-07-22 10:35:45,065 - __main__ - WARNING - Invalid filename format: Payslip_513_20250625 - Copy.pdf
2025-07-22 10:35:45,065 - __main__ - WARNING - Invalid filename format: Payslip_513_20250625 - Copy.pdf
2025-07-22 10:35:45,065 - __main__ - INFO - Queuing file for processing: Payslip_513_20250625.pdf
2025-07-22 10:35:45,065 - __main__ - INFO - Queuing file for processing: Payslip_513_20250625.pdf
2025-07-22 10:35:45,065 - __main__ - INFO - PayslipWorker-3 processing: Payslip_513_20250625.pdf
2025-07-22 10:35:45,065 - __main__ - INFO - PayslipWorker-1 processing: Payslip_513_20250625.pdf
2025-07-22 10:35:45,065 - __main__ - INFO - Processing payslip for employee 513, date 20250625
2025-07-22 10:35:45,065 - __main__ - INFO - Processing payslip for employee 513, date 20250625
2025-07-22 10:35:45,068 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 10:35:45,068 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 10:35:45,081 - __main__ - ERROR - Employee ID 513 not found in database
2025-07-22 10:35:45,081 - __main__ - ERROR - Employee ID 513 not found in database
2025-07-22 10:35:45,086 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 10:35:45,086 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 10:35:45,090 - __main__ - WARNING - Logged failed send for employee 513: Employee ID 513 not found in database
2025-07-22 10:35:45,090 - __main__ - WARNING - Logged failed send for employee 513: Employee ID 513 not found in database
2025-07-22 10:35:45,091 - __main__ - INFO - PayslipWorker-3 completed: Payslip_513_20250625.pdf
2025-07-22 10:35:45,091 - __main__ - INFO - PayslipWorker-1 completed: Payslip_513_20250625.pdf
2025-07-22 10:35:45,536 - __main__ - INFO - Found 2 new payslip files to process
2025-07-22 10:35:45,536 - __main__ - WARNING - Invalid filename format: Payslip_513_20250625 - Copy.pdf
2025-07-22 10:35:45,536 - __main__ - INFO - Queuing file for processing: Payslip_513_20250625.pdf
2025-07-22 10:35:45,536 - __main__ - INFO - PayslipWorker-5 processing: Payslip_513_20250625.pdf
2025-07-22 10:35:45,536 - __main__ - INFO - Processing payslip for employee 513, date 20250625
2025-07-22 10:35:45,539 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 10:35:45,549 - __main__ - ERROR - Employee ID 513 not found in database
2025-07-22 10:35:45,552 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 10:35:45,556 - __main__ - WARNING - Logged failed send for employee 513: Employee ID 513 not found in database
2025-07-22 10:35:45,557 - __main__ - INFO - PayslipWorker-5 completed: Payslip_513_20250625.pdf
2025-07-22 10:36:33,576 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:36:33,844 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:36:33,847 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:36:33,911 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:36:33,911 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:36:34,118 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:36:34,119 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:36:38,790 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:37:33,647 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:37:33,912 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:37:33,976 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:37:34,188 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:37:38,867 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:38:33,719 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:38:33,980 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:38:34,049 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:38:34,267 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:38:38,946 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:39:33,791 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:39:34,053 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:39:34,129 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:39:34,129 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:39:34,342 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:39:34,342 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:39:39,023 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:40:33,865 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:40:34,132 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:40:34,132 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:40:34,219 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:40:34,220 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:40:34,415 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:40:34,415 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:40:39,109 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:41:33,934 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:41:34,212 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:41:34,299 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:41:34,488 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:41:39,182 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:42:33,996 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:42:34,275 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:42:34,277 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:42:34,372 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:42:34,553 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:42:39,249 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:43:34,048 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:43:34,324 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:43:34,427 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:43:34,600 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:43:39,305 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:44:34,106 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:44:34,386 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:44:34,490 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:44:34,490 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:44:34,661 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:44:39,371 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:45:22,420 - __main__ - INFO - Found 1 new payslip files to process
2025-07-22 10:45:22,420 - __main__ - INFO - Queuing file for processing: Payslip_501_20250625.pdf
2025-07-22 10:45:22,421 - __main__ - INFO - PayslipWorker-1 processing: Payslip_501_20250625.pdf
2025-07-22 10:45:22,421 - __main__ - INFO - Processing payslip for employee 501, date 20250625
2025-07-22 10:45:22,424 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 10:45:22,434 - __main__ - INFO - Found 1 new payslip files to process
2025-07-22 10:45:22,434 - __main__ - INFO - Queuing file for processing: Payslip_501_20250625.pdf
2025-07-22 10:45:22,434 - __main__ - INFO - PayslipWorker-1 processing: Payslip_501_20250625.pdf
2025-07-22 10:45:22,434 - __main__ - INFO - Processing payslip for employee 501, date 20250625
2025-07-22 10:45:22,434 - __main__ - INFO - Processing payslip for employee 501, date 20250625
2025-07-22 10:45:22,438 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 10:45:22,438 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 10:45:22,545 - __main__ - INFO - Found 1 new payslip files to process
2025-07-22 10:45:22,545 - __main__ - INFO - Queuing file for processing: Payslip_501_20250625.pdf
2025-07-22 10:45:22,545 - __main__ - INFO - Queuing file for processing: Payslip_501_20250625.pdf
2025-07-22 10:45:22,545 - __main__ - INFO - PayslipWorker-5 processing: Payslip_501_20250625.pdf
2025-07-22 10:45:22,545 - __main__ - INFO - Processing payslip for employee 501, date 20250625
2025-07-22 10:45:22,545 - __main__ - INFO - Processing payslip for employee 501, date 20250625
2025-07-22 10:45:22,549 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 10:45:22,549 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 10:45:22,713 - __main__ - INFO - Found 1 new payslip files to process
2025-07-22 10:45:22,713 - __main__ - INFO - Queuing file for processing: Payslip_501_20250625.pdf
2025-07-22 10:45:22,714 - __main__ - INFO - PayslipWorker-3 processing: Payslip_501_20250625.pdf
2025-07-22 10:45:22,714 - __main__ - INFO - Processing payslip for employee 501, date 20250625
2025-07-22 10:45:22,714 - __main__ - INFO - Found 1 new payslip files to process
2025-07-22 10:45:22,714 - __main__ - INFO - Queuing file for processing: Payslip_501_20250625.pdf
2025-07-22 10:45:22,714 - __main__ - INFO - PayslipWorker-2 processing: Payslip_501_20250625.pdf
2025-07-22 10:45:22,714 - __main__ - INFO - Processing payslip for employee 501, date 20250625
2025-07-22 10:45:22,717 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 10:45:22,717 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 10:45:23,162 - __main__ - INFO - Found 1 new payslip files to process
2025-07-22 10:45:23,162 - __main__ - INFO - Queuing file for processing: Payslip_501_20250625.pdf
2025-07-22 10:45:23,162 - __main__ - INFO - PayslipWorker-2 processing: Payslip_501_20250625.pdf
2025-07-22 10:45:23,162 - __main__ - INFO - Processing payslip for employee 501, date 20250625
2025-07-22 10:45:23,165 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 10:45:31,580 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel )
2025-07-22 10:45:31,580 - __main__ - INFO - Successfully sent payslip to Jose Daniel  (<EMAIL>)
2025-07-22 10:45:31,581 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_501_20250625_20250722_104531.pdf
2025-07-22 10:45:31,582 - __main__ - INFO - PayslipWorker-1 completed: Payslip_501_20250625.pdf
2025-07-22 10:45:32,083 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel )
2025-07-22 10:45:32,083 - __main__ - INFO - Successfully sent payslip to Jose Daniel  (<EMAIL>)
2025-07-22 10:45:32,083 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_501_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_501_20250625_20250722_104532.pdf'
2025-07-22 10:45:32,083 - __main__ - INFO - PayslipWorker-1 completed: Payslip_501_20250625.pdf
2025-07-22 10:45:32,953 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel )
2025-07-22 10:45:32,953 - __main__ - INFO - Successfully sent payslip to Jose Daniel  (<EMAIL>)
2025-07-22 10:45:32,954 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_501_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_501_20250625_20250722_104532.pdf'
2025-07-22 10:45:32,954 - __main__ - INFO - PayslipWorker-2 completed: Payslip_501_20250625.pdf
2025-07-22 10:45:33,608 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel )
2025-07-22 10:45:33,609 - __main__ - INFO - Successfully sent payslip to Jose Daniel  (<EMAIL>)
2025-07-22 10:45:33,610 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_501_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_501_20250625_20250722_104533.pdf'
2025-07-22 10:45:33,610 - __main__ - INFO - PayslipWorker-5 completed: Payslip_501_20250625.pdf
2025-07-22 10:45:33,898 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel )
2025-07-22 10:45:33,898 - __main__ - INFO - Successfully sent payslip to Jose Daniel  (<EMAIL>)
2025-07-22 10:45:33,898 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_501_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_501_20250625_20250722_104533.pdf'
2025-07-22 10:45:33,899 - __main__ - INFO - PayslipWorker-2 completed: Payslip_501_20250625.pdf
2025-07-22 10:45:34,171 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:45:34,448 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:45:34,555 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:45:34,632 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel )
2025-07-22 10:45:34,633 - __main__ - INFO - Successfully sent payslip to Jose Daniel  (<EMAIL>)
2025-07-22 10:45:34,634 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_501_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_501_20250625_20250722_104534.pdf'
2025-07-22 10:45:34,635 - __main__ - INFO - PayslipWorker-2 completed: Payslip_501_20250625.pdf
2025-07-22 10:45:34,727 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:45:35,503 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel )
2025-07-22 10:45:35,504 - __main__ - INFO - Successfully sent payslip to Jose Daniel  (<EMAIL>)
2025-07-22 10:45:35,505 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_501_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_501_20250625_20250722_104535.pdf'
2025-07-22 10:45:35,505 - __main__ - INFO - PayslipWorker-5 completed: Payslip_501_20250625.pdf
2025-07-22 10:45:36,751 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel )
2025-07-22 10:45:36,751 - __main__ - INFO - Successfully sent payslip to Jose Daniel  (<EMAIL>)
2025-07-22 10:45:36,752 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_501_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_501_20250625_20250722_104536.pdf'
2025-07-22 10:45:36,752 - __main__ - INFO - PayslipWorker-3 completed: Payslip_501_20250625.pdf
2025-07-22 10:45:39,441 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:46:27,498 - __main__ - INFO - Found 1 new payslip files to process
2025-07-22 10:46:27,499 - __main__ - INFO - Queuing file for processing: Payslip_501_20250625.pdf
2025-07-22 10:46:27,499 - __main__ - INFO - PayslipWorker-1 processing: Payslip_501_20250625.pdf
2025-07-22 10:46:27,499 - __main__ - INFO - Processing payslip for employee 501, date 20250625
2025-07-22 10:46:27,502 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 10:46:27,513 - __main__ - INFO - Found 1 new payslip files to process
2025-07-22 10:46:27,513 - __main__ - INFO - Queuing file for processing: Payslip_501_20250625.pdf
2025-07-22 10:46:27,513 - __main__ - INFO - PayslipWorker-2 processing: Payslip_501_20250625.pdf
2025-07-22 10:46:27,513 - __main__ - INFO - Processing payslip for employee 501, date 20250625
2025-07-22 10:46:27,513 - __main__ - INFO - Processing payslip for employee 501, date 20250625
2025-07-22 10:46:27,516 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 10:46:27,517 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 10:46:27,605 - __main__ - INFO - Found 1 new payslip files to process
2025-07-22 10:46:27,605 - __main__ - INFO - Queuing file for processing: Payslip_501_20250625.pdf
2025-07-22 10:46:27,605 - __main__ - INFO - Queuing file for processing: Payslip_501_20250625.pdf
2025-07-22 10:46:27,605 - __main__ - INFO - PayslipWorker-5 processing: Payslip_501_20250625.pdf
2025-07-22 10:46:27,605 - __main__ - INFO - PayslipWorker-5 processing: Payslip_501_20250625.pdf
2025-07-22 10:46:27,605 - __main__ - INFO - Processing payslip for employee 501, date 20250625
2025-07-22 10:46:27,605 - __main__ - INFO - Processing payslip for employee 501, date 20250625
2025-07-22 10:46:27,608 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 10:46:27,609 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 10:46:27,784 - __main__ - INFO - Found 1 new payslip files to process
2025-07-22 10:46:27,785 - __main__ - INFO - Queuing file for processing: Payslip_501_20250625.pdf
2025-07-22 10:46:27,785 - __main__ - INFO - Queuing file for processing: Payslip_501_20250625.pdf
2025-07-22 10:46:27,785 - __main__ - INFO - PayslipWorker-4 processing: Payslip_501_20250625.pdf
2025-07-22 10:46:27,785 - __main__ - INFO - PayslipWorker-2 processing: Payslip_501_20250625.pdf
2025-07-22 10:46:27,785 - __main__ - INFO - Processing payslip for employee 501, date 20250625
2025-07-22 10:46:27,785 - __main__ - INFO - Processing payslip for employee 501, date 20250625
2025-07-22 10:46:27,789 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 10:46:27,789 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 10:46:28,237 - __main__ - INFO - Found 1 new payslip files to process
2025-07-22 10:46:28,237 - __main__ - INFO - Queuing file for processing: Payslip_501_20250625.pdf
2025-07-22 10:46:28,238 - __main__ - INFO - PayslipWorker-3 processing: Payslip_501_20250625.pdf
2025-07-22 10:46:28,238 - __main__ - INFO - Processing payslip for employee 501, date 20250625
2025-07-22 10:46:28,243 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 10:46:34,241 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 3 processed files
2025-07-22 10:46:34,520 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 3 processed files
2025-07-22 10:46:34,610 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 3 processed files
2025-07-22 10:46:34,790 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 3 processed files
2025-07-22 10:46:36,188 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel )
2025-07-22 10:46:36,189 - __main__ - INFO - Successfully sent payslip to Jose Daniel  (<EMAIL>)
2025-07-22 10:46:36,190 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_501_20250625_20250722_104636.pdf
2025-07-22 10:46:36,190 - __main__ - INFO - PayslipWorker-1 completed: Payslip_501_20250625.pdf
2025-07-22 10:46:37,072 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel )
2025-07-22 10:46:37,072 - __main__ - INFO - Successfully sent payslip to Jose Daniel  (<EMAIL>)
2025-07-22 10:46:37,072 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_501_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_501_20250625_20250722_104637.pdf'
2025-07-22 10:46:37,072 - __main__ - INFO - PayslipWorker-2 completed: Payslip_501_20250625.pdf
2025-07-22 10:46:37,810 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel )
2025-07-22 10:46:37,810 - __main__ - INFO - Successfully sent payslip to Jose Daniel  (<EMAIL>)
2025-07-22 10:46:37,810 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_501_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_501_20250625_20250722_104637.pdf'
2025-07-22 10:46:37,810 - __main__ - INFO - PayslipWorker-5 completed: Payslip_501_20250625.pdf
2025-07-22 10:46:39,357 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel )
2025-07-22 10:46:39,357 - __main__ - INFO - Successfully sent payslip to Jose Daniel  (<EMAIL>)
2025-07-22 10:46:39,358 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_501_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_501_20250625_20250722_104639.pdf'
2025-07-22 10:46:39,358 - __main__ - INFO - PayslipWorker-5 completed: Payslip_501_20250625.pdf
2025-07-22 10:46:39,510 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:46:40,128 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel )
2025-07-22 10:46:40,129 - __main__ - INFO - Successfully sent payslip to Jose Daniel  (<EMAIL>)
2025-07-22 10:46:40,129 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_501_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_501_20250625_20250722_104640.pdf'
2025-07-22 10:46:40,129 - __main__ - INFO - PayslipWorker-5 completed: Payslip_501_20250625.pdf
2025-07-22 10:46:41,048 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel )
2025-07-22 10:46:41,049 - __main__ - INFO - Successfully sent payslip to Jose Daniel  (<EMAIL>)
2025-07-22 10:46:41,051 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_501_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_501_20250625_20250722_104641.pdf'
2025-07-22 10:46:41,051 - __main__ - INFO - PayslipWorker-2 completed: Payslip_501_20250625.pdf
2025-07-22 10:46:41,959 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel )
2025-07-22 10:46:41,960 - __main__ - INFO - Successfully sent payslip to Jose Daniel  (<EMAIL>)
2025-07-22 10:46:41,961 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_501_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_501_20250625_20250722_104641.pdf'
2025-07-22 10:46:41,962 - __main__ - INFO - PayslipWorker-3 completed: Payslip_501_20250625.pdf
2025-07-22 10:46:42,840 - email_service - INFO - Email sent <NAME_EMAIL> (Jose Daniel )
2025-07-22 10:46:42,840 - __main__ - INFO - Successfully sent payslip to Jose Daniel  (<EMAIL>)
2025-07-22 10:46:42,842 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_501_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_501_20250625_20250722_104642.pdf'
2025-07-22 10:46:42,843 - __main__ - INFO - PayslipWorker-4 completed: Payslip_501_20250625.pdf
2025-07-22 10:47:34,307 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:47:34,574 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:47:34,678 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:47:34,864 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:47:39,574 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:48:34,382 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:48:34,649 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:48:34,649 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:48:34,751 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:48:34,752 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:48:34,943 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:48:39,651 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:49:34,454 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:49:34,721 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:49:34,818 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:49:35,022 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:49:39,735 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:50:34,530 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:50:34,796 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:50:34,893 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:50:35,096 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:50:35,097 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:50:39,815 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:51:34,584 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:51:34,871 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:51:34,974 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:51:34,974 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:51:35,166 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:51:35,169 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:51:39,893 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:52:34,660 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:52:34,952 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:52:35,056 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:52:35,226 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:52:35,230 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:52:39,975 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:53:34,734 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:53:35,028 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:53:35,029 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:53:35,135 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:53:35,135 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:53:35,302 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:53:35,302 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:53:40,063 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:54:34,805 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:54:35,104 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:54:35,210 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:54:35,210 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:54:35,381 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:54:40,150 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:55:34,880 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:55:35,181 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:55:35,287 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:55:35,289 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:55:35,460 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:55:35,467 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:55:40,246 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:56:34,952 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:56:35,262 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:56:35,364 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:56:35,533 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:56:35,543 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:56:40,331 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:57:35,031 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:57:35,347 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:57:35,347 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:57:35,447 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:57:35,608 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:57:35,614 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:57:40,413 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:58:35,112 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:58:35,425 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:58:35,528 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:58:35,683 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:58:35,685 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:58:40,505 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:59:35,188 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:59:35,505 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:59:35,610 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:59:35,756 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 10:59:40,594 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 11:00:35,264 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 11:00:35,579 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 11:00:35,685 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 11:00:35,685 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 11:00:35,826 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 11:00:40,672 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 11:01:35,332 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 11:01:35,654 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 11:01:35,654 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 11:01:35,766 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 11:01:35,766 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 11:01:35,902 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 11:01:40,752 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 11:02:35,398 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 11:02:35,721 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 11:02:35,828 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 11:02:35,967 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 11:02:35,968 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 11:02:40,814 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 11:03:35,455 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 11:03:35,783 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 11:03:35,891 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 11:03:36,021 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 11:03:40,878 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 11:04:35,502 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 11:04:35,847 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 11:04:35,847 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 11:04:35,944 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 11:04:35,945 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 11:04:36,069 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 11:04:40,935 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 11:05:35,564 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 11:05:35,911 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 11:05:35,911 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 11:05:36,007 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 11:05:36,007 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 11:05:36,133 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 11:05:41,005 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 11:06:35,620 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 11:06:35,980 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 11:06:35,980 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 11:06:36,070 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 11:06:36,071 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 11:06:36,198 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 11:06:36,198 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 11:06:41,071 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 11:07:35,680 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 11:07:36,049 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 11:07:36,135 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 11:07:36,260 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 11:07:36,260 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 11:07:41,138 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 11:08:35,728 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 11:08:36,105 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 11:08:36,194 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 11:08:36,315 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 11:08:41,198 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 11:09:24,454 - __main__ - INFO - Received signal 2, initiating graceful shutdown...
2025-07-22 11:09:24,455 - __main__ - INFO - PayslipWorker-4 received shutdown signal
2025-07-22 11:09:24,455 - __main__ - INFO - PayslipWorker-5 received shutdown signal
2025-07-22 11:09:24,455 - __main__ - INFO - PayslipWorker-3 received shutdown signal
2025-07-22 11:09:24,455 - __main__ - INFO - PayslipWorker-2 received shutdown signal
2025-07-22 11:09:24,455 - __main__ - INFO - PayslipWorker-1 received shutdown signal
2025-07-22 11:09:24,455 - __main__ - INFO - PayslipWorker-4 stopped
2025-07-22 11:09:24,456 - __main__ - INFO - PayslipWorker-5 stopped
2025-07-22 11:09:24,456 - __main__ - INFO - PayslipWorker-3 stopped
2025-07-22 11:09:24,456 - __main__ - INFO - PayslipWorker-2 stopped
2025-07-22 11:09:24,456 - __main__ - INFO - PayslipWorker-1 stopped
2025-07-22 11:09:25,244 - __main__ - INFO - Initiating graceful shutdown...
2025-07-22 11:09:25,244 - __main__ - INFO - Waiting for current file processing to complete...
2025-07-22 11:09:25,244 - __main__ - INFO - Stopping worker threads...
2025-07-22 11:09:25,244 - __main__ - INFO - PayslipSender service stopped
2025-07-22 11:09:28,055 - email_service - INFO - Email service initialized for smtp.gmail.com:587
2025-07-22 11:09:28,056 - __main__ - INFO - PayslipSender initialized for production deployment
2025-07-22 11:09:28,056 - __main__ - INFO - Working directory: C:\Users\<USER>\Desktop\PayslipSender
2025-07-22 11:09:28,056 - __main__ - INFO - Watch directory: C:\Users\<USER>\Desktop\PayslipSender\payslips
2025-07-22 11:09:28,056 - __main__ - INFO - Archived directory: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips
2025-07-22 11:09:28,056 - __main__ - INFO - Starting PayslipSender service with parallel processing...
2025-07-22 11:09:30,231 - email_service - INFO - SMTP connection test successful
2025-07-22 11:09:30,234 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 11:09:30,292 - __main__ - INFO - Database connection validated
2025-07-22 11:09:30,293 - __main__ - INFO - Monitoring directory: C:\Users\<USER>\Desktop\PayslipSender\payslips
2025-07-22 11:09:30,293 - __main__ - INFO - Using 5 worker threads for parallel processing
2025-07-22 11:09:30,293 - __main__ - INFO - PayslipSender service is running
2025-07-22 11:09:30,293 - __main__ - INFO - Starting 5 worker threads for file processing
2025-07-22 11:09:30,294 - __main__ - INFO - PayslipWorker-1 started and ready for processing
2025-07-22 11:09:30,294 - __main__ - INFO - PayslipWorker-2 started and ready for processing
2025-07-22 11:09:30,295 - __main__ - INFO - PayslipWorker-3 started and ready for processing
2025-07-22 11:09:30,295 - __main__ - INFO - PayslipWorker-4 started and ready for processing
2025-07-22 11:09:30,295 - __main__ - INFO - PayslipWorker-5 started and ready for processing
2025-07-22 11:09:30,296 - __main__ - INFO - Found 2 new payslip files to process
2025-07-22 11:09:30,296 - __main__ - ERROR - File Payslip_513_20250625 - Copy.pdf: File validation failed: Invalid filename format (expected: Payslip_EmployeeID_YYYYMMDD.pdf)
2025-07-22 11:09:30,298 - __main__ - INFO - Moved problematic file to error folder: ERROR_20250722_110930_Payslip_513_20250625 - Copy.pdf
2025-07-22 11:09:30,301 - __main__ - INFO - Queuing file for processing: Payslip_513_20250625.pdf (ID: c3f7ce6f8f76)
2025-07-22 11:09:30,301 - __main__ - INFO - PayslipWorker-1 processing: Payslip_513_20250625.pdf (ID: c3f7ce6f8f76)
2025-07-22 11:09:30,301 - __main__ - INFO - Processing payslip for employee 513, date 20250625 (ID: c3f7ce6f8f76)
2025-07-22 11:09:30,304 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 11:09:30,307 - __main__ - ERROR - Employee ID 513 not found in database
2025-07-22 11:09:30,307 - __main__ - INFO - Attempting to log failed send for employee 513
2025-07-22 11:09:30,309 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 11:09:30,317 - __main__ - INFO - ✅ Successfully logged failed send to database (Record ID: 182) - Employee: 513, Reason: Employee ID 513 not found in database
2025-07-22 11:09:30,317 - __main__ - ERROR - PayslipWorker-1 failed to process: Payslip_513_20250625.pdf (ID: c3f7ce6f8f76)
2025-07-22 11:09:35,776 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:09:36,157 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:09:36,253 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:09:36,370 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:09:37,158 - __main__ - INFO - Found 1 new payslip files to process
2025-07-22 11:09:37,158 - __main__ - INFO - Queuing file for processing: Payslip_502_20250625.pdf
2025-07-22 11:09:37,158 - __main__ - INFO - PayslipWorker-4 processing: Payslip_502_20250625.pdf
2025-07-22 11:09:37,158 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-22 11:09:37,158 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-22 11:09:37,163 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 11:09:37,163 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 11:09:37,254 - __main__ - INFO - Found 1 new payslip files to process
2025-07-22 11:09:37,254 - __main__ - INFO - Queuing file for processing: Payslip_502_20250625.pdf
2025-07-22 11:09:37,254 - __main__ - INFO - Queuing file for processing: Payslip_502_20250625.pdf
2025-07-22 11:09:37,254 - __main__ - INFO - PayslipWorker-2 processing: Payslip_502_20250625.pdf
2025-07-22 11:09:37,254 - __main__ - INFO - PayslipWorker-5 processing: Payslip_502_20250625.pdf
2025-07-22 11:09:37,254 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-22 11:09:37,254 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-22 11:09:37,257 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 11:09:37,257 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 11:09:37,309 - __main__ - INFO - Found 1 new payslip files to process
2025-07-22 11:09:37,309 - __main__ - INFO - Queuing file for processing: Payslip_502_20250625.pdf (ID: beba2d0c02ca)
2025-07-22 11:09:37,309 - __main__ - INFO - PayslipWorker-2 processing: Payslip_502_20250625.pdf (ID: beba2d0c02ca)
2025-07-22 11:09:37,309 - __main__ - INFO - Processing payslip for employee 502, date 20250625 (ID: beba2d0c02ca)
2025-07-22 11:09:37,314 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 11:09:37,371 - __main__ - INFO - Found 1 new payslip files to process
2025-07-22 11:09:37,371 - __main__ - INFO - Queuing file for processing: Payslip_502_20250625.pdf
2025-07-22 11:09:37,371 - __main__ - INFO - Queuing file for processing: Payslip_502_20250625.pdf
2025-07-22 11:09:37,371 - __main__ - INFO - PayslipWorker-5 processing: Payslip_502_20250625.pdf
2025-07-22 11:09:37,371 - __main__ - INFO - PayslipWorker-4 processing: Payslip_502_20250625.pdf
2025-07-22 11:09:37,371 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-22 11:09:37,371 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-22 11:09:37,374 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 11:09:37,374 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 11:09:37,777 - __main__ - INFO - Found 1 new payslip files to process
2025-07-22 11:09:37,777 - __main__ - INFO - Queuing file for processing: Payslip_502_20250625.pdf
2025-07-22 11:09:37,777 - __main__ - INFO - PayslipWorker-2 processing: Payslip_502_20250625.pdf
2025-07-22 11:09:37,777 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-22 11:09:37,780 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 11:09:47,765 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-22 11:09:47,767 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-22 11:09:47,770 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_502_20250625_20250722_110947.pdf
2025-07-22 11:09:47,770 - __main__ - INFO - PayslipWorker-4 completed: Payslip_502_20250625.pdf
2025-07-22 11:09:48,344 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-22 11:09:48,345 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-22 11:09:48,346 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_502_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_502_20250625_20250722_110948.pdf'
2025-07-22 11:09:48,346 - __main__ - INFO - PayslipWorker-4 completed: Payslip_502_20250625.pdf
2025-07-22 11:09:48,373 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-22 11:09:48,374 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-22 11:09:48,375 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_502_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_502_20250625_20250722_110948.pdf'
2025-07-22 11:09:48,375 - __main__ - INFO - PayslipWorker-2 completed: Payslip_502_20250625.pdf
2025-07-22 11:09:49,241 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-22 11:09:49,241 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-22 11:09:49,242 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_502_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_502_20250625_20250722_110949.pdf'
2025-07-22 11:09:49,242 - __main__ - INFO - PayslipWorker-1 completed: Payslip_502_20250625.pdf
2025-07-22 11:09:49,247 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-22 11:09:49,247 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-22 11:09:49,248 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_502_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_502_20250625_20250722_110949.pdf'
2025-07-22 11:09:49,248 - __main__ - INFO - PayslipWorker-5 completed: Payslip_502_20250625.pdf
2025-07-22 11:09:50,015 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-22 11:09:50,016 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-22 11:09:50,016 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_502_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_502_20250625_20250722_110950.pdf'
2025-07-22 11:09:50,017 - __main__ - INFO - PayslipWorker-2 completed: Payslip_502_20250625.pdf
2025-07-22 11:09:50,879 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-22 11:09:50,879 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-22 11:09:50,881 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_502_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_502_20250625_20250722_110950.pdf'
2025-07-22 11:09:50,882 - __main__ - INFO - PayslipWorker-5 completed: Payslip_502_20250625.pdf
2025-07-22 11:09:52,131 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-22 11:09:52,132 - __main__ - INFO - ✅ Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-22 11:09:52,133 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_502_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_502_20250625_20250722_110952.pdf'
2025-07-22 11:09:52,134 - __main__ - INFO - PayslipWorker-2 completed successfully: Payslip_502_20250625.pdf (ID: beba2d0c02ca)
2025-07-22 11:10:30,380 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 11:10:30,380 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 11:10:35,840 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:10:36,226 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:10:36,229 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:10:36,311 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:10:36,320 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:10:36,432 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:10:36,441 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:11:30,435 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 11:11:30,435 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 11:11:35,894 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:11:36,299 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:11:36,299 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:11:36,364 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:11:36,373 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:11:36,480 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:11:36,524 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:12:30,494 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 11:12:30,494 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 11:12:35,946 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:12:36,359 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:12:36,364 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:12:36,424 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:12:36,432 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:12:36,541 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:12:36,585 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:13:30,557 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 11:13:30,557 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 11:13:36,011 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:13:36,418 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:13:36,420 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:13:36,483 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:13:36,490 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:13:36,600 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:13:36,644 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:14:30,610 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 11:14:30,610 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 11:14:36,057 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:14:36,484 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:14:36,529 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:14:36,536 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:14:36,649 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:14:36,692 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:15:30,655 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 11:15:30,655 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 11:15:36,100 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:15:36,538 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:15:36,538 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:15:36,576 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:15:36,581 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:15:36,696 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:15:36,740 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:16:30,719 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 11:16:30,719 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 11:16:36,163 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:16:36,604 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:16:36,604 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:16:36,636 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:16:36,646 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:16:36,754 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:16:36,806 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:17:30,798 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 11:17:30,798 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 11:17:36,233 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:17:36,680 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:17:36,702 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:17:36,719 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:17:36,824 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:17:36,873 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:18:30,876 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 11:18:30,876 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 11:18:36,294 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:18:36,758 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:18:36,769 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:18:36,785 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:18:36,899 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:18:36,946 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:19:30,954 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 11:19:30,954 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 11:19:36,365 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:19:36,828 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:19:36,832 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:19:36,846 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:19:36,968 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:19:37,016 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:20:31,027 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 11:20:31,027 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 11:20:36,443 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:20:36,898 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:20:36,898 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:20:36,911 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:20:37,033 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:20:37,084 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:21:31,104 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 11:21:31,104 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 11:21:36,508 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:21:36,980 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:21:36,980 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:21:36,980 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:21:36,980 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:21:37,101 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:21:37,150 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:22:31,187 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 11:22:31,187 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 11:22:36,580 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:22:37,052 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:22:37,068 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:22:37,068 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:22:37,068 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:22:37,168 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:22:37,221 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:23:31,269 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 11:23:31,269 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 11:23:36,649 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:23:37,121 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:23:37,134 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:23:37,143 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:23:37,143 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:23:37,244 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:23:37,299 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:24:31,350 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 11:24:31,350 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 11:24:36,719 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:24:37,200 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:24:37,214 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:24:37,229 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:24:37,313 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:24:37,374 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:25:31,419 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 11:25:31,419 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 11:25:36,785 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:25:37,282 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:25:37,295 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:25:37,309 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:25:37,382 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:25:37,442 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:26:31,513 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 11:26:31,513 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 11:26:36,852 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:26:37,355 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:26:37,367 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:26:37,388 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:26:37,458 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:26:37,519 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:27:31,591 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 11:27:31,591 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 11:27:36,928 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:27:37,427 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:27:37,439 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:27:37,471 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:27:37,471 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:27:37,532 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:27:37,598 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:28:31,674 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 11:28:31,674 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 11:28:37,003 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:28:37,502 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:28:37,507 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:28:37,553 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:28:37,553 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:28:37,611 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:28:37,674 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:29:31,757 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 11:29:31,757 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 11:29:37,072 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:29:37,580 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:29:37,627 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:29:37,674 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:29:37,744 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:30:31,829 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 11:30:31,829 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 11:30:37,141 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:30:37,651 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:30:37,651 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:30:37,699 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:30:37,745 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:30:37,810 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:31:31,907 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 11:31:31,907 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 11:31:37,224 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:31:37,738 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:31:37,777 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:31:37,778 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:31:37,811 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:31:37,888 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:32:31,994 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 11:32:31,994 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 11:32:37,300 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:32:37,821 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:32:37,861 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:32:37,885 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:32:37,959 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:33:32,074 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 11:33:32,074 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 11:33:37,377 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:33:37,898 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:33:37,898 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:33:37,944 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:33:37,944 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:33:37,960 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:33:38,036 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:34:32,159 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 11:34:32,159 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 11:34:37,458 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:34:37,977 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:34:38,031 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:34:38,040 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:34:38,117 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:35:32,250 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 11:35:32,250 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 11:35:37,537 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:35:38,057 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:35:38,057 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:35:38,113 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:35:38,113 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:35:38,113 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:35:38,191 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:36:32,335 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 11:36:32,335 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 11:36:37,606 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:36:38,135 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:36:38,135 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:36:38,197 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:36:38,204 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:36:38,270 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:37:32,432 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 11:37:32,432 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 11:37:37,682 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:37:38,219 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:37:38,219 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:37:38,272 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:37:38,284 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:37:38,284 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:37:38,338 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:38:32,511 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 11:38:32,511 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 11:38:37,748 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:38:38,301 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:38:38,349 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:38:38,369 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:38:38,410 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:39:32,594 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 11:39:32,594 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 11:39:37,820 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:39:38,381 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:39:38,421 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:39:38,443 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:39:38,443 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:39:38,488 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:40:32,677 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 11:40:32,677 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 11:40:37,890 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:40:38,462 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:40:38,462 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:40:38,500 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:40:38,542 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:40:38,545 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:40:38,558 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:41:32,762 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 11:41:32,762 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 11:41:37,965 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:41:38,545 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:41:38,575 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:41:38,626 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:41:38,637 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:42:32,845 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 11:42:32,845 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 11:42:38,035 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:42:38,635 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:42:38,653 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:42:38,717 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:42:38,717 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:43:32,929 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 11:43:32,929 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 11:43:38,105 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:43:38,719 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:43:38,719 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:43:38,723 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:43:38,811 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:43:38,814 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:43:38,814 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:44:33,015 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 11:44:33,015 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 11:44:38,177 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:44:38,814 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:44:38,814 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:44:38,814 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:44:38,890 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:44:38,895 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:45:33,093 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 11:45:33,093 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 11:45:38,246 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:45:38,905 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:45:38,905 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:45:38,905 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:45:38,958 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:45:38,970 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:46:33,168 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 11:46:33,168 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 11:46:38,315 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:46:38,993 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:46:38,993 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:46:39,029 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:46:39,050 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:46:39,050 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:47:33,253 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 11:47:33,253 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 11:47:38,389 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:47:39,091 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:47:39,091 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:47:39,103 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:47:39,122 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:48:33,331 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 11:48:33,331 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 11:48:38,463 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:48:39,182 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:48:39,183 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:48:39,183 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:48:39,183 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:48:39,194 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:48:39,194 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:49:33,401 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 11:49:33,401 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 11:49:38,539 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:49:39,277 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:49:39,277 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:49:39,277 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:49:39,280 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:50:33,479 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 11:50:33,479 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 11:50:38,613 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:50:39,351 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:50:39,361 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:50:39,370 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:50:39,370 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:50:39,370 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:51:33,556 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 11:51:33,556 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 11:51:38,683 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:51:39,427 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:51:39,443 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:51:39,443 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:51:39,465 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:51:39,465 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:52:33,638 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 11:52:33,638 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 11:52:38,754 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:52:39,500 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:52:39,524 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:52:39,561 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:52:39,561 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:52:39,561 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:53:33,716 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 11:53:33,716 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 11:53:38,821 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:53:39,580 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:53:39,608 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:53:39,648 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:53:39,648 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:53:39,648 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:54:33,802 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 11:54:33,802 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 11:54:38,904 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:54:39,660 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:54:39,689 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:54:39,742 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:54:39,743 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:54:39,743 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:55:33,876 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 11:55:33,876 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 11:55:38,973 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:55:39,740 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:55:39,773 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:55:39,826 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:55:39,826 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:55:39,826 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:56:33,965 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 11:56:33,967 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 11:56:39,046 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:56:39,816 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:56:39,844 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:56:39,844 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:56:39,927 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:56:39,927 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:57:34,054 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 11:57:34,054 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 11:57:39,121 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:57:39,887 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:57:39,928 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:57:40,017 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:57:40,018 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:57:40,018 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:58:34,138 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 11:58:34,138 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 11:58:39,192 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:58:39,971 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:58:40,004 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:58:40,004 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:58:40,112 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:58:40,112 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:59:34,221 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 11:59:34,221 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 11:59:39,266 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:59:40,040 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:59:40,082 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:59:40,200 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 11:59:40,200 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:00:34,296 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 12:00:34,296 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 12:00:39,345 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:00:40,121 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:00:40,159 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:00:40,299 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:00:40,299 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:00:40,299 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:01:34,383 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 12:01:34,383 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 12:01:39,420 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:01:40,198 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:01:40,244 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:01:40,245 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:01:40,392 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:01:40,392 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:01:40,392 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:02:34,474 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 12:02:34,474 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 12:02:39,490 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:02:40,266 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:02:40,322 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:02:40,481 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:02:40,481 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:02:40,484 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:03:34,573 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 12:03:34,573 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 12:03:39,582 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:03:40,343 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:03:40,399 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:03:40,562 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:03:40,571 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:03:40,584 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:04:34,664 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 12:04:34,664 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 12:04:39,675 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:04:40,416 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:04:40,472 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:04:40,472 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:04:40,633 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:04:40,649 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:04:40,677 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:05:34,754 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 12:05:34,754 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 12:05:39,763 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:05:40,495 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:05:40,546 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:05:40,703 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:05:40,717 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:05:40,764 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:06:34,834 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 12:06:34,834 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 12:06:39,839 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:06:40,563 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:06:40,626 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:06:40,626 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:06:40,776 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:06:40,794 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:06:40,842 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:07:34,925 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 12:07:34,925 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 12:07:39,931 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:07:40,640 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:07:40,708 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:07:40,848 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:07:40,867 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:07:40,932 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:08:35,009 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 12:08:35,009 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 12:08:40,016 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:08:40,718 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:08:40,786 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:08:40,926 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:08:40,946 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:08:41,018 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:09:35,109 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 12:09:35,109 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 12:09:40,115 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:09:40,793 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:09:40,863 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:09:41,004 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:09:41,022 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:09:41,116 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:10:35,192 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 12:10:35,192 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 12:10:40,197 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:10:40,862 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:10:40,934 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:10:41,080 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:10:41,093 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:10:41,200 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:11:35,277 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 12:11:35,277 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 12:11:40,278 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:11:40,937 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:11:41,010 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:11:41,155 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:11:41,170 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:11:41,285 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:12:35,367 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 12:12:35,367 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 12:12:40,352 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:12:41,015 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:12:41,095 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:12:41,236 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:12:41,248 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:12:41,377 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:13:35,458 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 12:13:35,458 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 12:13:40,429 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:13:41,086 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:13:41,182 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:13:41,304 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:13:41,313 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:13:41,464 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:14:35,542 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 12:14:35,542 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 12:14:40,507 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:14:41,173 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:14:41,269 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:14:41,386 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:14:41,393 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:14:41,551 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:15:35,630 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 12:15:35,630 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 12:15:40,585 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:15:41,249 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:15:41,350 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:15:41,350 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:15:41,458 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:15:41,467 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:15:41,640 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:16:35,713 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 12:16:35,713 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 12:16:40,657 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:16:41,322 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:16:41,434 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:16:41,434 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:16:41,535 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:16:41,541 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:16:41,721 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:17:35,797 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 12:17:35,797 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 12:17:40,727 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:17:41,394 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:17:41,504 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:17:41,609 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:17:41,613 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:17:41,804 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:18:35,871 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 12:18:35,871 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 12:18:40,794 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:18:41,463 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:18:41,579 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:18:41,579 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:18:41,685 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:18:41,685 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:18:41,879 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:19:35,954 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 12:19:35,954 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 12:19:40,864 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:19:41,532 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:19:41,654 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:19:41,654 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:19:41,758 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:19:41,964 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:20:36,046 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 12:20:36,046 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 12:20:40,938 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:20:41,613 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:20:41,737 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:20:41,737 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:20:41,840 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:20:41,840 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:20:42,056 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:21:36,129 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 12:21:36,129 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 12:21:41,012 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:21:41,694 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:21:41,819 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:21:41,919 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:21:42,136 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:22:36,195 - __main__ - INFO - Status: 0 queued, 0 processing, 5/5 workers, 1 completed
2025-07-22 12:22:36,195 - __main__ - INFO - Stats: 1 sent, 1 failed, 1 errors, 11 duplicates prevented
2025-07-22 12:22:41,073 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:22:41,754 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:22:41,883 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:22:41,883 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:22:41,989 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:22:42,202 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:23:08,032 - __main__ - INFO - Received signal 2, initiating graceful shutdown...
2025-07-22 12:23:08,032 - __main__ - INFO - PayslipWorker-2 received shutdown signal
2025-07-22 12:23:08,032 - __main__ - INFO - PayslipWorker-4 received shutdown signal
2025-07-22 12:23:08,038 - __main__ - INFO - PayslipWorker-3 received shutdown signal
2025-07-22 12:23:08,038 - __main__ - INFO - PayslipWorker-5 received shutdown signal
2025-07-22 12:23:08,038 - __main__ - INFO - PayslipWorker-1 received shutdown signal
2025-07-22 12:23:08,038 - __main__ - INFO - PayslipWorker-2 stopped
2025-07-22 12:23:08,038 - __main__ - INFO - PayslipWorker-4 stopped
2025-07-22 12:23:08,038 - __main__ - INFO - PayslipWorker-3 stopped
2025-07-22 12:23:08,038 - __main__ - INFO - PayslipWorker-5 stopped
2025-07-22 12:23:08,038 - __main__ - INFO - PayslipWorker-1 stopped
2025-07-22 12:23:08,234 - __main__ - INFO - Initiating graceful shutdown...
2025-07-22 12:23:08,234 - __main__ - INFO - Waiting for current file processing to complete...
2025-07-22 12:23:08,234 - __main__ - INFO - Stopping worker threads...
2025-07-22 12:23:08,234 - __main__ - INFO - === PayslipSender Processing Statistics ===
2025-07-22 12:23:08,234 - __main__ - INFO - Total files processed: 2
2025-07-22 12:23:08,236 - __main__ - INFO - Successful sends: 1
2025-07-22 12:23:08,236 - __main__ - INFO - Failed sends: 1
2025-07-22 12:23:08,236 - __main__ - INFO - Errors encountered: 1
2025-07-22 12:23:08,236 - __main__ - INFO - Duplicates prevented: 11
2025-07-22 12:23:08,236 - __main__ - INFO - Success rate: 50.0%
2025-07-22 12:23:08,236 - __main__ - INFO - ============================================
2025-07-22 12:23:08,236 - __main__ - INFO - PayslipSender service stopped
2025-07-22 12:23:41,128 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:23:41,812 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:23:41,942 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:23:42,050 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:23:42,262 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:24:41,176 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:24:41,864 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:24:41,995 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:24:41,995 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:24:42,105 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:24:42,310 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:24:48,928 - email_service - INFO - Email service initialized for smtp.gmail.com:587
2025-07-22 12:24:48,928 - __main__ - INFO - PayslipSender initialized for production deployment
2025-07-22 12:24:48,928 - __main__ - INFO - Working directory: C:\Users\<USER>\Desktop\PayslipSender\dist
2025-07-22 12:24:48,928 - __main__ - INFO - Watch directory: C:\Users\<USER>\Desktop\PayslipSender\payslips
2025-07-22 12:24:48,928 - __main__ - INFO - Archived directory: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips
2025-07-22 12:24:48,928 - __main__ - INFO - Starting PayslipSender service...
2025-07-22 12:24:50,659 - email_service - INFO - SMTP connection test successful
2025-07-22 12:24:50,666 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 12:24:50,744 - __main__ - INFO - Database connection validated
2025-07-22 12:24:50,744 - __main__ - INFO - Monitoring directory: C:\Users\<USER>\Desktop\PayslipSender\payslips
2025-07-22 12:24:50,744 - __main__ - INFO - PayslipSender service is running
2025-07-22 12:24:50,744 - __main__ - INFO - Found 1 new payslip files to process
2025-07-22 12:24:50,744 - __main__ - INFO - New payslip file detected: Payslip_513_20250625.pdf
2025-07-22 12:24:50,744 - __main__ - INFO - Processing payslip for employee 513, date 20250625
2025-07-22 12:24:50,748 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 12:24:50,750 - __main__ - ERROR - Employee ID 513 not found in database
2025-07-22 12:24:50,754 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 12:24:50,760 - __main__ - WARNING - Logged failed send for employee 513: Employee ID 513 not found in database
2025-07-22 12:25:33,056 - __main__ - INFO - Found 1 new payslip files to process
2025-07-22 12:25:33,056 - __main__ - INFO - Queuing file for processing: Payslip_502_20250625.pdf
2025-07-22 12:25:33,056 - __main__ - INFO - Queuing file for processing: Payslip_502_20250625.pdf
2025-07-22 12:25:33,056 - __main__ - INFO - PayslipWorker-2 processing: Payslip_502_20250625.pdf
2025-07-22 12:25:33,056 - __main__ - INFO - PayslipWorker-4 processing: Payslip_502_20250625.pdf
2025-07-22 12:25:33,056 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-22 12:25:33,056 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-22 12:25:33,056 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 12:25:33,056 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 12:25:33,159 - __main__ - INFO - Found 1 new payslip files to process
2025-07-22 12:25:33,159 - __main__ - INFO - Queuing file for processing: Payslip_502_20250625.pdf
2025-07-22 12:25:33,159 - __main__ - INFO - Queuing file for processing: Payslip_502_20250625.pdf
2025-07-22 12:25:33,159 - __main__ - INFO - PayslipWorker-4 processing: Payslip_502_20250625.pdf
2025-07-22 12:25:33,159 - __main__ - INFO - PayslipWorker-4 processing: Payslip_502_20250625.pdf
2025-07-22 12:25:33,159 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-22 12:25:33,159 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-22 12:25:33,159 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 12:25:33,159 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 12:25:33,234 - __main__ - INFO - Found 1 new payslip files to process
2025-07-22 12:25:33,234 - __main__ - INFO - Queuing file for processing: Payslip_502_20250625.pdf
2025-07-22 12:25:33,234 - __main__ - INFO - PayslipWorker-2 processing: Payslip_502_20250625.pdf
2025-07-22 12:25:33,234 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-22 12:25:33,234 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 12:25:33,394 - __main__ - INFO - Found 1 new payslip files to process
2025-07-22 12:25:33,394 - __main__ - INFO - Queuing file for processing: Payslip_502_20250625.pdf
2025-07-22 12:25:33,394 - __main__ - INFO - PayslipWorker-1 processing: Payslip_502_20250625.pdf
2025-07-22 12:25:33,394 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-22 12:25:33,396 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 12:25:33,815 - __main__ - INFO - Found 1 new payslip files to process
2025-07-22 12:25:33,815 - __main__ - INFO - New payslip file detected: Payslip_502_20250625.pdf
2025-07-22 12:25:33,815 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-22 12:25:33,818 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 12:25:33,919 - __main__ - INFO - Found 1 new payslip files to process
2025-07-22 12:25:33,919 - __main__ - INFO - Queuing file for processing: Payslip_502_20250625.pdf
2025-07-22 12:25:33,919 - __main__ - INFO - PayslipWorker-5 processing: Payslip_502_20250625.pdf
2025-07-22 12:25:33,919 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-22 12:25:33,919 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 12:25:41,241 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 12:25:41,927 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 12:25:42,067 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 12:25:42,067 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 12:25:42,169 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 12:25:42,403 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 12:25:42,576 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-22 12:25:42,576 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-22 12:25:42,576 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_502_20250625_20250722_122542.pdf
2025-07-22 12:25:42,576 - __main__ - INFO - PayslipWorker-4 completed: Payslip_502_20250625.pdf
2025-07-22 12:25:43,936 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-22 12:25:43,936 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-22 12:25:43,936 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_502_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_502_20250625_20250722_122543.pdf'
2025-07-22 12:25:43,936 - __main__ - INFO - PayslipWorker-2 completed: Payslip_502_20250625.pdf
2025-07-22 12:25:44,349 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-22 12:25:44,349 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-22 12:25:44,349 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_502_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_502_20250625_20250722_122544.pdf'
2025-07-22 12:25:44,349 - __main__ - INFO - PayslipWorker-4 completed: Payslip_502_20250625.pdf
2025-07-22 12:25:44,850 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-22 12:25:44,850 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-22 12:25:44,850 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_502_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_502_20250625_20250722_122544.pdf'
2025-07-22 12:25:44,850 - __main__ - INFO - PayslipWorker-4 completed: Payslip_502_20250625.pdf
2025-07-22 12:25:45,247 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-22 12:25:45,247 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-22 12:25:45,247 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_502_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_502_20250625_20250722_122545.pdf'
2025-07-22 12:25:45,247 - __main__ - INFO - PayslipWorker-2 completed: Payslip_502_20250625.pdf
2025-07-22 12:25:45,954 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-22 12:25:45,954 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-22 12:25:45,954 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_502_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_502_20250625_20250722_122545.pdf'
2025-07-22 12:25:46,865 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-22 12:25:46,865 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-22 12:25:46,872 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_502_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_502_20250625_20250722_122546.pdf'
2025-07-22 12:25:46,872 - __main__ - INFO - PayslipWorker-1 completed: Payslip_502_20250625.pdf
2025-07-22 12:25:47,824 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-22 12:25:47,824 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-22 12:25:47,824 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_502_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_502_20250625_20250722_122547.pdf'
2025-07-22 12:25:47,824 - __main__ - INFO - PayslipWorker-5 completed: Payslip_502_20250625.pdf
2025-07-22 12:26:41,297 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:26:41,972 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:26:42,121 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:26:42,223 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:26:42,465 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:27:41,349 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:27:42,017 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:27:42,167 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:27:42,167 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:27:42,272 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:27:42,517 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:28:41,392 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:28:42,062 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:28:42,229 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:28:42,328 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:28:42,562 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:29:41,439 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:29:42,103 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:29:42,282 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:29:42,372 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:29:42,383 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:29:42,617 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:30:41,488 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:30:42,157 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:30:42,332 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:30:42,413 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:30:42,432 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:30:42,666 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:31:41,532 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:31:42,199 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:31:42,382 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:31:42,456 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:31:42,482 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:31:42,717 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:32:41,580 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:32:42,259 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:32:42,431 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:32:42,497 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:32:42,528 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:32:42,763 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:33:41,633 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:33:42,314 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:33:42,486 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:33:42,547 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:33:42,582 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:33:42,816 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:34:41,683 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:34:42,369 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:34:42,536 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:34:42,600 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:34:42,633 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:34:42,866 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:35:41,728 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:35:42,425 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:35:42,596 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:35:42,596 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:35:42,648 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:35:42,679 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:35:42,925 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:36:41,779 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:36:42,479 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:36:42,653 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:36:42,653 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:36:42,694 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:36:42,746 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:36:42,979 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:37:41,829 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:37:42,524 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:37:42,709 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:37:42,710 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:37:42,745 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:37:42,793 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:37:43,030 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:38:41,891 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:38:42,580 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:38:42,756 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:38:42,760 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:38:42,791 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:38:42,846 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:38:43,094 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:39:41,941 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:39:42,633 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:39:42,805 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:39:42,807 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:39:42,840 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:39:42,906 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:39:43,147 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:40:42,010 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:40:42,694 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:40:42,865 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:40:42,866 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:40:42,901 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:40:42,957 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:40:43,211 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:41:42,066 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:41:42,745 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:41:42,927 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:41:42,927 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:41:42,960 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:41:43,009 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:41:43,267 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:42:42,130 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:42:42,798 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:42:42,983 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:42:43,009 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:42:43,067 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:42:43,330 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:43:42,176 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:43:42,845 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:43:43,048 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:43:43,057 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:43:43,118 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:43:43,385 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:44:42,230 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:44:42,895 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:44:43,108 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:44:43,108 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:44:43,171 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:44:43,436 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:45:42,282 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:45:42,952 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:45:43,165 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:45:43,179 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:45:43,184 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:45:43,225 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:45:43,490 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:46:42,339 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:46:43,019 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:46:43,219 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:46:43,230 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:46:43,244 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:46:43,277 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:46:43,548 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:47:42,390 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:47:43,077 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:47:43,286 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:47:43,288 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:47:43,292 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:47:43,326 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:47:43,604 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:48:42,442 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:48:43,136 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:48:43,343 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:48:43,378 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:48:43,659 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:49:42,492 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:49:43,198 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:49:43,405 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:49:43,405 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:49:43,428 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:49:43,717 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:50:42,552 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:50:43,255 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:50:43,480 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:50:43,480 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:50:43,487 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:50:43,771 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:51:42,618 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:51:43,326 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:51:43,568 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:51:43,568 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:51:43,568 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:51:43,839 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:52:42,690 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:52:43,396 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:52:43,654 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:52:43,669 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:52:43,909 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:53:42,769 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:53:43,475 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:53:43,736 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:53:43,763 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:53:43,772 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:53:43,982 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:54:42,857 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:54:43,541 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:54:43,806 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:54:43,833 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:54:43,859 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:54:43,859 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:54:44,054 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:55:42,917 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:55:43,597 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:55:43,856 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:55:43,887 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:55:43,918 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:55:44,104 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:56:18,842 - email_service - INFO - Email service initialized for smtp.gmail.com:587
2025-07-22 12:56:18,848 - __main__ - INFO - PayslipSender initialized for production deployment
2025-07-22 12:56:18,848 - __main__ - INFO - Working directory: C:\Users\<USER>\Desktop\PayslipSender\dist
2025-07-22 12:56:18,848 - __main__ - INFO - Watch directory: C:\Users\<USER>\Desktop\PayslipSender\payslips
2025-07-22 12:56:18,848 - __main__ - INFO - Archived directory: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips
2025-07-22 12:56:18,848 - __main__ - INFO - Starting PayslipSender service...
2025-07-22 12:56:20,988 - email_service - INFO - SMTP connection test successful
2025-07-22 12:56:20,994 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 12:56:21,074 - __main__ - INFO - Database connection validated
2025-07-22 12:56:21,074 - __main__ - INFO - Monitoring directory: C:\Users\<USER>\Desktop\PayslipSender\payslips
2025-07-22 12:56:21,074 - __main__ - INFO - PayslipSender service is running
2025-07-22 12:56:21,074 - __main__ - INFO - Found 1 new payslip files to process
2025-07-22 12:56:21,074 - __main__ - INFO - New payslip file detected: Payslip_513_20250625.pdf
2025-07-22 12:56:21,074 - __main__ - INFO - Processing payslip for employee 513, date 20250625
2025-07-22 12:56:21,080 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 12:56:21,081 - __main__ - ERROR - Employee ID 513 not found in database
2025-07-22 12:56:21,084 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 12:56:21,090 - __main__ - WARNING - Logged failed send for employee 513: Employee ID 513 not found in database
2025-07-22 12:56:42,993 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:56:43,648 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:56:43,911 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:56:43,946 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:56:43,994 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:56:43,994 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:56:44,157 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:57:43,051 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:57:43,694 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:57:43,957 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:57:43,991 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:57:44,052 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:57:44,052 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:57:44,200 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:58:43,106 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:58:43,739 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:58:44,003 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:58:44,034 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:58:44,107 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:58:44,107 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:58:44,246 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 12:59:38,045 - __main__ - INFO - Found 1 new payslip files to process
2025-07-22 12:59:38,045 - __main__ - INFO - Queuing file for processing: Payslip_502_20250625.pdf
2025-07-22 12:59:38,045 - __main__ - INFO - PayslipWorker-2 processing: Payslip_502_20250625.pdf
2025-07-22 12:59:38,045 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-22 12:59:38,045 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 12:59:38,079 - __main__ - INFO - Found 1 new payslip files to process
2025-07-22 12:59:38,079 - __main__ - INFO - Queuing file for processing: Payslip_502_20250625.pdf
2025-07-22 12:59:38,079 - __main__ - INFO - PayslipWorker-5 processing: Payslip_502_20250625.pdf
2025-07-22 12:59:38,079 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-22 12:59:38,082 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 12:59:38,160 - __main__ - INFO - Found 1 new payslip files to process
2025-07-22 12:59:38,160 - __main__ - INFO - Found 1 new payslip files to process
2025-07-22 12:59:38,161 - __main__ - INFO - Queuing file for processing: Payslip_502_20250625.pdf
2025-07-22 12:59:38,161 - __main__ - INFO - Queuing file for processing: Payslip_502_20250625.pdf
2025-07-22 12:59:38,161 - __main__ - INFO - PayslipWorker-2 processing: Payslip_502_20250625.pdf
2025-07-22 12:59:38,161 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-22 12:59:38,161 - __main__ - INFO - PayslipWorker-4 processing: Payslip_502_20250625.pdf
2025-07-22 12:59:38,161 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-22 12:59:38,161 - __main__ - INFO - Found 1 new payslip files to process
2025-07-22 12:59:38,161 - __main__ - INFO - Queuing file for processing: Payslip_502_20250625.pdf
2025-07-22 12:59:38,161 - __main__ - INFO - PayslipWorker-1 processing: Payslip_502_20250625.pdf
2025-07-22 12:59:38,161 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-22 12:59:38,162 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 12:59:38,162 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 12:59:38,162 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 12:59:38,248 - __main__ - INFO - Found 1 new payslip files to process
2025-07-22 12:59:38,248 - __main__ - INFO - New payslip file detected: Payslip_502_20250625.pdf
2025-07-22 12:59:38,248 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-22 12:59:38,248 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 12:59:38,281 - __main__ - INFO - Found 1 new payslip files to process
2025-07-22 12:59:38,281 - __main__ - INFO - Queuing file for processing: Payslip_502_20250625.pdf
2025-07-22 12:59:38,281 - __main__ - INFO - PayslipWorker-5 processing: Payslip_502_20250625.pdf
2025-07-22 12:59:38,281 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-22 12:59:38,281 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 12:59:38,785 - __main__ - INFO - Found 1 new payslip files to process
2025-07-22 12:59:38,785 - __main__ - INFO - Queuing file for processing: Payslip_502_20250625.pdf
2025-07-22 12:59:38,785 - __main__ - INFO - PayslipWorker-1 processing: Payslip_502_20250625.pdf
2025-07-22 12:59:38,785 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-22 12:59:38,785 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 12:59:43,167 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 12:59:43,789 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 12:59:44,049 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 12:59:44,086 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 12:59:44,169 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 12:59:44,169 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 12:59:44,288 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 2 processed files
2025-07-22 12:59:48,045 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-22 12:59:48,045 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-22 12:59:48,047 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_502_20250625_20250722_125948.pdf
2025-07-22 12:59:48,047 - __main__ - INFO - PayslipWorker-1 completed: Payslip_502_20250625.pdf
2025-07-22 12:59:48,717 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-22 12:59:48,717 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-22 12:59:48,717 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_502_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_502_20250625_20250722_125948.pdf'
2025-07-22 12:59:48,717 - __main__ - INFO - PayslipWorker-2 completed: Payslip_502_20250625.pdf
2025-07-22 12:59:49,358 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-22 12:59:49,358 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-22 12:59:49,358 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_502_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_502_20250625_20250722_125949.pdf'
2025-07-22 12:59:49,358 - __main__ - INFO - PayslipWorker-5 completed: Payslip_502_20250625.pdf
2025-07-22 12:59:49,854 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-22 12:59:49,854 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-22 12:59:49,854 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_502_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_502_20250625_20250722_125949.pdf'
2025-07-22 12:59:49,854 - __main__ - INFO - PayslipWorker-4 completed: Payslip_502_20250625.pdf
2025-07-22 12:59:51,006 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-22 12:59:51,006 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-22 12:59:51,006 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_502_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_502_20250625_20250722_125951.pdf'
2025-07-22 12:59:51,006 - __main__ - INFO - PayslipWorker-1 completed: Payslip_502_20250625.pdf
2025-07-22 12:59:51,736 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-22 12:59:51,736 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-22 12:59:51,736 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_502_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_502_20250625_20250722_125951.pdf'
2025-07-22 12:59:51,736 - __main__ - INFO - PayslipWorker-2 completed: Payslip_502_20250625.pdf
2025-07-22 12:59:51,924 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-22 12:59:51,924 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-22 12:59:51,924 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_502_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_502_20250625_20250722_125951.pdf'
2025-07-22 12:59:51,924 - __main__ - INFO - PayslipWorker-5 completed: Payslip_502_20250625.pdf
2025-07-22 12:59:53,106 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-22 12:59:53,106 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-22 12:59:53,106 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_502_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_502_20250625_20250722_125953.pdf'
2025-07-22 13:00:43,241 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 13:00:43,846 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 13:00:44,111 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 13:00:44,138 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 13:00:44,242 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 13:00:44,347 - __main__ - INFO - Status: 0 files in queue, 5/5 active workers, 1 processed files
2025-07-22 13:00:52,851 - __main__ - INFO - Found 1 new payslip files to process
2025-07-22 13:00:52,851 - __main__ - INFO - Queuing file for processing: Payslip_502_20250625.pdf
2025-07-22 13:00:52,851 - __main__ - INFO - PayslipWorker-4 processing: Payslip_502_20250625.pdf
2025-07-22 13:00:52,851 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-22 13:00:52,851 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 13:00:53,122 - __main__ - INFO - Found 1 new payslip files to process
2025-07-22 13:00:53,122 - __main__ - INFO - Queuing file for processing: Payslip_502_20250625.pdf
2025-07-22 13:00:53,122 - __main__ - INFO - PayslipWorker-3 processing: Payslip_502_20250625.pdf
2025-07-22 13:00:53,122 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-22 13:00:53,122 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 13:00:53,147 - __main__ - INFO - Found 1 new payslip files to process
2025-07-22 13:00:53,147 - __main__ - INFO - Queuing file for processing: Payslip_502_20250625.pdf
2025-07-22 13:00:53,147 - __main__ - INFO - PayslipWorker-3 processing: Payslip_502_20250625.pdf
2025-07-22 13:00:53,147 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-22 13:00:53,150 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 13:00:53,254 - __main__ - INFO - Found 1 new payslip files to process
2025-07-22 13:00:53,254 - __main__ - INFO - Queuing file for processing: Payslip_502_20250625.pdf
2025-07-22 13:00:53,254 - __main__ - INFO - Found 1 new payslip files to process
2025-07-22 13:00:53,254 - __main__ - INFO - Found 1 new payslip files to process
2025-07-22 13:00:53,254 - __main__ - INFO - Queuing file for processing: Payslip_502_20250625.pdf
2025-07-22 13:00:53,254 - __main__ - INFO - PayslipWorker-1 processing: Payslip_502_20250625.pdf
2025-07-22 13:00:53,254 - __main__ - INFO - Queuing file for processing: Payslip_502_20250625.pdf
2025-07-22 13:00:53,254 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-22 13:00:53,254 - __main__ - INFO - PayslipWorker-5 processing: Payslip_502_20250625.pdf
2025-07-22 13:00:53,254 - __main__ - INFO - PayslipWorker-5 processing: Payslip_502_20250625.pdf
2025-07-22 13:00:53,254 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-22 13:00:53,254 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-22 13:00:53,254 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 13:00:53,254 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 13:00:53,260 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 13:00:53,355 - __main__ - INFO - Found 1 new payslip files to process
2025-07-22 13:00:53,355 - __main__ - INFO - Queuing file for processing: Payslip_502_20250625.pdf
2025-07-22 13:00:53,355 - __main__ - INFO - PayslipWorker-1 processing: Payslip_502_20250625.pdf
2025-07-22 13:00:53,355 - __main__ - INFO - Processing payslip for employee 502, date 20250625
2025-07-22 13:00:53,355 - __main__ - INFO - Using ODBC driver: ODBC Driver 18 for SQL Server
2025-07-22 13:01:02,269 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-22 13:01:02,269 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-22 13:01:02,269 - __main__ - INFO - Moved processed file to: C:\Users\<USER>\Desktop\PayslipSender\archived_payslips\Payslip_502_20250625_20250722_130102.pdf
2025-07-22 13:01:02,269 - __main__ - INFO - PayslipWorker-4 completed: Payslip_502_20250625.pdf
2025-07-22 13:01:03,061 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-22 13:01:03,061 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-22 13:01:03,061 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_502_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_502_20250625_20250722_130103.pdf'
2025-07-22 13:01:03,061 - __main__ - INFO - PayslipWorker-1 completed: Payslip_502_20250625.pdf
2025-07-22 13:01:03,715 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-22 13:01:03,715 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-22 13:01:03,715 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_502_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_502_20250625_20250722_130103.pdf'
2025-07-22 13:01:03,715 - __main__ - INFO - PayslipWorker-3 completed: Payslip_502_20250625.pdf
2025-07-22 13:01:04,504 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-22 13:01:04,504 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-22 13:01:04,504 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_502_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_502_20250625_20250722_130104.pdf'
2025-07-22 13:01:04,504 - __main__ - INFO - PayslipWorker-3 completed: Payslip_502_20250625.pdf
2025-07-22 13:01:05,905 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-22 13:01:05,905 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-22 13:01:05,905 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_502_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_502_20250625_20250722_130105.pdf'
2025-07-22 13:01:05,905 - __main__ - INFO - PayslipWorker-5 completed: Payslip_502_20250625.pdf
2025-07-22 13:01:06,800 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-22 13:01:06,800 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-22 13:01:06,800 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_502_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_502_20250625_20250722_130106.pdf'
2025-07-22 13:01:06,800 - __main__ - INFO - PayslipWorker-1 completed: Payslip_502_20250625.pdf
2025-07-22 13:01:10,512 - email_service - INFO - Email sent <NAME_EMAIL> (Test 1)
2025-07-22 13:01:10,512 - __main__ - INFO - Successfully sent payslip to Test 1 (<EMAIL>)
2025-07-22 13:01:10,512 - __main__ - ERROR - Error moving file to archived directory: [WinError 2] The system cannot find the file specified: 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\payslips\\Payslip_502_20250625.pdf' -> 'C:\\Users\\<USER>\\Desktop\\PayslipSender\\archived_payslips\\Payslip_502_20250625_20250722_130110.pdf'
2025-07-22 13:01:10,512 - __main__ - INFO - PayslipWorker-5 completed: Payslip_502_20250625.pdf
