@echo off
REM ============================================================================
REM Simple PayslipSender Service Registration (No NSSM)
REM ============================================================================
REM This script registers PayslipSender as a Windows service using SC command
REM with optimized settings to avoid Error 1053
REM Run as Administrator
REM ============================================================================

echo.
echo ========================================
echo PayslipSender Service Registration
echo ========================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script must be run as Administrator!
    echo Right-click and select "Run as administrator"
    echo.
    pause
    exit /b 1
)

REM Set variables
set SERVICE_NAME=DSCPayslipSender
set SERVICE_DISPLAY_NAME=DSC PayslipSender Service
set CURRENT_DIR=%~dp0
set EXECUTABLE_PATH=%CURRENT_DIR%dist\PayslipSender.exe

echo Service Name: %SERVICE_NAME%
echo Display Name: %SERVICE_DISPLAY_NAME%
echo Executable: %EXECUTABLE_PATH%
echo.

REM Check if executable exists
if not exist "%EXECUTABLE_PATH%" (
    echo ERROR: PayslipSender.exe not found at: %EXECUTABLE_PATH%
    echo Please run 'python create_exe.py' first
    echo.
    pause
    exit /b 1
)

REM Remove existing service if it exists
echo Checking for existing service...
sc query "%SERVICE_NAME%" >nul 2>&1
if %errorLevel% equ 0 (
    echo Existing service found. Stopping and removing...
    sc stop "%SERVICE_NAME%" >nul 2>&1
    timeout /t 5 /nobreak >nul
    sc delete "%SERVICE_NAME%" >nul 2>&1
    timeout /t 3 /nobreak >nul
    echo Existing service removed.
)

REM Create logs directory
if not exist "%CURRENT_DIR%logs" (
    mkdir "%CURRENT_DIR%logs"
    echo Created logs directory
)

echo.
echo Registering PayslipSender as Windows service...

REM Register the service with manual start (to avoid timeout issues)
sc create "%SERVICE_NAME%" ^
    binPath= "\"%EXECUTABLE_PATH%\"" ^
    DisplayName= "%SERVICE_DISPLAY_NAME%" ^
    start= demand ^
    type= own

if %errorLevel% neq 0 (
    echo ERROR: Failed to create service
    pause
    exit /b 1
)

REM Set service description
sc description "%SERVICE_NAME%" "Automated payslip email sender for DSC Human Resources. Monitors payslips folder and sends emails automatically."

REM Configure service to restart on failure
sc failure "%SERVICE_NAME%" reset= 86400 actions= restart/60000/restart/120000/restart/300000

REM Change to automatic start after registration
echo Setting service to automatic startup...
sc config "%SERVICE_NAME%" start= auto

echo.
echo ✅ Service registered successfully!
echo.
echo Service Details:
echo - Name: %SERVICE_NAME%
echo - Display Name: %SERVICE_DISPLAY_NAME%
echo - Executable: %EXECUTABLE_PATH%
echo - Startup: Automatic (delayed)
echo - Recovery: Restart on failure
echo.

REM Set service to delayed auto start to avoid startup conflicts
echo Configuring delayed automatic start...
sc config "%SERVICE_NAME%" start= delayed-auto

echo.
echo ⚠️  IMPORTANT: Service configured with delayed automatic start
echo This helps avoid Error 1053 by starting after other services are ready.
echo.

REM Ask if user wants to start the service now
set /p START_NOW="Do you want to start the service now? (Y/N): "
if /i "%START_NOW%"=="Y" (
    echo.
    echo Starting PayslipSender service...
    sc start "%SERVICE_NAME%"
    
    if %errorLevel% equ 0 (
        echo.
        echo ✅ Service started successfully!
        echo.
        echo The service is now running and will start automatically on boot (delayed).
        echo.
        echo Monitor the service:
        echo - Application logs: %CURRENT_DIR%logs\payslip_sender.log
        echo - Windows Event Viewer: Services section
        echo.
    ) else (
        echo.
        echo ❌ Failed to start service
        echo This might be due to configuration issues.
        echo.
        echo Troubleshooting steps:
        echo 1. Check .env file configuration
        echo 2. Test executable manually: %EXECUTABLE_PATH%
        echo 3. Check Windows Event Viewer for details
        echo 4. Try starting manually: sc start "%SERVICE_NAME%"
    )
) else (
    echo.
    echo Service registered but not started.
    echo To start: sc start "%SERVICE_NAME%"
)

echo.
echo ========================================
echo Registration Complete!
echo ========================================
echo.
echo Service Management Commands:
echo - Start:  sc start "%SERVICE_NAME%"
echo - Stop:   sc stop "%SERVICE_NAME%"
echo - Status: sc query "%SERVICE_NAME%"
echo - Remove: sc delete "%SERVICE_NAME%"
echo.
echo The service will automatically start when Windows boots (with delay).
echo.

pause
