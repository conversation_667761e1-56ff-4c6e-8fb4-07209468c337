@echo off
REM PayslipSender Quick Setup Script
REM Run this as Administrator for best results

echo ============================================================
echo PayslipSender Quick Setup
echo ============================================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8+ from https://python.org
    echo Make sure to check "Add Python to PATH" during installation
    pause
    exit /b 1
)

echo ✅ Python found
python --version

REM Install dependencies
echo.
echo 📦 Installing dependencies...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)

echo ✅ Dependencies installed successfully

REM Create configuration file if it doesn't exist
if not exist .env (
    echo.
    echo 📝 Creating configuration file...
    copy .env.example .env
    echo ✅ Configuration file created: .env
    echo ⚠️  IMPORTANT: Edit .env file with your database and email settings
) else (
    echo ✅ Configuration file already exists: .env
)

REM Create directories
echo.
echo 📁 Creating directories...
if not exist payslips mkdir payslips
if not exist archived_payslips mkdir archived_payslips
if not exist logs mkdir logs
echo ✅ Directories created

REM Test basic functionality
echo.
echo 🧪 Testing basic functionality...
python -c "import pyodbc, dotenv, watchdog; print('✅ All modules imported successfully')"
if %errorlevel% neq 0 (
    echo ERROR: Module import failed
    pause
    exit /b 1
)

echo.
echo ============================================================
echo Setup Complete! 🎉
echo ============================================================
echo.
echo Next steps:
echo 1. Edit .env file with your database and email settings
echo 2. Test with: python payslip_sender.py
echo 3. Create executable with: python create_exe.py
echo.
echo For detailed instructions, see README.md
echo ============================================================
pause
