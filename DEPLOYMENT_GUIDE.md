# PayslipSender - HR Department Deployment Guide

## Overview
PayslipSender is a production-ready Windows service that automatically processes payslip PDF files and emails them to employees using your existing HR database.

## System Requirements
- Windows 10/11 or Windows Server 2016+
- Python 3.8 or higher
- SQL Server with existing employee database
- Gmail account with App Password for SMTP
- NSSM (Non-Sucking Service Manager) for Windows service deployment

## Quick Start

### 1. Install Python Dependencies
```bash
pip install -r requirements.txt
```

### 2. Configure Database Connection
Edit the `.env` file with your database details:
```env
DATABASE_SERVER=your_sql_server_name
DATABASE_NAME=your_database_name
DATABASE_USERNAME=your_username
DATABASE_PASSWORD=your_password
```

### 3. Test the Application
```bash
python payslip_sender.py
```

### 4. Deploy as Windows Service
```bash
nssm install DSCPayslipSender "C:\Python\python.exe" "C:\Path\To\payslip_sender.py"
nssm start DSCPayslipSender
```

## Detailed Configuration

### Database Configuration
The system connects to your existing employee database. Configure these settings in `.env`:

```env
# Database Connection
DATABASE_SERVER=localhost                    # Your SQL Server name/IP
DATABASE_PORT=1433                          # SQL Server port
DATABASE_NAME=dscpayrollemailsender         # Your database name
DATABASE_USERNAME=sa                        # Database username
DATABASE_PASSWORD=your_password             # Database password
DATABASE_TRUSTED_CONNECTION=no              # Use 'yes' for Windows Auth

# Employee Table Schema
EMPLOYEE_TABLE_NAME=Employees               # Your employee table name
EMPLOYEE_ID_COLUMN=EmployeeID              # Employee ID column name
EMPLOYEE_NAME_COLUMN=Name                  # Employee name column name
EMPLOYEE_EMAIL_COLUMN=EmployeeEmail        # Employee email column name
```

### Email Configuration
Configure Gmail SMTP settings:

```env
# Email Settings
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_gmail_app_password      # Use App Password, not regular password
SMTP_USE_TLS=True
```

**Important**: Use Gmail App Password, not your regular Gmail password.

### File Processing Configuration
```env
# File Directories
WATCH_DIRECTORY=./payslips                 # Where to watch for new payslips
ARCHIVED_DIRECTORY=./archived_payslips     # Where to move processed files
SCAN_INTERVAL_SECONDS=30                   # How often to check for new files

# Logging
LOG_LEVEL=INFO                             # DEBUG, INFO, WARNING, ERROR
LOG_FILE=logs/payslip_sender.log          # Log file location
LOG_TO_CONSOLE=false                       # Set to true for console output
```

## File Naming Convention
Payslip files must follow this exact format:
```
Payslip_EmployeeID_YYYYMMDD.pdf
```

Examples:
- `Payslip_501_20250125.pdf` - Employee 501, January 25, 2025
- `Payslip_1234_20250630.pdf` - Employee 1234, June 30, 2025

## Windows Service Deployment

### Install NSSM
1. Download NSSM from https://nssm.cc/download
2. Extract to `C:\nssm` and add to PATH

### Install Service (Run as Administrator)
```batch
# Install the service
nssm install DSCPayslipSender "C:\Python\python.exe" "C:\Path\To\PayslipSender\payslip_sender.py"

# Configure service
nssm set DSCPayslipSender AppDirectory "C:\Path\To\PayslipSender"
nssm set DSCPayslipSender DisplayName "DSC Payslip Sender Service"
nssm set DSCPayslipSender Description "Automatically processes and emails payslip PDFs to employees"
nssm set DSCPayslipSender Start SERVICE_AUTO_START

# Start the service
nssm start DSCPayslipSender
```

### Service Management
```batch
# Check service status
sc query DSCPayslipSender

# Start service
nssm start DSCPayslipSender

# Stop service
nssm stop DSCPayslipSender

# Restart service
nssm restart DSCPayslipSender

# Remove service
nssm remove DSCPayslipSender confirm
```

## Monitoring and Troubleshooting

### Log Files
- **Application Log**: `logs/payslip_sender.log`
- **Service Errors**: Check Windows Event Viewer

### Common Issues

#### 1. Database Connection Failed
- Verify SQL Server is running
- Check server name/IP address
- Verify database name and credentials
- Ensure SQL Server allows remote connections

#### 2. Email Authentication Failed
- Use Gmail App Password, not regular password
- Enable 2-factor authentication on Gmail
- Verify SMTP settings

#### 3. Files Not Processing
- Check filename format: `Payslip_EmployeeID_YYYYMMDD.pdf`
- Verify employee exists in database
- Check logs for specific errors

#### 4. Service Won't Start
```batch
# Check Python path
where python

# Reinstall with full paths
nssm remove DSCPayslipSender confirm
nssm install DSCPayslipSender "FULL_PYTHON_PATH" "FULL_SCRIPT_PATH"
```

### Diagnostic Commands
```bash
# Test database connection
python -c "from payslip_sender import PayslipSender; ps = PayslipSender(); print('Connection OK')"

# View recent logs
type logs\payslip_sender.log | findstr /C:"ERROR" /C:"WARNING"

# Check service status
sc query DSCPayslipSender
```

## Production Checklist

### Before Deployment
- [ ] Python 3.8+ installed
- [ ] All dependencies installed (`pip install -r requirements.txt`)
- [ ] `.env` file configured with correct database settings
- [ ] Gmail App Password created and configured
- [ ] Database connection tested
- [ ] Employee table accessible with correct schema
- [ ] Test payslip file processed successfully

### After Deployment
- [ ] Windows service installed and running
- [ ] Service starts automatically on boot
- [ ] Log files being created
- [ ] Test payslip processed and emailed
- [ ] Failed logs table created in database
- [ ] Monitoring alerts configured (optional)

## File Structure
```
PayslipSender/
├── payslip_sender.py          # Main application
├── email_service.py           # Email functionality
├── requirements.txt           # Python dependencies
├── .env                       # Configuration file
├── DEPLOYMENT_GUIDE.md        # This guide
├── payslips/                  # Drop new payslips here
├── archived_payslips/         # Processed files moved here
└── logs/                      # Application logs
    └── payslip_sender.log
```

## Support
For technical support, check:
1. Application logs: `logs/payslip_sender.log`
2. Windows Event Viewer for service errors
3. Database connection using SQL Server Management Studio
4. Email settings using a test email client

## Security Notes
- Store `.env` file securely with appropriate file permissions
- Use dedicated service account for database access
- Regularly rotate Gmail App Password
- Monitor log files for unauthorized access attempts
- Keep Python and dependencies updated
