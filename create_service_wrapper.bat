@echo off
REM ============================================================================
REM Create PayslipSender Service Wrapper and Register Service
REM ============================================================================
REM This script creates a lightweight service wrapper to avoid Error 1053
REM and registers it as a Windows service
REM Run as Administrator
REM ============================================================================

echo.
echo ========================================
echo PayslipSender Service Wrapper Setup
echo ========================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script must be run as Administrator!
    echo Right-click and select "Run as administrator"
    echo.
    pause
    exit /b 1
)

REM Set variables
set SERVICE_NAME=DSCPayslipSender
set SERVICE_DISPLAY_NAME=DSC PayslipSender Service
set CURRENT_DIR=%~dp0

echo Current Directory: %CURRENT_DIR%
echo.

REM Check if main executable exists
if not exist "%CURRENT_DIR%dist\PayslipSender.exe" (
    echo ERROR: PayslipSender.exe not found at: %CURRENT_DIR%dist\PayslipSender.exe
    echo Please run 'python create_exe.py' first to create the main executable
    echo.
    pause
    exit /b 1
)

REM Check if wrapper script exists
if not exist "%CURRENT_DIR%payslip_service_wrapper.py" (
    echo ERROR: payslip_service_wrapper.py not found
    echo This file should be in the same directory as this batch file
    echo.
    pause
    exit /b 1
)

echo Creating service wrapper executable...

REM Create wrapper executable using PyInstaller
python -m PyInstaller --onefile --name PayslipServiceWrapper payslip_service_wrapper.py

if %errorLevel% neq 0 (
    echo ERROR: Failed to create wrapper executable
    echo Make sure PyInstaller is installed: pip install pyinstaller
    echo.
    pause
    exit /b 1
)

REM Check if wrapper executable was created
if not exist "%CURRENT_DIR%dist\PayslipServiceWrapper.exe" (
    echo ERROR: Wrapper executable not created at: %CURRENT_DIR%dist\PayslipServiceWrapper.exe
    echo.
    pause
    exit /b 1
)

echo ✅ Service wrapper executable created successfully
echo.

REM Remove existing service if it exists
echo Checking for existing service...
sc query "%SERVICE_NAME%" >nul 2>&1
if %errorLevel% equ 0 (
    echo Existing service found. Stopping and removing...
    sc stop "%SERVICE_NAME%" >nul 2>&1
    timeout /t 5 /nobreak >nul
    sc delete "%SERVICE_NAME%" >nul 2>&1
    timeout /t 3 /nobreak >nul
    echo Existing service removed.
)

REM Create logs directory
if not exist "%CURRENT_DIR%logs" (
    mkdir "%CURRENT_DIR%logs"
    echo Created logs directory
)

echo.
echo Registering PayslipSender service with wrapper...

REM Register the service using the wrapper executable
sc create "%SERVICE_NAME%" ^
    binPath= "\"%CURRENT_DIR%dist\PayslipServiceWrapper.exe\" --start" ^
    DisplayName= "%SERVICE_DISPLAY_NAME%" ^
    start= auto ^
    type= own

if %errorLevel% neq 0 (
    echo ERROR: Failed to create service
    pause
    exit /b 1
)

REM Set service description
sc description "%SERVICE_NAME%" "Automated payslip email sender for DSC Human Resources (with service wrapper to prevent startup timeouts)"

REM Configure service to restart on failure
sc failure "%SERVICE_NAME%" reset= 86400 actions= restart/30000/restart/60000/restart/120000

echo.
echo ✅ Service registered successfully with wrapper!
echo.
echo Service Details:
echo - Name: %SERVICE_NAME%
echo - Display Name: %SERVICE_DISPLAY_NAME%
echo - Wrapper: %CURRENT_DIR%dist\PayslipServiceWrapper.exe
echo - Main App: %CURRENT_DIR%dist\PayslipSender.exe
echo - Startup: Automatic
echo - Recovery: Restart on failure
echo.

REM Ask if user wants to start the service now
set /p START_NOW="Do you want to start the service now? (Y/N): "
if /i "%START_NOW%"=="Y" (
    echo.
    echo Starting PayslipSender service...
    sc start "%SERVICE_NAME%"
    
    if %errorLevel% equ 0 (
        echo.
        echo ✅ Service started successfully!
        echo.
        echo The service wrapper should start quickly and avoid Error 1053.
        echo The wrapper will then launch the main PayslipSender application.
        echo.
        echo Monitor the service:
        echo - Service status: sc query "%SERVICE_NAME%"
        echo - Application logs: %CURRENT_DIR%logs\payslip_sender.log
        echo - Windows Event Viewer: Services section
        echo.
    ) else (
        echo.
        echo ❌ Failed to start service
        echo Check Windows Event Viewer for details
        echo Try starting manually: sc start "%SERVICE_NAME%"
    )
) else (
    echo.
    echo Service registered but not started.
    echo To start: sc start "%SERVICE_NAME%"
)

echo.
echo ========================================
echo Setup Complete!
echo ========================================
echo.
echo How it works:
echo 1. Windows starts PayslipServiceWrapper.exe (fast startup)
echo 2. Wrapper launches PayslipSender.exe (main application)
echo 3. Wrapper monitors and restarts main app if it crashes
echo.
echo Service Management Commands:
echo - Start:  sc start "%SERVICE_NAME%"
echo - Stop:   sc stop "%SERVICE_NAME%"
echo - Status: sc query "%SERVICE_NAME%"
echo - Remove: sc delete "%SERVICE_NAME%"
echo.

pause
